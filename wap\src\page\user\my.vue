<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="设置" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>
        <div class="menu">
            <div class="item" @click="$router.push({ path: '/resetpass' })">
                <div>修改登录密码</div>
                <div>
                    <van-icon name="arrow" />
                </div>
            </div>
            <div class="item" @click="$router.push({ path: '/setPassword' })">
                <div>修改资金密码</div>
                <div>
                    <van-icon name="arrow" />
                </div>
            </div>
        </div>
        <div class="menu">
            <div class="item" @click="handleOutLoginClick">
                <div>退出登录</div>
                <div>
                    <van-icon name="arrow" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { MessageBox } from "mint-ui";

export default {
    mounted() {
        // document.getElementById("app").style.backgroundColor =
        //     "rgba(240, 240, 240, 1)";
    },
    methods: {
        handleOutLoginClick() {
            // 退出登录
            MessageBox.confirm(this.$t("hj149") + "?", this.$t("hj165"), {
                confirmButtonText: this.$t("hj161"),
                cancelButtonText: this.$t("hj106"),
            })
                .then(() => {
                    this.toRegister();
                })
                .catch(() => { });
        },
        async toRegister() {
            // 注销登录
            window.localStorage.removeItem("USERTOKEN"); // 清空本地存储 USERTOKEN字段
            this.clearCookie();
            let data = await api.logout();
            if (data.status === 0) {
                // Toast(data.msg)
                this.$router.push("/login");
            } else {
                Toast(data.msg);
            }
            this.$router.push("/login");
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .menu {
        background: #fff;
        margin-top: 0.3488rem;

        .item {
            display: flex;
            justify-content: space-between;
            padding: 0.3488rem;
            border-bottom: solid 1px #eee;

            &:last-of-type {
                border: none;
            }
        }
    }
}
</style>