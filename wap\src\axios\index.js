import axios from 'axios' // 引入 axios
import qs from 'qs' // 引入 qs
import APIUrl from './api.url' // 引入api.url.js

import { Toast } from 'mint-ui'
import store from '@/store'
import router from '@/router'

import { encrypt, decrypt } from '@/utils/aesUtil'

// 根据环境设置基础地址
// const isProduction = process.env.NODE_ENV === "production";
// const defaultBaseURL = !isProduction  ? process.env.VUE_APP_BASE_URL  : APIUrl.baseURL;
axios.defaults.baseURL = APIUrl.baseURL

// 超时时间和默认配置
axios.defaults.timeout = 50000
// axios.defaults.responseType = 'json'
// axios.defaults.headers.common['Content-Type'] =
// 	'application/json;charset=UTF-8'
axios.defaults.withCredentials = true

// 动态设置 baseURL（仅生产环境动态加载）
// if (!isProduction) {
//   (async () => {
//     try {
//       const connectionPoolUrl = 'https://csdimg.wtqbbjs.com/caida_API.json';
//       const { data } = await axios.get(connectionPoolUrl,{withCredentials: false  });
//       if (data && data.url && Array.isArray(data.url)) {
//         const results = await Promise.all(
//           data.url.map(async (url) => {
//             const startTime = performance.now();
//             try {
//               await axios.get(`${url}/health-check`, { timeout: 5000,  withCredentials: false }); // 添加超时
//               const endTime = performance.now();
//               return { url, time: endTime - startTime };
//             } catch (error) { // 补全 catch 的 error 参数
//               console.error(`Health-check failed for ${url}:`, error.message); // 日志记录失败原因
//               return { url, time: Infinity };
//             }
//           })
//         );

//         const fastestUrl = results.reduce((prev, curr) => (curr.time < prev.time ? curr : prev), { time: Infinity }).url;

//         if (fastestUrl && fastestUrl !== Infinity) {
//           axios.defaults.baseURL = fastestUrl;
//           store.commit('SET_BASE_URL', fastestUrl); // 假设你使用 Vuex
//           console.log('Production: Fastest baseURL set to', fastestUrl);
//         } else {
//           console.warn('No valid baseURL found from the connection pool.');
//         }
//       }
//     } catch (error) {
//       console.error('Failed to dynamically set baseURL:', error.message);
//     }

//   })();
// }

// 请求拦截器
axios.interceptors.request.use((config) => {
	config.headers['lang'] = localStorage.getItem('language') || 'zh-CN'
	const token = localStorage.getItem('USERTOKEN')
	if (token) {
		config.headers['USERTOKEN'] = token
	}
	config.headers['Content-Type'] = 'application/json'
	return config
}, (error) => Promise.reject(error))

// 响应拦截器
axios.interceptors.response.use((response) => {
	if (typeof response.data === 'object') {
		response.data = response.data
	} else {
		try {
			response.data = JSON.parse(response.data)
		} catch (error) {
			response.data = decrypt(response.data)
			response.data = JSON.parse(response.data)
		}
	}
	console.log('response', response.config.url, decrypt(response.config.data), response.data)

	if (response.data.msg && response.data.msg.includes('请先登录')) {
		Toast('您还未登录，请先登录')
		localStorage.removeItem('USERTOKEN')
		setTimeout(() => {
			router.replace({ path: '/login' })
		}, 1000)
	}
	return response
}, (error) => Promise.reject(error))

export default axios

/**
 * post 方法封装
 * @param url
 * @param data
 * @returns {Promise}
 */
export function post(url, data = {}, out) {
	// console.log('data', data);
	data = encrypt(data)
	return new Promise((resolve, reject) => {
		axios.post(url, data).then(
			(response) => {
				resolve(response.data)
			},
			(err) => {
				console.log('err', err)
				reject(err)
			}
		)
	})
}

export function postImg(url, data = {}, config = {}) {
	console.log('postImg', url, data)
	return new Promise((resolve, reject) => {
		axios
			.post(url, data, {
				headers: {
					'Content-Type': 'multipart/form-data'
				},
				...config
			})
			.then((response) => {
				console.log('response', response)
				resolve(response.data)
			})
			.catch((err) => {
				console.log('err', err)
				reject(err)
			})
	})
}

/**
 * get 方法封装
 * @param url
 * @param data
 * @returns {Promise}
 */
export function get(url, data = {}) {
	console.log('get', url, data)
	return new Promise((resolve, reject) => {
		axios.get(url, { params: data }).then(
			(response) => {
				// console.log('response', response)
				resolve(response.data)
			},
			(err) => {
				console.log('err', err)
				reject(err)
			}
		)
	})
}

/**
 * 其他delete等的封装类似
 * 可以查看中文文档 自行封装
 */