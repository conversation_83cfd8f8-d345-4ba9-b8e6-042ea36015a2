<template>
    <div class="container">
        <div class="search_layout">
            <div class="search">
                <div class="search_icon">
                    <svg t="1742902499156" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4638" width="64" height="64">
                        <path d="M653.54 707.45C498.1 833.17 270.18 809.07 144.46 653.63 18.84 498.32 42.96 270.17 198.28 144.55 353.72 18.84 581.64 42.93 707.36 198.37c107.37 132.76 107.37 322.5 0 455.26l241.48 241.35c14.87 14.86 14.88 38.96 0.03 53.84-14.87 14.89-38.99 14.9-53.87 0.02L653.54 707.45zM425.8 139.62c-157.93 0-285.96 128.03-285.96 285.96 0 157.93 128.03 285.96 285.96 285.96 157.84 0 285.84-127.89 285.96-285.74-0.08-157.92-128.02-285.97-285.96-286.18z" p-id="4639"></path>
                    </svg>
                </div>
                <input placeholder="请输入股票代码/名称" v-model="keyWords" @input="listArr = [];getStock();loading = true">
            </div>
            <div class="back" @click="$router.go(-1)">取消</div>
        </div>
        <div class="data" v-if="keyWords">
            <div class="data_title">
                <div class="w_3">股票名称</div>
                <div class="center">最新价</div>
                <div>涨跌幅</div>
            </div>
            <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="500" :immediate-check="false">
                <div v-for="(value, index) in listArr" :key="index" @click="goDetail(value)">
                    <div class="stock">
                        <div class="w_3">
                            <div class="stock_name">{{value.name}}</div>
                            <div style="display: flex; align-items: center;">
                                <div class="tag" v-if="value.stock_type == 'sz'">深</div>
                                <div class="tag" v-if="value.stock_type == 'sh'">沪</div>
                                <div class="tag" v-if="value.stock_type == 'bj'">北</div>
                                <span style="margin-left: 0.1162rem; color: rgba(181, 181, 181, 1);">{{value.code}}</span>
                            </div>
                        </div>
                        <div :class="`text_center ${value.hcrate > 0 ? 'red' : 'green'}`">{{value.nowPrice}}</div>
                        <div :class="`text_right ${value.hcrate > 0 ? 'red' : 'green'}`">{{value.hcrate}}%</div>
                    </div>
                </div>
            </van-list>
        </div>
    </div>
</template>

<script>
import { Toast } from "vant";
import { MessageBox } from "mint-ui";
import handleDt from "@/utils/deTh";
import * as api from "@/axios/api";
export default {
    props: {},
    data() {
        return {
            keyWords: "",
            pageNum: 1,
            loading: false,
            finished: false,
            listArr: [],
        };
    },
    mounted() {},
    watch: {
        keyWords(val) {
            if (val == "") {
                this.listArr = [];
            }
        },
    },
    methods: {
        //进入详情
        goDetail(item) {
            //   if (this.userData.length == 0) {

            //     return;
            //   }

            var codes = "";
            var names = "";
            var stock_type = "";
            var soks = "";
            var if_zhishu = "0";
            var if_us = "";
            codes = item.code;
            names = item.name;
            stock_type = item.gid.substring(0, 2);
            soks = this.filterSH(item.gid);
            if_zhishu = "0";
            if_us = item.stock_type == "us" ? "1" : "";
            this.$router.push({
                path: "/kline",
                query: {
                    name: names,
                    stockplate: item.stock_plate,
                    code: codes,
                    type: stock_type,
                    sok: soks,
                    if_us: if_us,
                    usType: item.type,
                    if_zhishu: if_zhishu,
                },
            });
        },
        filterSH(val) {
            if (val.indexOf("sh") >= 0) {
                return 1;
            } else if (val.indexOf("bj") >= 0 || val.indexOf("sz") >= 0) {
                return 0;
            }
        },
        onLoad() {
            // 异步更新数据
            // setTimeout 仅做示例，真实场景中一般为 ajax 请求
            // setTimeout(() => {
            //     for (let i = 0; i < 10; i++) {
            //         this.list.push(this.list.length + 1);
            //     }

            //     // 加载状态结束
            //     this.loading = false;

            //     // 数据全部加载完成
            //     if (this.list.length >= 40) {
            //         this.finished = true;
            //     }
            // }, 1000);
            this.loading = true;
            this.pageNum++;
            this.getStock();
        },
        getStock: handleDt.debounce(async function () {
            // this.listArr = []
            let opt = {
                pageNum: this.pageNum,
                pageSize: 15,
                keyWords: this.keyWords,
            };
            let data = await api.getStock(opt);
            if (data.status == 0) {
                this.loading = false;
                if (data.data.list.length == 0) {
                    this.finished = true;
                    return;
                } else if (
                    data.data.list.length > 0 &&
                    data.data.list.length < 15
                ) {
                    this.finished = true;
                    data.data.list.forEach((element) => {
                        this.listArr.push(element);
                    });
                } else {
                    data.data.list.forEach((element) => {
                        this.listArr.push(element);
                    });
                }
            }
        }, 2000),
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    background: #fff;
    min-height: 100%;
    .search_layout {
        padding: 0.3488rem;
        display: flex;
        .search {
            flex: 1;
            border: solid 1px rgba(224, 57, 54, 1);
            // margin: 0.3488rem;
            margin-top: 0;
            height: 0.7441rem;
            border-radius: 0.2325rem;
            overflow: hidden;
            padding: 0 0.3488rem;
            display: flex;
            align-items: center;
            .search_icon {
                svg {
                    width: 0.3488rem;
                    height: 0.3488rem;
                    fill: rgba(181, 181, 181, 1);
                }
            }
            input {
                margin-left: 0.3488rem;
                flex: 1;
            }
            span {
                color: rgba(181, 181, 181, 1);
                margin-left: 0.3488rem;
            }
        }
        .back {
            line-height: 0.7441rem;
            margin-left: 0.3488rem;
        }
    }
    .data {
        .data_title {
            display: flex;
            padding: 0.3488rem;
            color: rgba(181, 181, 181, 1);
            .center {
                flex: 1;
                text-align: center;
            }
        }
        .w_3 {
            width: 3.7209rem;
        }
        .stock {
            padding: 0.3488rem;
            display: flex;
            border-bottom: solid 1px rgba(245, 247, 250, 1);
            align-items: center;
            .stock_name {
                font-size: 0.372rem;
                margin-bottom: 0.2325rem;
            }
            .tag {
                background: rgba(255, 141, 26, 1);
                font-size: 0.2791rem;
                color: #fff;
                width: 0.4651rem;
                height: 0.4651rem;
                line-height: 0.4651rem;
                border-radius: 0.1162rem;
                text-align: center;
            }
            .text_right {
                font-size: 0.372rem;
            }
            .text_center {
                flex: 1;
                text-align: center;
                font-size: 0.372rem;
            }
        }
    }
    // .data {
    //     table {
    //         width: 100%;
    //     }
    //     th {
    //         color: rgba(181, 181, 181, 1);
    //     }
    //     .text_center {
    //         text-align: center;
    //     }
    //     .text_right {
    //         text-align: right;
    //     }
    // }
}
</style>