webpackJsonp([20],{CxV0:function(t,s,e){"use strict";Object.defineProperty(s,"__esModule",{value:!0});var n=e("//Fk"),a=e.n(n),r=e("Xxa5"),i=e.n(r),c=e("exGp"),o=e.n(c),l=e("Gu7T"),u=e.n(l),d=e("eOoE"),v=e("Fd2+"),p=e("/5sW"),f=e("rAph"),h={name:"SpeedTest",data:function(){return{loading:!1,connectionStatus:"已连接",connectionClass:"status-connected",currentBaseUrl:"",speedTestResults:[]}},computed:{apiSpeedTestResults:function(){return this.$apiSpeedTestResults||[]}},watch:{apiSpeedTestResults:{handler:function(t){t&&t.length>0&&(this.speedTestResults=[].concat(u()(t)),this.updateConnectionStatus())},deep:!0}},mounted:function(){this.currentBaseUrl=this.$currentBaseUrl||d.a.defaults.baseURL||"未知服务器",this.$apiSpeedTestResults&&this.$apiSpeedTestResults.length>0?(this.speedTestResults=[].concat(u()(this.$apiSpeedTestResults)),this.updateConnectionStatus()):this.refreshTest()},methods:{refreshTest:function(){var t=this;return o()(i.a.mark(function s(){var e,n,r,c,l,u;return i.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(t.loading=!0,s.prev=1,0!==(e=t.$apiUrls||f.a.API_URLS||[]).length){s.next=7;break}return v.k.fail("无可用服务器地址"),t.loading=!1,s.abrupt("return");case 7:return n=[],r=null,c=1/0,l=e.map(function(){var s=o()(i.a.mark(function s(e){var a,o,l,u,v,p,h;return i.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return a=performance.now(),s.prev=1,s.next=4,d.a.get(""+e+(t.$apiHealthCheckPath||f.a.HEALTH_CHECK_PATH),{timeout:2e4,withCredentials:!1});case 4:o=s.sent,l=performance.now(),u=l-a,v=o.data&&o.data.data===(t.$apiHealthCheckResponse||f.a.HEALTH_CHECK_RESPONSE),p={url:e,responseTime:u,status:v?"可用":"不可用",isValid:v},n.push(p),v&&u<c&&(r=e,c=u),s.next=17;break;case 13:s.prev=13,s.t0=s.catch(1),h=performance.now(),n.push({url:e,responseTime:h-a,status:"不可用",error:s.t0.message,isValid:!1});case 17:case"end":return s.stop()}},s,t,[[1,13]])}));return function(t){return s.apply(this,arguments)}}()),s.next=13,a.a.all(l);case 13:u=n.sort(function(t,s){return t.responseTime-s.responseTime}),t.speedTestResults=u,p.default.prototype.$apiSpeedTestResults=u,t.$apiSpeedTestResults=u,t.updateConnectionStatus(),r&&r!==t.currentBaseUrl?v.b.confirm({title:"发现更快的服务器",message:"已发现响应更快的服务器："+t.formatUrl(r)+"，是否切换？",confirmButtonText:"切换",cancelButtonText:"保持当前",confirmButtonColor:"#f6020c"}).then(function(){t.setAsDefault(r)}).catch(function(){v.k.success("测速完成")}):v.k.success("测速完成"),s.next=25;break;case 21:s.prev=21,s.t0=s.catch(1),console.error("测速失败:",s.t0),v.k.fail("测速失败，请稍后重试");case 25:return s.prev=25,t.loading=!1,s.finish(25);case 28:case"end":return s.stop()}},s,t,[[1,21,25,28]])}))()},updateConnectionStatus:function(){var t=this,s=this.speedTestResults.find(function(s){return s.url===t.currentBaseUrl});if(!s)return this.connectionStatus="未知状态",void(this.connectionClass="status-unknown");s.isValid?s.responseTime<200?(this.connectionStatus="极速连接",this.connectionClass="status-excellent"):s.responseTime<500?(this.connectionStatus="良好连接",this.connectionClass="status-good"):s.responseTime<1e3?(this.connectionStatus="正常连接",this.connectionClass="status-normal"):(this.connectionStatus="缓慢连接",this.connectionClass="status-slow"):(this.connectionStatus="连接异常",this.connectionClass="status-error")},setAsDefault:function(t){var s=this;t&&v.b.confirm({title:"设置默认服务器",message:"确定要将 "+this.formatUrl(t)+" 设置为默认服务器吗？",confirmButtonText:"确定",cancelButtonText:"取消",confirmButtonColor:"#f6020c"}).then(function(){try{d.a.defaults.baseURL=t,p.default.prototype.$currentBaseUrl=t,"undefined"!=typeof window&&(window.$currentBaseUrl=t),s.currentBaseUrl=t,s.updateConnectionStatus(),v.k.success("服务器已切换")}catch(t){console.error("设置服务器失败:",t),v.k.fail("设置失败，请重试")}}).catch(function(){})},formatUrl:function(t){if(!t)return"未知";try{return new URL(t).hostname}catch(s){return t.replace(/^https?:\/\//,"").split("/")[0]}}}},_={render:function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"speed-test-container"},[e("div",{staticClass:"header"},[e("van-nav-bar",{attrs:{title:"网络测速","left-arrow":""},on:{"click-left":function(s){return t.$router.go(-1)}},scopedSlots:t._u([{key:"left",fn:function(){return[e("van-icon",{attrs:{name:"arrow-left",size:"20",color:"#333"}})]},proxy:!0},{key:"title",fn:function(){return[e("span",{staticClass:"nav-title"},[t._v("网络连接测速")])]},proxy:!0}])})],1),t._v(" "),e("div",{staticClass:"content-wrapper"},[e("div",{staticClass:"server-card"},[e("div",{staticClass:"card-header"},[e("div",{staticClass:"card-title"},[e("van-icon",{attrs:{name:"setting-o"}}),t._v(" "),e("span",[t._v("当前服务器")])],1),t._v(" "),e("div",{staticClass:"refresh-btn",on:{click:t.refreshTest}},[e("van-icon",{attrs:{name:"replay"}}),t._v(" "),e("span",[t._v("重新测试")])],1)]),t._v(" "),e("div",{staticClass:"server-info"},[e("div",{staticClass:"server-url"},[t._v(t._s(t.currentBaseUrl||"正在加载..."))]),t._v(" "),e("div",{staticClass:"connection-status",class:t.connectionClass},[e("div",{staticClass:"status-dot"}),t._v(" "),e("span",[t._v(t._s(t.connectionStatus))])])])]),t._v(" "),e("div",{staticClass:"servers-list-card"},[e("div",{staticClass:"card-header"},[e("div",{staticClass:"card-title"},[e("van-icon",{attrs:{name:"cluster-o"}}),t._v(" "),e("span",[t._v("全部服务器")])],1),t._v(" "),e("div",{staticClass:"servers-count"},[t._v("共 "+t._s(t.speedTestResults.length)+" 个")])]),t._v(" "),e("div",{staticClass:"servers-table"},[e("div",{staticClass:"mobile-server-list"},[t.loading?e("div",{staticClass:"loading-state"},[e("van-loading",{attrs:{type:"spinner",color:"#f6020c"}}),t._v(" "),e("span",[t._v("正在测速中...")])],1):t._l(t.speedTestResults,function(s,n){return e("div",{key:n,staticClass:"server-item",class:[s.url===t.currentBaseUrl?"current-server":"",s.isValid?"":"invalid-server"]},[e("div",{staticClass:"server-item-rank"},[e("span",{staticClass:"rank-number"},[t._v(t._s(n+1))])]),t._v(" "),e("div",{staticClass:"server-item-content"},[e("div",{staticClass:"server-item-header"},[e("div",{staticClass:"server-name"},[t._v(t._s(t.formatUrl(s.url)))]),t._v(" "),e("div",{staticClass:"server-status",class:s.isValid?"status-ok":"status-fail"},[t._v("\n                                        "+t._s(s.status)+"\n                                    ")])]),t._v(" "),e("div",{staticClass:"server-item-details"},[e("div",{staticClass:"response-time"},[e("van-icon",{attrs:{name:"clock-o"}}),t._v(" "),e("span",[t._v(t._s(s.responseTime)+"ms")])],1),t._v(" "),e("div",{staticClass:"server-action"},[e("van-button",{attrs:{type:"primary",size:"small",disabled:s.url===t.currentBaseUrl||!s.isValid},on:{click:function(e){return t.setAsDefault(s.url)}}},[t._v("\n                                            "+t._s(s.url===t.currentBaseUrl?"当前使用":"设为默认")+"\n                                        ")])],1)])])])})],2)])]),t._v(" "),e("div",{staticClass:"speed-info-card"},[e("div",{staticClass:"card-header"},[e("div",{staticClass:"card-title"},[e("van-icon",{attrs:{name:"info-o"}}),t._v(" "),e("span",[t._v("测速说明")])],1)]),t._v(" "),t._m(0)])])])},staticRenderFns:[function(){var t=this.$createElement,s=this._self._c||t;return s("div",{staticClass:"info-content"},[s("p",[this._v("为提供更佳的交易体验，系统会自动选择响应速度最快的服务器。您可以通过本页面查看当前网络环境下各服务器的连接速度。")]),this._v(" "),s("p",[this._v('如果您遇到交易延迟或断连问题，建议点击"重新测试"按钮刷新测速结果。')])])}]};var C=e("VU/8")(h,_,!1,function(t){e("Dqa6")},"data-v-1b047ccb",null);s.default=C.exports},Dqa6:function(t,s){}});