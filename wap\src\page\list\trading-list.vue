<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="行情" fixed @click-right="$router.push({ path: '/Searchlist' })">
                <template #right>
                    <van-icon name="search" />
                </template>
            </van-nav-bar>
        </div>
        <div class="hot">
            <div class="hot_layout">
                <div :class="`hot_item ${value.floatPoint < 0 ? 'greenbg' : 'redbg'}`" v-for="value in hotStockList"
                    :key="value.id">
                    <div class="hot_item_title">{{ value.indexName }}</div>
                    <div class="hot_item_num">{{ value.currentPoint }}</div>
                    <div class="hot_item_sub">
                        <div class="hot_item_sub_left">{{ Number(value.floatPoint).toFixed(2) }}</div>
                        <div class="hot_item_sub_right">{{ value.floatRate }}%</div>
                        <div class="clear"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout">
            <div class="sub_menu">
                <div :class="`sub_menu_item ${itemIndex == 0 ? 'active' : ''}`" @click="changeItemIndex(0)">
                    <div class="sub_menu_item_title">自选</div>
                    <div class="sub_menu_item_bar"></div>
                </div>
                <div :class="`sub_menu_item ${itemIndex == 1 ? 'active' : ''}`" @click="changeItemIndex(1)">
                    <div class="sub_menu_item_title">上交所</div>
                    <div class="sub_menu_item_bar"></div>
                </div>
                <div :class="`sub_menu_item ${itemIndex == 2 ? 'active' : ''}`" @click="changeItemIndex(2)">
                    <div class="sub_menu_item_title">深交所</div>
                    <div class="sub_menu_item_bar"></div>
                </div>
                <div :class="`sub_menu_item ${itemIndex == 3 ? 'active' : ''}`" @click="changeItemIndex(3)">
                    <div class="sub_menu_item_title">科创</div>
                    <div class="sub_menu_item_bar"></div>
                </div>
                <div :class="`sub_menu_item ${itemIndex == 4 ? 'active' : ''}`" @click="changeItemIndex(4)">
                    <div class="sub_menu_item_title">北交所</div>
                    <div class="sub_menu_item_bar"></div>
                </div>
                <div class="clear"></div>
            </div>
            <div class="trade">
                <div class="trade_title">
                    <div class="trade_title_text" style="width: 40%;">股票名称</div>
                    <div class="trade_title_text center" style="width: 30%;">最新价格</div>
                    <div class="trade_title_text center" style="width: 30%;">涨跌幅</div>
                    <div class="clear"></div>
                </div>
                <div class="trade_list">
                    <van-list v-model="loading" :finished="finished" :immediate-check="false"
                        :finished-text="$t('hj43')" @load="getStockList">
                        <div class="trade_list_item" v-for="value in stockList" :key="value.id"
                            @click="goDetail(value)">
                            <div class="trade_list_item_box trade_box" style="width: 40%;">
                                <div class="trade_box_title" v-if="itemIndex == 0">{{ value.stockName }}</div>
                                <div class="trade_box_title" v-else>{{ value.name }}</div>
                                <div class="trade_box_desc" v-if="itemIndex == 0">
                                    <div class="trade_box_tag" v-if="value.stockGid.indexOf('sz') > -1">深</div>
                                    <div class="trade_box_tag" v-if="value.stockGid.indexOf('sh') > -1">沪</div>
                                    <div class="trade_box_tag" v-if="value.stockGid.indexOf('bj') > -1">北</div>
                                    <div class="trade_box_num">{{ value.stockGid }}</div>
                                </div>
                                <div class="trade_box_desc" v-else>
                                    <div class="trade_box_tag" v-if="value.symbol.indexOf('sz') > -1">深</div>
                                    <div class="trade_box_tag" v-if="value.symbol.indexOf('sh') > -1">沪</div>
                                    <div class="trade_box_tag" v-if="value.symbol.indexOf('bj') > -1">北</div>
                                    <div class="trade_box_num">{{ value.symbol }}</div>
                                </div>
                            </div>

                            <div :class="`trade_list_item_box num ${value.hcrate > 0 ? 'red' : 'green'}`"
                                style="width: 30%;" v-if="itemIndex == 0">{{ Number(value.nowPrice).toFixed(2) }}</div>
                            <div :class="`trade_list_item_box num ${value.changepercent > 0 ? 'red' : 'green'}`"
                                style="width: 30%;" v-else>{{ Number(value.buy).toFixed(2) }}</div>

                            <div class="trade_list_item_box" style="width: 30%;" v-if="itemIndex == 0">
                                <div :class="`box ${value.hcrate > 0 ? '' : 'green'}`">{{ value.hcrate }}%</div>
                            </div>
                            <div class="trade_list_item_box" style="width: 30%;" v-else>
                                <div :class="`box ${value.changepercent > 0 ? '' : 'green'}`">{{ value.changepercent }}%
                                </div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </van-list>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import Axios from "axios";
export default {
    data() {
        return {
            itemIndex: 0,
            hotStockList: [],
            stockList: [],
            pageNum: 1,
            finished: false,
            loading: false,
            source: "",
        };
    },
    mounted() {
        this.getListMarket();
        // this.getListMarket();
        // this.getStock();
        // this.getStocks();
        this.changeItemIndex(0);
    },
    methods: {
        changeItemIndex(index) {
            this.itemIndex = index;
            // switch (index) {
            //     case 0:
            //         break;
            // }
            this.pageNum = 1;
            this.finished = false;
            this.stockList = [];
            if (this.itemIndex == 0) {
                this.getMyList();
            } else {
                this.getStockList();
            }
        },
        getMyList() {
            let _this = this;
            _this.loading = true;
            if (_this.source) {
                _this.source.cancel("close request");
            }
            _this.source = Axios.CancelToken.source();
            let opt = {
                pageNum: this.pageNums,
                pageSize: 15,
                keyWords: this.gpcodes,
            };
            api.getMyList(opt, {
                cancelToken: _this.source.token,
            }).then((res) => {
                if (res.data.list.length < 15) {
                    this.finished = true;
                }
                for (let i = 0; i < res.data.list.length; i++) {
                    _this.stockList.push(res.data.list[i]);
                }
                _this.loading = false;
                _this.pageNum++;
            });
            // this.loadings = false;
            // if (data.status == 0) {
            //     data.data.list.forEach((element) => {
            //         this.listArrs.push(element);
            //     });
            // }
            // if (data.data.list.length < 15) {
            //     this.finisheds = true;
            // }
        },
        getStockList() {
            let _this = this;
            let opt = {
                pageNum: this.pageNum,
                pageSize: 20,
                asc: 1,
                // sort: "changepercent",
                node: "hs_a",
                // stockPlate: this.stockPlate,
                // keyWords: this.gpcode,
                // stockType: this.stockType
            };
            if (_this.source) {
                _this.source.cancel("close request");
            }
            _this.source = Axios.CancelToken.source();
            switch (this.itemIndex) {
                case 1:
                    opt.node = "sh_a";
                    break;
                case 2:
                    opt.node = "sz_a";
                    break;
                case 3:
                    opt.node = "kcb";
                    break;
                case 4:
                    opt.node = "hs_bjs";
                    break;
            }
            _this.loading = true;
            api.getStockSort(opt, {
                cancelToken: _this.source.token,
            }).then((res) => {
                if (res.data.length < 15) {
                    this.finished = true;
                }
                for (let i = 0; i < res.data.length; i++) {
                    _this.stockList.push(res.data[i]);
                }
                _this.loading = false;
                _this.pageNum++;
            });
        },
        getListMarket() {
            let val = {
                pageNum: 1,
                pageSize: 15,
            };
            api.getListMarket(val).then((res) => {
                for (let i = 0; i < 3; i++) {
                    this.hotStockList.push(res.data[i]);
                }
            });
        },
        async getStocks() {
            // 沪深降序
            let opt = {
                pageNo: this.pageNum,
                pageSize: 20,
                asc: 1,
                sort: "changepercent",
                node: "hs_a",
                // stockPlate: this.stockPlate,
                // keyWords: this.gpcode,
                // stockType: this.stockType
            };
            let data = await api.getStockSort(opt);
            this.loading = false;
            if (data.status === 0) {
                if (data.data.length < 15) {
                    this.finished = true;
                }

                data.data.forEach((element) => {
                    this.listArrs.push(element);
                });
            } else {
                // this.texts = data.msg;
                // this.alertShow = true;
            }
        },
        goDetail(item) {
            var codes = "";
            var names = "";
            var stock_type = "";
            var soks = "";
            var if_zhishu = "0";
            var if_us = "";
            var symbol = this.itemIndex == 0 ? item.stockGid : item.symbol;
            codes = this.itemIndex == 0 ? item.stockCode : item.code;
            names = item.name;
            stock_type = symbol.substring(0, 2);
            soks = this.filterSH(symbol);
            if_zhishu = "0";
            if_us = item.stock_type == "us" ? "1" : "";
            this.$router.push({
                path: "/kline",
                query: {
                    name: names,
                    stockplate: item.stock_plate,
                    code: codes,
                    type: stock_type,
                    sok: soks,
                    if_us: if_us,
                    usType: item.type,
                    if_zhishu: if_zhishu,
                    symbol: symbol,
                },
            });
        },
        filterSH(val) {
            if (val.indexOf("sh") >= 0) {
                return 1;
            } else if (val.indexOf("bj") >= 0 || val.indexOf("sz") >= 0) {
                return 0;
            }
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    padding-bottom: 1.3488rem;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .hot {
        background: #fff;

        .title {
            font-size: 0.4186rem;
            padding-left: 0.3488rem;
            padding-bottom: 0.3488rem;
            padding-top: 0.3488rem;
        }

        .hot_layout {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 0.3488rem;
            padding: 0.3488rem;

            .hot_item {
                // float: left;
                // background: linear-gradient(to bottom, #ff5656, #fff);
                // border-top-left-radius: 0.2326rem;
                // border-top-right-radius: 0.2326rem;
                // margin-left: 0.3488rem;
                // width: calc((100% - 0.3488rem * 4) / 3);
                padding: 0.3488rem 0;

                &.redbg {
                    background: rgba(224, 57, 54, 0.03);
                }

                &.greenbg {
                    background: rgba(14, 201, 177, 0.03);

                    .hot_item_num {
                        color: green;
                    }

                    .hot_item_sub {
                        color: green;
                    }
                }

                &.bgreen {
                    background: linear-gradient(to bottom, #28d8a3, #fff);

                    .hot_item_num {
                        color: #01573d;
                    }

                    .hot_item_sub {
                        color: #01573d;
                    }
                }

                .hot_item_title {
                    font-size: 0.3721rem;
                    text-align: center;
                }

                .hot_item_num {
                    color: rgb(247, 2, 2);
                    text-align: center;
                    font-size: 0.4186rem;
                    margin: 0.3488rem 0;
                }

                .hot_item_sub {
                    color: rgb(247, 2, 2);
                    font-size: 0.2791rem;

                    .hot_item_sub_left {
                        float: left;
                        margin-left: 0.3488rem;
                    }

                    .hot_item_sub_right {
                        float: right;
                        margin-right: 0.3488rem;
                    }
                }
            }
        }
    }

    .layout {
        background: #fff;
        margin-top: 0.3488rem;

        .sub_menu {
            padding: 0 0.3488rem;
            padding-top: 0.3488rem;
            border-bottom: 1px solid rgba(249, 249, 249, 1);
            display: flex;

            .clear {
                clear: both;
            }

            .sub_menu_item {
                text-align: center;
                font-size: 0.3255rem;
                flex: 1;
                display: flex;
                flex-direction: column;

                &.active {
                    color: rgba(238, 0, 17, 1);

                    .sub_menu_item_bar {
                        display: block;
                    }
                }

                .sub_menu_item_bar {
                    background: rgba(238, 0, 17, 1);
                    // width: 100%;
                    height: 2px;
                    // width: 1.07rem;
                    display: none;
                    margin: auto;
                    margin-top: 0.2326rem;
                }
            }
        }

        .trade {
            // box-shadow: 0 0 10px #0003;
            // border-radius: 0.2326rem;
            margin: 0.3488rem;
            overflow: hidden;
            // padding: 0.3488rem;

            .clear {
                clear: both;
            }

            .trade_title {
                display: flex;

                .trade_title_text {
                    font-size: 0.3255rem;
                    color: rgba(181, 181, 181, 1);
                    flex: 1;
                }
            }

            .center {
                text-align: center;
            }

            .trade_list {
                margin-top: 0.3488rem;

                .trade_list_item {
                    margin-top: 0.3488rem;

                    .trade_list_item_box {
                        float: left;

                        .box {
                            background: red;
                            text-align: center;
                            color: #fff;
                            border-radius: 0.2326rem;
                            height: calc(0.4186rem + 0.3256rem + 0.1163rem);
                            line-height: calc(0.4186rem + 0.3256rem + 0.1163rem);
                            font-size: 0.4186rem;

                            &.green {
                                background: green;
                            }
                        }

                        &.num {
                            text-align: center;
                            line-height: calc(0.4186rem + 0.3256rem + 0.1163rem);
                            font-size: 0.4186rem;
                        }

                        &.green {
                            color: green;
                        }

                        &.red {
                            color: red;
                        }

                        &.trade_box {
                            .trade_box_title {
                                font-size: 0.4186rem;
                            }

                            .trade_box_desc {
                                display: flex;
                                margin-top: 0.1163rem;

                                .trade_box_tag {
                                    background: #6d9cff;
                                    font-size: 0.2791rem;
                                    color: #fff;
                                    width: 0.3256rem;
                                    height: 0.3256rem;
                                    line-height: 0.3256rem;
                                    text-align: center;
                                }

                                .trade_box_num {
                                    color: rgba(125, 125, 125, 1);
                                    margin-left: 0.1163rem;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
