<template>
    <div class="container">
        <div class="header">
            <van-nav-bar v-if="type == 1" title="打新记录" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
            <van-nav-bar v-else title="配售记录" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>
        <div class="menu">
            <div :class="`item ${itemIndex == 0 ? 'active' : ''}`" @click="changeItemIndex(0)">
                <div>
                    <span>申购中</span>
                    <span></span>
                </div>
            </div>
            <div :class="`item ${itemIndex == 1 ? 'active' : ''}`" @click="changeItemIndex(1)">
                <div>
                    <span>中签</span>
                    <span></span>
                </div>
            </div>
            <div :class="`item ${itemIndex == 2 ? 'active' : ''}`" @click="changeItemIndex(2)">
                <div>
                    <span>未中签</span>
                    <span></span>
                </div>
            </div>
        </div>
        <div class="list">
            <template v-for="value in list">
                <div class="item" :key="value.id">
                    <!-- <div class="item_title">
                        <span>{{value.newName}}</span>
                        <span v-if="itemIndex == 0">申购中</span>
                        <span v-if="itemIndex == 1 && value.status == 4" style="background: #ffab0e; color: #fff; width: 1.8604rem; height: 0.6976rem; text-align: center; line-height: 0.6976rem; border-radius: 0.3288rem; font-size: 0.3255rem;">已认缴</span>
                        <span v-if="itemIndex == 1 && value.status == 3" style="background: #ee0011; color: #fff; width: 1.8604rem; height: 0.6976rem; text-align: center; line-height: 0.6976rem; border-radius: 0.3288rem; font-size: 0.3255rem;" @click="getrenjiao(value.id)">认缴</span>
                    </div> -->
                    <div class="flex" style="font-size: 0.3720rem; margin-bottom: 0.3488rem;">
                        <div style="display: flex; align-items: center;">
                            <div class="tag" v-if="value.newType == '深' || value.newType == '创'">深</div>
                            <div class="tag" v-if="value.newType == '沪' || value.newType == '科'">沪</div>
                            <div class="tag" v-if="value.newType == '北'">北</div>
                            <span style="margin-left: 0.1162rem;">{{value.newName}} [{{value.newCode}}]</span>
                        </div>
                        <div>
                            <span v-if="itemIndex == 0">申购中</span>
                            <span v-if="itemIndex == 1 && value.status == 4" style="color: rgba(255, 141, 26, 1); font-size: 0.3255rem;">已认缴</span>
                            <span v-if="itemIndex == 1 && value.status == 3" @click="getrenjiao(value.id)" style="background: #ee0011; color: #fff; height: 0.8372rem; display: block; line-height: 0.8372rem; padding: 0 0.3488rem; border-radius: 0.2325rem; font-size: 0.3255rem;">认缴</span>
                        </div>
                    </div>
                    <div class="grid">
                        <div class="grid_flex">
                            <div class="grid_flex_title">发行价格</div>
                            <div>￥{{value.buyPrice}}</div>
                        </div>
                        <div class="grid_flex">
                            <div class="grid_flex_title">申购代码</div>
                            <div>{{value.newCode}}</div>
                        </div>
                        <div class="grid_flex">
                            <div class="grid_flex_title">数量</div>
                            <div>{{value.applyNumber}}</div>
                        </div>
                        <div class="grid_flex">
                            <div class="grid_flex_title">申购时间</div>
                            <div>{{ value.addTime }}</div>
                        </div>
                    </div>

                    <!-- <div style="display: flex; margin-top: 0.3488rem;">
                        <div style="flex: 1; display: flex; flex-direction: column; align-items: center;">
                            <span>申购代码</span>
                            <span style="margin-top: 0.1162rem;">{{value.newCode}}</span>
                        </div>
                        <div style="flex: 1; display: flex; flex-direction: column; align-items: center;">
                            <span>发行价格</span>
                            <span style="margin-top: 0.1162rem;">{{value.buyPrice}}</span>
                        </div>
                        <div style="flex: 1; display: flex; flex-direction: column; align-items: center;" v-if="itemIndex == 0">
                            <span>数量（股）</span>
                            <span style="margin-top: 0.1162rem;">以中签数量为准</span>
                        </div>
                        <div style="flex: 1; display: flex; flex-direction: column; align-items: center;" v-if="itemIndex == 1">
                            <span>数量（股）</span>
                            <span style="margin-top: 0.1162rem;">{{value.applyNumber}}</span>
                        </div>
                    </div>
                    <div style="display: flex; margin-top: 0.3488rem; justify-content: space-between;">
                        <div style="flex: 1; display: flex; flex-direction: column; align-items: center;">
                            <span>申购时间</span>
                            <span style="margin-top: 0.1162rem;">{{value.addTime}}</span>
                        </div>
                        <div style="flex: 1; display: flex; flex-direction: column; align-items: center;" v-if="itemIndex == 0">
                            <span>申购配号</span>
                            <span style="margin-top: 0.1162rem;">{{value.orderNo}}</span>
                        </div>
                        <div style="flex: 1; display: flex; flex-direction: column; align-items: center; color: #f70202;" v-if="itemIndex == 1 && value.status != 4">
                            <span>剩余缴款</span>
                            <span style="margin-top: 0.1162rem;">{{value.bond}}</span>
                        </div>
                    </div> -->
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { MessageBox, Toast } from "mint-ui";
import Axios from "axios";

export default {
    data() {
        return {
            type: 0,
            itemIndex: 0,
            list: [],
            pageNum: 1,
            finished: false,
            loading: false,
            source: "",
        };
    },
    mounted() {
        if (this.$route.query.type) {
            this.type = this.$route.query.type;
        }
        this.changeItemIndex(0);
    },
    methods: {
        changeItemIndex(index) {
            this.list = [];
            this.itemIndex = index;
            if (!this.type) return;
            let type = 1;
            switch (index) {
                case 0:
                    type = 1;
                    break;
                case 1:
                    type = 3;
                    break;
                case 2:
                    type = 2;
                    break;
            }
            this.getzqjl(type);
        },
        getzqjl(type) {
            let _this = this;
            if (_this.source) {
                _this.source.cancel("close request");
            }
            _this.source = Axios.CancelToken.source();
            api.getzqjl(
                {
                    status: type,
                },
                {
                    cancelToken: _this.source.token,
                }
            ).then((res) => {
                _this.list = res.data;
            });
        },
        getrenjiao(id) {
            let _this = this;
            MessageBox.confirm(this.$t("hj251") + "?", this.$t("hj165"), {
                confirmButtonText: this.$t("hj161"),
                cancelButtonText: this.$t("hj106"),
            })
                .then(() => {
                    let opt = {
                        id: id,
                    };
                    api.submitSubscribe(opt).then((res) => {
                        Toast(res.msg);
                        _this.changeItemIndex(1);
                    });
                })
                .catch(() => {});
        },
    },
};
</script>


<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .menu {
        display: flex;
        padding-top: 0.3488rem;
        background: #fff;
        .item {
            flex: 1;
            display: flex;
            justify-content: center;
            div {
                position: relative;
                padding-bottom: 0.2325rem;
                span:nth-of-type(2) {
                    background: rgba(238, 0, 17, 1);
                    height: 2px;
                    position: absolute;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    display: none;
                }
            }
            &.active {
                color: rgba(238, 0, 17, 1);
                div {
                    span:nth-of-type(2) {
                        display: block;
                    }
                }
            }
        }
    }
    .list {
        background: #fff;
        margin-top: 0.3488rem;
        min-height: calc(100vh - 1.4188rem);
        .item {
            padding: 0.3488rem;
            border-bottom: solid 1px #f1f1f1;
            .flex {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-bottom: 0.3488rem;
                .tag {
                    background: rgba(255, 141, 26, 1);
                    font-size: 0.2791rem;
                    color: #fff;
                    width: 0.4651rem;
                    height: 0.4651rem;
                    line-height: 0.4651rem;
                    border-radius: 0.1162rem;
                    text-align: center;
                }
                .icon {
                    width: 0.3488rem;
                    height: 0.3488rem;
                    fill: rgba(181, 181, 181, 1);
                }
            }
            .grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 0.3488rem;
                .grid_flex {
                    display: flex;
                    .grid_flex_title {
                        color: rgba(181, 181, 181, 1);
                        margin-right: 0.3488rem;
                    }
                }
            }
        }
    }
}
</style>