<template>
    <div class="signature-pad-container">
        <img v-show="signatureUrl" :src="signatureUrl" alt="">
        <canvas v-show="!signatureUrl" ref="canvas" @mousedown="startDrawing" @mousemove="draw" @mouseup="stopDrawing" @mouseleave="stopDrawing" @touchstart="startDrawing" @touchmove="draw" @touchend="stopDrawing"></canvas>
        <div class="buttons" v-if="!signatureUrl">
            <button class="clear-button" @click="clear">清除</button>
            <button class="save-button" :disabled="!isDrawable" @click="save">保存</button>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { Toast } from "mint-ui";
import * as qiniu from "qiniu-js";

export default {
    props: {
        signatureUrl: {
            type: String,
            default: null,
        },
        qiniuDomain: {
            type: String,
            default: null,
        },
        id: {
            type: [String, Number],
            default: "",
        },
    },
    data() {
        return {
            drawing: false,
            context: null,
            isDrawable: false,
        };
    },
    async mounted() {
        this.resizeCanvas();
        window.addEventListener("resize", this.resizeCanvas);
        if (this.signatureUrl) {
            this.loadSignature();
        }
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.resizeCanvas);
    },
    methods: {
        resizeCanvas() {
            const canvas = this.$refs.canvas;
            const container = this.$el;
            canvas.width = container.offsetWidth;
            canvas.height = container.offsetHeight;
            this.context = canvas.getContext("2d");
            this.context.strokeStyle = "black";
            this.context.lineWidth = 2;
            if (this.signatureUrl) {
                this.loadSignature();
            }
        },
        loadSignature() {
            const canvas = this.$refs.canvas;
            const context = this.context;
            const img = new Image();
            img.src = this.signatureUrl;
            img.onload = () => {
                context.drawImage(img, 0, 0, canvas.width, canvas.height);
                this.isDrawable = true;
            };
        },
        getEventPosition(event) {
            const canvas = this.$refs.canvas;
            if (event.touches && event.touches.length > 0) {
                const rect = canvas.getBoundingClientRect();
                return {
                    x: event.touches[0].clientX - rect.left,
                    y: event.touches[0].clientY - rect.top,
                };
            }
            return {
                x: event.offsetX,
                y: event.offsetY,
            };
        },
        startDrawing(event) {
            if (this.signatureUrl) return;
            this.drawing = true;
            const pos = this.getEventPosition(event);
            this.context.beginPath();
            this.context.moveTo(pos.x, pos.y);
            this.isDrawable = true;
        },
        draw(event) {
            if (!this.drawing || this.signatureUrl) return;
            event.preventDefault();
            const pos = this.getEventPosition(event);
            this.context.lineTo(pos.x, pos.y);
            this.context.stroke();
        },
        stopDrawing() {
            if (!this.drawing || this.signatureUrl) return;
            this.drawing = false;
            this.context.closePath();
        },
        clear() {
            if (this.signatureUrl) return;
            const canvas = this.$refs.canvas;
            this.context.clearRect(0, 0, canvas.width, canvas.height);
            this.isDrawable = false;
        },
        async save() {
            if (this.signatureUrl) return;
            const canvas = this.$refs.canvas;
            const dataURL = canvas.toDataURL("image/png");
            const file = this.dataURLtoFile(dataURL, "signature.png");
            this.imgloading = true;
            // var formData = new FormData()
            // formData.append('upload_file',file)
            // formData.append('userId',this.id)
            const url = await this.upqiniu(file);
            if (!url) return;
            const formData = {
                userId: this.id,
                imgUrl: url,
            };
            api.adminupload(formData).then((res) => {
                if (res.status == 0) {
                    this.$emit("getInfo"); // Call the save prop function with the File object
                } else {
                    Toast("签署失败");
                }
                this.imgloading = false;
            });
            // this.onSave(file); // Call the onSave prop function with the File object
        },
        async upqiniu(file) {
            // 获取上传 Token
            const res = await api.getUploadToken();
            if (res.status === 0) {
                const uploadToken = res.data;

                if (!file) {
                    this.$message.error("请选择文件");
                    return;
                }

                // 压缩图片
                const options = {
                    quality: 0.92, // 图片压缩质量
                    noCompressIfLarger: true, // 如果压缩后文件更大，则不压缩
                    // maxWidth: 1000,           // 最大宽度
                    // maxHeight: 618            // 最大高度
                };

                try {
                    const data = await qiniu.compressImage(file, options);

                    // 上传配置
                    const putExtra = {};
                    const config = {
                        region: qiniu.region.z2, // 使用华南区域
                    };

                    const timestamp = Date.now();
                    const fileExtension = file.name.split(".").pop(); // 获取文件扩展名
                    const fileName = `${timestamp}.${fileExtension}`; // 生成新的文件名

                    // 上传压缩后的图片
                    const observable = qiniu.upload(
                        data.dist, // 压缩后的图片文件
                        fileName, // 时间戳命名的文件名
                        uploadToken, // 上传Token
                        putExtra, // 额外参数
                        config // 配置项
                    );
                    const domain = this.qiniuDomain;
                    return new Promise((resolve, reject) => {
                        observable.subscribe({
                            next(res) {
                                console.log(res.total); // 上传过程中会多次触发，可以显示上传进度
                            },
                            error(err) {
                                console.error(err);
                                this.$message.error("文件上传失败");
                                reject(err);
                            },
                            complete(res) {
                                const fileUrl = `${domain}/${res.key}`;
                                resolve(fileUrl);
                            },
                        });
                    });
                } catch (error) {
                    this.$message.error("图片压缩失败");
                    console.error(error);
                }
            } else {
                this.$message.error("获取上传Token失败");
            }
        },
        dataURLtoFile(dataurl, filename) {
            const arr = dataurl.split(",");
            const mime = arr[0].match(/:(.*?);/)[1];
            const bstr = atob(arr[1]);
            let n = bstr.length;
            const u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            return new File([u8arr], filename, { type: mime });
        },
    },
};
</script>

<style scoped>
.signature-pad-container {
    position: relative;
    border: 1px solid #ccc;
    display: inline-block;
    width: 100%;
    height: 300px; /* Set desired height */
}

canvas {
    display: block;
    width: 100%;
    height: 100%;
    background-color: #fff;
}

.buttons {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    padding: 0 10px;
    column-gap: 20px;
    padding-bottom: 20px;
}

button {
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    transition: background-color 0.3s ease;
    flex: 1;
}

.clear-button {
    background-color: #ea4445;
}

.clear-button:hover {
    background-color: #c93a3b;
}

.save-button {
    background-color: #17b780;
}

.save-button:hover {
    background-color: #128d61;
}
</style>
