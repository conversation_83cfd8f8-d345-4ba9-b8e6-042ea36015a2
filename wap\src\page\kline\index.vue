<template>
    <div class="container">
        <div class="header">
            <van-nav-bar left-arrow fixed @click-left="$router.go(-1)" @click-right="$router.push({path: '/Searchlist'})">
                <template #title>
                    <div style="display: flex;" v-if="singDetails && singDetails.gid">
                        <div class="tag" v-if="singDetails.gid.indexOf('sz') > -1">深</div>
                        <div class="tag" v-if="singDetails.gid.indexOf('sh') > -1">沪</div>
                        <div class="tag" v-if="singDetails.gid.indexOf('bj') > -1">北</div>
                        <div style="margin-left: 0.1162rem; ">{{singDetails.name + '[' + singDetails.gid + ']'}}</div>
                    </div>
                </template>
                <template #right>
                    <van-icon name="search" />
                </template>
            </van-nav-bar>
        </div>
        <div class="main">
            <div class="main_top">
                <div :class="`main_top_left ${singDetails.hcrate > 0 ? 'red' : 'green'}`">
                    <div style="margin-left: 0.3488rem;">
                        <div style="text-align: center; font-size: 0.4651rem;">{{ singDetails.nowPrice }}</div>
                        <div class="main_top_left_bottom">
                            <div>{{Number(singDetails.nowPrice - singDetails.preclose_px).toFixed(2)}}</div>
                            <div>{{ singDetails.hcrate }}%</div>
                        </div>
                    </div>
                </div>
                <div class="main_top_right">
                    <div>最高：<span class="red">{{ singDetails.today_max }}</span></div>
                    <div>最低：<span class="green">{{ singDetails.today_min }}</span></div>
                    <div>金额：{{ (Number(singDetails.business_balance) / 100000000).toFixed(2) }} 亿</div>
                    <div>今开：{{ singDetails.open_px }}</div>
                    <div>昨收：{{ singDetails.preclose_px }}</div>
                    <div>成交：<span class="red">{{ (Number(singDetails.business_amount) / 10000).toFixed(2) }} 万</span></div>
                </div>
                <!-- <div :class="`main_top_left ${singDetails.hcrate > 0 ? 'red' : 'green'}`">
                    <span>{{ singDetails.nowPrice }}</span>
                    <font>元</font>
                </div>
                <div :class="`main_top_right ${singDetails.hcrate > 0 ? 'red' : 'green'}`">
                    <span>{{Number(singDetails.nowPrice - singDetails.preclose_px).toFixed(2)}}</span>
                    <span>{{ singDetails.hcrate > 0 ? '+' : '' }}{{ singDetails.hcrate }}%</span>
                </div> -->
            </div>
            <div class="line"></div>
            <div class="ebox">
                <Kline :type="singDetails.type" style="height: 8.1395rem;" />
                <!-- <div class="echart_info">
                    <div>最高：{{ singDetails.today_max }}</div>
                    <div>最低：{{ singDetails.today_min }}</div>
                    <div>金额：{{ (Number(singDetails.business_balance) / 100000000).toFixed(2) }} 亿</div>
                    <div>今开：{{ singDetails.open_px }}</div>
                    <div>昨收：{{ singDetails.preclose_px }}</div>
                    <div>成交：{{ (Number(singDetails.business_amount) / 10000).toFixed(2) }} 万</div>
                </div> -->
            </div>
            <div class="line"></div>
            <div class="ebox">
                <div class="ctab">
                    <div :class="`tab_item ${tabIndex == 0 ? 'select' : ''}`" @click="tabIndex = 0">
                        <div>
                            <div>最新成交</div>
                            <span class="bar"></span>
                        </div>
                    </div>
                    <div :class="`tab_item ${tabIndex == 1 ? 'select' : ''}`" @click="tabIndex = 1; sellList = []; getSellList();">
                        <div>
                            <div>当前成交</div>
                            <span class="bar"></span>
                        </div>
                    </div>
                </div>

                <div class="buy_info" v-if="tabIndex == 0">
                    <div class="buy_info_left">
                        <div class="title">买盘</div>
                        <div class="list">
                            <div class="list_info">
                                <span>买1</span>
                                <span>{{ singDetails.buy1_num }}</span>
                                <span>{{ singDetails.buy1 }}</span>
                            </div>
                            <div class="list_info">
                                <span>买2</span>
                                <span>{{ singDetails.buy2_num }}</span>
                                <span>{{ singDetails.buy2 }}</span>
                            </div>
                            <div class="list_info">
                                <span>买3</span>
                                <span>{{ singDetails.buy3_num }}</span>
                                <span>{{ singDetails.buy3 }}</span>
                            </div>
                            <div class="list_info">
                                <span>买4</span>
                                <span>{{ singDetails.buy4_num }}</span>
                                <span>{{ singDetails.buy4 }}</span>
                            </div>
                            <div class="list_info">
                                <span>买5</span>
                                <span>{{ singDetails.buy5_num }}</span>
                                <span>{{ singDetails.buy5 }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="buy_info_left">
                        <div class="title">卖盘</div>
                        <div class="list">
                            <div class="list_info green">
                                <span>卖1</span>
                                <span>{{ singDetails.sell1_num }}</span>
                                <span>{{ singDetails.sell1 }}</span>
                            </div>
                            <div class="list_info green">
                                <span>卖2</span>
                                <span>{{ singDetails.sell2_num }}</span>
                                <span>{{ singDetails.sell2 }}</span>
                            </div>
                            <div class="list_info green">
                                <span>卖3</span>
                                <span>{{ singDetails.sell3_num }}</span>
                                <span>{{ singDetails.sell3 }}</span>
                            </div>
                            <div class="list_info green">
                                <span>卖4</span>
                                <span>{{ singDetails.sell4_num }}</span>
                                <span>{{ singDetails.sell4 }}</span>
                            </div>
                            <div class="list_info green">
                                <span>卖5</span>
                                <span>{{ singDetails.sell5_num }}</span>
                                <span>{{ singDetails.sell5 }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ebox_container" v-if="tabIndex == 1">
                    <van-list v-model="loading" :finished="finished" :finished-text="$t('hj43')" @load="getSellList" :immediate-check="immediate">
                        <div class="cbox" v-for="value in sellList" :key="value.id">
                            <div class="flex">
                                <div style="display: flex; align-items: center;">
                                    <div class="tag" v-if="singDetails.gid.indexOf('sz') > -1">深</div>
                                    <div class="tag" v-if="singDetails.gid.indexOf('sh') > -1">沪</div>
                                    <div class="tag" v-if="singDetails.gid.indexOf('bj') > -1">北</div>
                                    <div>{{value.stockName}}({{value.stockGid}})</div>
                                </div>
                                <div>最新价格：<span class="red">{{value.now_price}}</span></div>
                            </div>
                            <div class="flex">
                                <div>买入价格：{{value.buyOrderPrice}}</div>
                                <div>买入数量：{{value.buyNum / 100}}手</div>
                            </div>
                            <div class="flex">
                                <div>市值：{{ value.buyPrice }}</div>
                            </div>
                            <div class="flex">
                                <div>浮动盈亏：{{ value.profitAndLose }}</div>
                                <div>总盈亏：{{ value.allProfitAndLose }}</div>
                            </div>
                            <div class="flex">
                                <div>时间</div>
                                <div>{{ value.buyOrderTime | gettime }}</div>
                            </div>
                            <div class="cbtn" @click.stop="getpingcang(value.positionSn)">我要平仓</div>
                        </div>
                    </van-list>
                </div>
            </div>
            <div class="action">
                <div :class="`zixuan ${isOptionOpt ? 'select' : ''}`" @click="option()">
                    <div v-if="!isOptionOpt">
                        <svg t="1743077219751" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7112" width="64" height="64">
                            <path d="M941.696 82.176v859.648H82.176V82.176h859.52z m0-82.176H82.176C36.736 0 0 36.736 0 82.176v859.648C0 987.264 36.736 1024 82.176 1024h859.648c45.44 0 82.176-36.736 82.176-82.176V82.176A82.304 82.304 0 0 0 941.696 0z" p-id="7113"></path>
                            <path d="M471.424 548.672H248.384a39.872 39.872 0 0 1-39.808-39.936c0-22.08 17.92-40 40.064-40h222.784V244.864a37.312 37.312 0 1 1 74.688 0v528a37.312 37.312 0 0 1-74.688 0V548.672z m303.296-75.84a35.968 35.968 0 1 1 0 71.936l-163.84 3.2a35.968 35.968 0 0 1 0-71.936l163.84-3.2z" p-id="7114"></path>
                        </svg>
                    </div>
                    <div v-else>
                        <svg style="fill: rgba(14, 201, 177, 1);" t="1743077301130" class="icon" viewBox="0 0 1025 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8096" width="64" height="64">
                            <path d="M512.653762 1023.998047A511.791377 511.791377 0 0 1 313.345574 40.47724a512.105864 512.105864 0 0 1 398.616376 943.45886 508.786731 508.786731 0 0 1-199.308188 40.061947z m0-943.45886C274.285175 80.539187 80.986279 273.838083 80.986279 512.20667s193.298896 431.667483 431.667483 431.667482 431.667483-193.298896 431.667482-431.667482S751.022348 80.539187 512.653762 80.539187z" fill="" p-id="8097"></path>
                            <path d="M427.522124 715.521052a40.061947 40.061947 0 0 1-28.043364-12.018584L207.181413 511.205121a40.36742 40.36742 0 0 1 57.088275-57.088275l163.252436 164.253984 339.525003-339.525004a40.36742 40.36742 0 0 1 57.088275 57.088275L455.565487 703.502468a40.061947 40.061947 0 0 1-28.043363 12.018584z" fill="" p-id="8098"></path>
                        </svg>
                    </div>
                    <span>{{isOptionOpt == false ? '自选' : '已加入'}}</span>
                </div>
                <div class="buy" @click="goBuy(0)">买</div>
                <div class="sell" @click="goBuy(1)">卖</div>
            </div>
        </div>
    </div>
</template>

<script>
import Kline from "./components/kLine.vue";
import * as api from "@/axios/api";
import { Toast } from "mint-ui";
import { MessageBox } from "mint-ui";
export default {
    name: "kline",
    data() {
        return {
            news: "tab_2",
            tabIndex: 0,
            kLineDetails: {}, // K线图详情数据
            singDetails: {},
            scFlag: false,
            acseFlag: false,
            jianjie: "",
            optionBtn: false,
            isOptionOpt: false,
            dialogFlag: false,
            timedata: [],
            newsdetailList: [],
            tabhq: 0,
            finished: false,
            immediate: false,
            sellList: [],
        };
    },
    components: {
        Kline,
    },
    created() {
        const { query } = this.$route;
        this.kLineDetails = query;
        if (query.if_us == "1") {
            this.getSingDetailUs();
        } else {
            this.getSingDetails();
        }
        this.getOpation();
        // this.$Lazyload();
        this.getUserInfo();
    },

    // beforeDestroy() {
    //   this.Klinetype = false;
    //   window.clearInterval()
    // },
    methods: {
        getSellList() {
            let _this = this;
            let opt = {
                state: 0,
                stockCode: _this.singDetails.code, // 代码
                stockSpell: "", // 简拼
                pageNum: 1,
                pageSize: 100,
            };
            _this.loading = true;
            api.getOrderList(opt).then((res) => {
                if (res.data.list.length < 15) _this.finished = true;
                for (let i = 0; i < res.data.list.length; i++) {
                    console.log(res.data.list[i]);
                    _this.sellList.push(res.data.list[i]);
                }
                _this.loading = false;
            });
        },
        getpingcang(val) {
            MessageBox.confirm(this.$t("hj139") + "?", this.$t("hj165"), {
                confirmButtonText: this.$t("hj161"),
                cancelButtonText: this.$t("hj106"),
            })
                .then(async () => {
                    let opt = {
                        positionSn: val,
                    };
                    let data = await api.sell(opt);
                    if (data.status === 0) {
                        Toast(data.msg);
                        // 沪深京持仓
                        this.finished = false;
                        this.getListDetail();
                        this.tabsPositionNumArr = [];
                        // 沪深京平仓
                        this.finisheds = false;
                        this.tabsPcArr = [];
                        this.init();
                    } else if (data.msg.indexOf("不在交易时段内") > -1) {
                        Toast(this.$t("hj140"));
                    } else {
                        Toast(data.msg);
                    }
                })
                .catch(() => {});
        },
        goBuy(tab) {
            this.$router.push({
                path: "/buyStocks",
                query: {
                    name: this.singDetails.name,
                    code: this.singDetails.code,
                    m: this.singDetails.nowPrice,
                    id: this.singDetails.id,
                    tab: tab,
                    symbol: this.singDetails.gid,
                },
            });
        },
        async getHknews() {
            await api.queryIndexNews().then((res) => {
                if (res.status == 0) {
                    this.newsdetailList = res.data.data;
                }
            });
        },
        async getNohknews() {
            let data = await api.queryNewsList(4);
            this.newsdetailList = data.data.list;
        },
        async option() {
            if (this.optionBtn) {
                return;
            }
            this.optionBtn = true;
            if (this.isOptionOpt) {
                let data = await api.delOption({
                    code: this.kLineDetails.code,
                });
                if (data.status === 0) {
                    this.getOpation();
                    this.optionBtn = false;

                    Toast(this.$t("hj97"));
                } else {
                    this.optionBtn = false;

                    Toast(data.msg);
                }
            } else {
                let data = await api.addOption({
                    code: this.kLineDetails.code,
                });
                if (data.status === 0) {
                    this.getOpation();
                    this.optionBtn = false;

                    Toast(this.$t("hj96"));
                } else {
                    Toast(data.msg);
                    this.optionBtn = false;
                }
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        async getUserInfo() {
            // 获取用户信息
            //   let showcookie = this.getCookie('USER_TOKEN');
            let data = await api.getUserInfo();
            if (data.status === 0) {
                // this.getProductSetting()
                this.$store.state.userInfo = data.data;
            } else {
                Toast(data.msg);
            }
            this.$store.state.user = this.user;
        },
        async getOpation() {
            let opts = {
                code: this.$route.query.code,
            };
            let data = await api.isOption(opts);
            if (data.status === 0) {
                // 0 --> 未添加
                this.isOptionOpt = false;
            } else {
                this.isOptionOpt = true;
            }
        },
        async getSingDetails() {
            let opts = {
                code: this.kLineDetails.code,
                stockType: this.kLineDetails.type,
            };
            await api.getSingleStock(opts).then((res) => {
                if (res.status === 0) {
                    this.singDetails = res.data.stock;

                    if (res.data.introduction) {
                        this.jianjie = res.data.introduction;
                    } else {
                        this.jianjie = res.data.indexintroduction;
                    }
                    console.log(this.singDetails);
                    if (
                        this.kLineDetails.if_zhishu != "0" &&
                        this.singDetails.gid.indexOf("hk") > -1
                    ) {
                        this.getHknews();
                    } else {
                        this.getNohknews();
                    }
                }
            });
        },
        async getSingDetailUs() {
            let opts = {
                code: this.kLineDetails.code,
                stockType: this.kLineDetails.type,
            };
            await api.getUsDetail(opts).then((res) => {
                // console.log(res,1111123);
                // var that = this
                // if(!res){
                //   setTimeout(() => {
                //     that.getSingDetailUs()
                //   }, 3000);

                // }
                if (res.status === 0) {
                    this.singDetails = res.data.stock;
                    this.timedata = res.data.timedata;
                    if (res.data.introduction) {
                        this.jianjie = res.data.introduction;
                    } else {
                        this.jianjie = res.data.indexintroduction;
                    }
                    this.getNohknews();
                    console.log(this.singDetails);
                }
            });
        },
        handleJj() {
            this.acseFlag = true;
            setTimeout(() => {
                this.acseFlag = false;
            }, 1000);
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        handleBack() {
            this.$router.go(-1);
        },
        handleSc() {
            this.scFlag = !this.scFlag;
        },
        // goBuy(index) {
        //   this.$router.push({
        //     path: "/TradingBuy",
        //     query: {
        //       t: index,
        //       code: this.kLineDetails.code,
        //       m: this.singDetails.nowPrice,
        //       type: this.kLineDetails.if_zhishu,
        //       id: this.singDetails.id,
        //       name: this.kLineDetails.name,
        //       if_us: this.kLineDetails.if_us,
        //     }
        //   });
        //   if (navigator.vibrate) {
        //     // 支持
        //     navigator.vibrate([55]);
        //   }
        // }
    },
    filters: {
        getName(name) {
            if (name.length > 15) {
                return name.substring(0, 14);
            } else {
                return name;
            }
        },
        gettime(time) {
            if (!time) {
                return "";
            }
            var nd = new Date(time);
            var y = nd.getFullYear();
            var mm = nd.getMonth() + 1;
            var d = nd.getDate();
            var h = nd.getHours();
            var m = nd.getMinutes();
            var c = nd.getSeconds();
            if (mm < 10) {
                mm = "0" + mm;
            }
            if (d < 10) {
                d = "0" + d;
            }
            if (h < 10) {
                h = "0" + h;
            }
            if (m < 10) {
                m = "0" + m;
            }
            if (c < 10) {
                c = "0" + c;
            }
            // 17:35:2922-06-2022
            return y + "-" + mm + "-" + d + " " + h + ":" + m + ":" + c;
        },
        utc2beijing(utc_datetime) {
            // 转为正常的时间格式 年-月-日 时:分:秒
            var T_pos = utc_datetime.indexOf("T");
            var Z_pos = utc_datetime.indexOf("Z");
            var year_month_day = utc_datetime.substr(0, T_pos);
            var hour_minute_second = utc_datetime.substr(
                T_pos + 1,
                Z_pos - T_pos - 1
            );
            var new_datetime = year_month_day + " " + hour_minute_second; // 2017-03-31 08:02:06

            // 处理成为时间戳
            timestamp = new Date(Date.parse(new_datetime));
            timestamp = timestamp.getTime();
            timestamp = timestamp / 1000;

            // 增加8个小时，北京时间比utc时间多八个时区
            var timestamp = timestamp + 8 * 60 * 60;

            // 时间戳转为时间
            var beijing_datetime = new Date(parseInt(timestamp) * 1000)
                .toLocaleString()
                .replace(/年|月/g, "-")
                .replace(/日/g, " ");
            return beijing_datetime; // 2017-03-31 16:02:06
        },
    },
};
</script>

<style lang="less" scoped>
.tag {
    background: #6d9cff;
    font-size: 0.2791rem;
    color: #fff;
    width: 0.3256rem;
    height: 0.3256rem;
    line-height: 0.3256rem;
    text-align: center;
    margin-right: 0.1162rem;
}
.container {
    font-size: 0.3256rem;
    padding: 0;
    background: #fff;
    min-height: 100vh;
    padding-bottom: 1.8603rem;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .main {
        // padding: 0 0.3488rem;
        .main_top {
            display: flex;
            // justify-content: center;
            // align-items: center;
            // padding: 0.3488rem 0;
            .main_top_left {
                width: 2.7906rem;
                line-height: 0.8372rem;
                .main_top_left_bottom {
                    display: flex;
                    justify-content: space-between;
                }
            }
            .main_top_right {
                font-size: 0.279rem;
                flex: 1;
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                margin-left: 0.2326rem;
                line-height: 0.8372rem;
                &.red {
                    color: #f70202;
                }
            }
        }
        .line {
            height: 0.3488rem;
            width: 100%;
            background: rgba(245, 247, 250, 1);
        }
        .ebox {
            background: #fff;
            // box-shadow: 0 0 6px #0003;
            // padding: 0.3488rem;
            // border-radius: 0.2326rem;
            &.mt {
                // margin-top: 0.3488rem;
            }
            .echart_info {
                margin-top: 0.2326rem;
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 0.2326rem;
            }
            .ctab {
                display: flex;
                border-bottom: solid 1px rgba(223, 223, 223, 1);
                .tab_item {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    padding: 0.3488rem 0;
                    padding-bottom: 0;
                    font-size: 0.3721rem;
                    color: rgba(148, 149, 154, 1);
                    .bar {
                        margin-top: 0.3488rem;
                        background: rgba(224, 57, 54, 1);
                        height: 2px;
                        // margin-top: 0.1162rem;
                        display: none;
                        width: 100%;
                    }
                    &.select {
                        color: #333;
                        .bar {
                            display: block;
                        }
                    }
                }
            }
            .buy_info {
                display: flex;
                padding-top: 0.3488rem;
                .buy_info_left {
                    flex: 1;
                    // width: 50%;
                    padding: 0.3488rem;
                    padding-top: 0;
                    .list {
                        // display: flex;
                        // flex-direction: column;
                        .list_info {
                            display: flex;
                            justify-content: space-between;
                            margin-top: 0.3488rem;
                            color: #f70202;
                            span:nth-of-type(1) {
                                color: rgba(181, 181, 181, 1);
                            }
                            span:nth-of-type(3) {
                                color: #333;
                            }
                            &.green {
                                color: #01573d;
                            }
                            span {
                                margin-right: 0.2326rem;
                                &:nth-last-of-type(1) {
                                    margin-right: 0;
                                }
                            }
                        }
                    }
                }
            }
            .ebox_container {
                padding: 0 0.3488rem;
                .ebox {
                    box-shadow: 0 0 6px #0003;
                    padding: 0.3488rem;
                    border-radius: 0.2326rem;
                    display: flex;
                    align-items: center;
                    .clabel {
                        color: rgba(125, 125, 125, 1);
                        width: 30%;
                    }
                    .tag {
                        background: #6d9cff;
                        font-size: 0.2791rem;
                        color: #fff;
                        width: 0.3256rem;
                        height: 0.3256rem;
                        line-height: 0.3256rem;
                        text-align: center;
                        margin-right: 0.1162rem;
                    }
                    .cang {
                        display: flex;
                        .cang_item {
                            background: #eee;
                            margin-right: 0.2326rem;
                            font-size: 0.2791rem;
                            padding: 0.1163rem;
                            &.active {
                                background: rgba(238, 0, 17, 1);
                                color: #fff;
                            }
                        }
                    }
                    .input {
                        flex: 1;
                        input {
                            width: 100%;
                        }
                    }
                    &.mt {
                        margin-top: 0.3488rem;
                    }
                }
                .ebtn {
                    background: rgba(238, 0, 17, 1);
                    text-align: center;
                    color: #fff;
                    border-radius: 0.2326rem;
                    height: 1.0233rem;
                    line-height: 1.0233rem;
                    margin-top: 0.3488rem;
                }
                .cbox {
                    border-bottom: solid 1px rgba(223, 223, 223, 1);
                    font-size: 0.3256rem;
                    line-height: 0.6976rem;
                    padding: 0.3488rem 0;
                    .tag {
                        background: #6d9cff;
                        font-size: 0.2791rem;
                        color: #fff;
                        width: 0.3256rem;
                        height: 0.3256rem;
                        line-height: 0.3256rem;
                        text-align: center;
                        margin-right: 0.1162rem;
                    }
                    .flex {
                        display: flex;
                        justify-content: space-between;
                    }
                    .cbtn {
                        background: rgba(240, 240, 240, 1);
                        height: 0.6976rem;
                        line-height: 0.6976rem;
                        border-radius: 0.3333rem;
                        width: 2.3255rem;
                        text-align: center;
                        margin: auto;
                        margin-top: 0.3488rem;
                    }
                }
            }
        }
        .action {
            background: #fff;
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0.3488rem;
            display: flex;
            padding: 0 0.3488rem;
            height: 1.1627rem;
            .zixuan {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 1px solid rgba(245, 247, 250, 1);
                svg {
                    width: 0.3488rem;
                    height: 0.3488rem;
                    fill: #333;
                }
                span {
                    margin-left: 0.2325rem;
                }
                &.select {
                    color: rgba(14, 201, 177, 1);
                }
            }
            .buy {
                flex: 1;
                font-size: 0.372rem;
                background: rgba(215, 12, 24, 1);
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 0.3488rem;
            }
            .sell {
                flex: 1;
                font-size: 0.372rem;
                background: rgba(37, 103, 255, 1);
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            // div {
            //     display: flex;
            //     flex-direction: column;
            //     justify-content: center;
            //     align-items: center;
            //     flex: 1;
            //     img {
            //         display: block;
            //         width: 0.814rem;
            //         height: 0.814rem;
            //     }
            //     span {
            //         margin-top: 0.2326rem;
            //     }
            // }
        }
    }
}
</style>