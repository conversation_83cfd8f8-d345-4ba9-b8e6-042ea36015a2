<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <title>首页</title>
  <link rel="shortcut icon" href="static/logo.png" type=image/x-icon>
  <link href="static/css/public1.css" rel="stylesheet" />
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #fff;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }

    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
      z-index: 9999;
      transition: opacity 0.3s ease;
    }

    .logo-container {
      position: relative;
      width: 120px;
      height: 120px;
      margin-bottom: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .loading-logo-img {
      width: 60px;
      /* 进一步缩小logo尺寸 */
      height: 60px;
      /* 进一步缩小logo尺寸 */
      object-fit: contain;
      border-radius: 12px;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
      z-index: 2;
      /* 确保logo在旋转环上方 */
      position: relative;
      /* 改为相对定位 */
    }

    /* 修改脉动动画，不使用transform位移 */
    @keyframes pulse {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.05);
      }

      100% {
        transform: scale(1);
      }
    }

    .logo-pulse {
      animation: pulse 2s infinite;
    }

    .spinner {
      position: absolute;
      width: 110px;
      /* 调整旋转环尺寸 */
      height: 110px;
      /* 调整旋转环尺寸 */
      border: 3px solid rgba(238, 0, 17, 0.1);
      border-top: 3px solid #ee0011;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 1;
      /* 确保旋转环在logo下方 */
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .progress-container {
      width: 200px;
      height: 4px;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      overflow: hidden;
      margin-top: 20px;
    }

    .progress-bar {
      height: 100%;
      width: 0%;
      background-color: #ee0011;
      border-radius: 2px;
      transition: width 0.3s ease;
    }

    .loading-text {
      margin-top: 15px;
      font-size: 14px;
      color: #666;
    }

    .percent-text {
      font-weight: bold;
      color: #ee0011;
    }
  </style>
  <script>
    // 禁用缩放
    function addMeta() {
      //   $('head').append('<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />');
    }
    // setTimeout(addMeta, 3000);

    // 禁用双指放大
    document.documentElement.addEventListener(
      "touchstart",
      function (event) {
        if (event.touches.length > 1) {
          event.preventDefault();
        }
      },
      {
        passive: false,
      }
    );

    // 禁用双击放大
    var lastTouchEnd = 0;
    document.documentElement.addEventListener(
      "touchend",
      function (event) {
        var now = Date.now();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      },
      {
        passive: false,
      }
    );

    // 确保DOM加载完成后再执行
    document.addEventListener("DOMContentLoaded", function () {
      // 初始化进度条
      var progressBar = document.getElementById('progress-bar');
      var percentText = document.getElementById('percent-text');
      var loadingText = document.getElementById('loading-text');
      var loadingContainer = document.getElementById('loading-container');
      var loadingLogo = document.getElementById('loading-logo-img');

      // 添加脉动动画类
      if (loadingLogo) {
        loadingLogo.classList.add('logo-pulse');
      }

      var startTime = new Date().getTime();
      var progress = 0;

      // 创建全局方法供Vue调用
      window.hideLoadingScreen = function () {
        window.appIsReady = true;
      };

      // 模拟加载进度
      var interval = setInterval(function () {
        // 计算已经过去的时间（毫秒）
        var elapsedTime = new Date().getTime() - startTime;

        if (progress < 95) {
          // 前95%的进度缓慢增加，约4秒达到95%
          progress = Math.min(95, Math.floor((elapsedTime / 4000) * 95));
        } else if (window.appIsReady) {
          // 如果应用已准备好，直接完成到100%
          progress = 100;
        }

        if (progressBar) {
          progressBar.style.width = progress + '%';
        }

        if (percentText) {
          percentText.innerText = progress + '%';
        }

        if (progress >= 100) {
          clearInterval(interval);

          if (loadingText) {
            loadingText.innerText = '加载完成';
          }

          // 延迟一小段时间后隐藏加载界面
          setTimeout(function () {
            if (loadingContainer) {
              loadingContainer.style.opacity = '0';
              setTimeout(function () {
                loadingContainer.style.display = 'none';
              }, 300);
            }
          }, 200);
        }
      }, 100);

      window.appIsReady = false;
    });
  </script>
</head>

<body>
  <div id="app">
    <div id="loading-container" class="loading-container">
      <div class="logo-container">
        <div class="spinner"></div>
        <img id="loading-logo-img" src="static/logo.png" alt="Logo" class="loading-logo-img">
      </div>
      <div class="progress-container">
        <div id="progress-bar" class="progress-bar"></div>
      </div>
      <div id="loading-text" class="loading-text">
        正在加载应用 <span id="percent-text" class="percent-text">0%</span>
      </div>
    </div>
  </div>
  <!-- built files will be auto injected -->
</body>

</html>