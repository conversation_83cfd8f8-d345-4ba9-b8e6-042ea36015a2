webpackJsonp([16],{M4Fd:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=s("Xxa5"),r=s.n(a),i=s("exGp"),c=s.n(i),n=s("c2Ch"),v=s("Au9i"),d={data:function(){return{currentItem:{}}},mounted:function(){this.currentItem=JSON.parse(decodeURIComponent(this.$route.query.item)),console.log(this.currentItem)},methods:{parseNumber:function(t){return parseFloat(t).toFixed(2)},withdrawOrder:function(t){var e=this;if(0!=t.backStatus)return!1;var s={id:t.id,userId:t.userId};v.MessageBox.confirm("确定撤单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(c()(r.a.mark(function t(){var a;return r.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,n.s(s);case 2:0===(a=t.sent).status?(Object(v.Toast)({message:a.data,type:"success"}),e.$router.go(-1)):Object(v.Toast)({message:a.data,type:"error"});case 4:case"end":return t.stop()}},t,e)})))}}},l={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return t.currentItem&&t.currentItem.stockName?s("div",{staticClass:"container"},[s("div",{staticClass:"header"},[s("van-nav-bar",{attrs:{title:"委托详情","left-arrow":"",fixed:""},on:{"click-left":function(e){return t.$router.go(-1)}}})],1),t._v(" "),s("div",{staticClass:"ebox"},[s("div",{staticClass:"elabel"},[t._v("委托股票")]),t._v(" "),s("div",{staticClass:"enr"},[s("div",{staticClass:"stock"},[s("div",{staticClass:"name"}),t._v(" "),s("div",{staticClass:"child"},[t.currentItem.stockGid.indexOf("sz")>-1?s("div",{staticClass:"tag"},[t._v("深")]):t._e(),t._v(" "),t.currentItem.stockGid.indexOf("sh")>-1?s("div",{staticClass:"tag"},[t._v("沪")]):t._e(),t._v(" "),t.currentItem.stockGid.indexOf("bj")>-1?s("div",{staticClass:"tag"},[t._v("北")]):t._e(),t._v(" "),s("div",[t._v(t._s(t.currentItem.stockName)+"（"+t._s(t.currentItem.stockCode)+"）")])])])])]),t._v(" "),t._m(0),t._v(" "),t._m(1),t._v(" "),s("div",{staticClass:"ebox"},[s("div",{staticClass:"elabel"},[t._v("委托手数")]),t._v(" "),s("div",{staticClass:"enr"},[t._v(t._s(t.currentItem.orderNum/100))])]),t._v(" "),s("div",{staticClass:"ebox"},[s("div",{staticClass:"elabel"},[t._v("委托股数")]),t._v(" "),s("div",{staticClass:"enr"},[t._v(t._s(t.currentItem.orderNum))])]),t._v(" "),s("div",{staticClass:"ebox"},[s("div",{staticClass:"elabel"},[t._v("委托价格")]),t._v(" "),s("div",{staticClass:"enr"},[t._v(t._s(t.parseNumber(t.currentItem.buyOrderPrice)))])]),t._v(" "),s("div",{staticClass:"ebox"},[s("div",{staticClass:"elabel"},[t._v("委托时间")]),t._v(" "),s("div",{staticClass:"enr"},[t._v(t._s(t.dayjs(t.currentItem.buyOrderTime).format("YYYY-MM-DD HH:mm:ss")))])]),t._v(" "),s("div",{staticClass:"ebtn",on:{click:function(e){return t.withdrawOrder(t.currentItem)}}},[t._v("取消委托")])]):t._e()},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[this._v("买卖类型")]),this._v(" "),e("div",{staticClass:"enr"},[this._v("证券买入")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[this._v("成交类型")]),this._v(" "),e("div",{staticClass:"enr"},[this._v("挂单")])])}]};var o=s("VU/8")(d,l,!1,function(t){s("cbLr")},"data-v-6cf34ada",null);e.default=o.exports},cbLr:function(t,e){}});