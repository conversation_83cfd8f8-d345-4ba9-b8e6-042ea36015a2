webpackJsonp([15],{IkwI:function(t,i,s){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var e=s("mvHQ"),a=s.n(e),n=s("c2Ch"),c=s("mtWM"),v=s.n(c),d={data:function(){return{list:[],finished:!1,loading:!0,itemIndex:0,pageNum:1,source:""}},mounted:function(){this.changeItemIndex(0)},methods:{parseNumber:function(t){return parseFloat(t).toFixed(2)},changeItemIndex:function(t){this.pageNum=1,this.list=[],this.itemIndex=t,this.finished=!1,this.getOrderList(t)},chicangDetail:function(t){this.$router.push({path:"/chicangDetail?type=dazong&item="+a()(t)})},weituoDetail:function(t){this.$router.push({path:"/weituoDetail?item="+a()(t)})},getOrderList:function(t){var i=this;i.source&&i.source.cancel("close request"),i.source=v.a.CancelToken.source(),i.loading=!0;var s={state:t,type:3,stockCode:"",stockSpell:"",pageNum:this.pageNum,pageSize:15};n.T(s,{cancelToken:i.source.token}).then(function(t){t.data.list.length<15&&(i.finished=!0);for(var s=0;s<t.data.list.length;s++)i.list.push(t.data.list[s]);i.loading=!1,i.pageNum++})}}},r={render:function(){var t=this,i=t.$createElement,s=t._self._c||i;return s("div",{staticClass:"container"},[s("div",{staticClass:"header"},[s("van-nav-bar",{attrs:{title:"天启护盘记录","left-arrow":"",fixed:""},on:{"click-left":function(i){return t.$router.go(-1)}}})],1),t._v(" "),s("div",{staticClass:"menu"},[s("div",{class:"item "+(0==t.itemIndex?"active":""),on:{click:function(i){return t.changeItemIndex(0)}}},[t._m(0)]),t._v(" "),s("div",{class:"item "+(1==t.itemIndex?"active":""),on:{click:function(i){return t.changeItemIndex(1)}}},[t._m(1)]),t._v(" "),s("div",{class:"item "+(2==t.itemIndex?"active":""),on:{click:function(i){return t.changeItemIndex(2)}}},[t._m(2)])]),t._v(" "),0==t.itemIndex?s("div",{staticClass:"list"},[t._m(3),t._v(" "),s("div",{staticClass:"list_container"},[s("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(i){return t.getOrderList(1)}},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},[t._l(t.list,function(i){return[s("div",{key:i.id,staticClass:"item",on:{click:function(s){return t.chicangDetail(i)}}},[s("div",{staticClass:"ebox",staticStyle:{"justify-content":"left"}},[s("div",{staticClass:"stock"},[s("div",{staticClass:"name"},[t._v(t._s(i.stockName))]),t._v(" "),s("div",{staticClass:"child"},[i.stockGid.indexOf("sz")>-1?s("div",{staticClass:"tag"},[t._v("深")]):t._e(),t._v(" "),i.stockGid.indexOf("sh")>-1?s("div",{staticClass:"tag"},[t._v("沪")]):t._e(),t._v(" "),i.stockGid.indexOf("bj")>-1?s("div",{staticClass:"tag"},[t._v("北")]):t._e(),t._v(" "),s("div",[t._v(t._s(i.stockCode))])])])]),t._v(" "),s("div",{staticClass:"cbox"},[s("span",[t._v(t._s(t.parseNumber(i.buyNum)))]),t._v(" "),s("span",[t._v(t._s(t.parseNumber(i.buyPrice)))])]),t._v(" "),s("div",{staticClass:"cbox"},[s("span",[t._v(t._s(t.parseNumber(i.now_price)))]),t._v(" "),s("span",[t._v(t._s(t.parseNumber(i.buyOrderPrice)))])]),t._v(" "),s("div",{staticClass:"cbox"},[s("span",{class:i.profitAndLose>0?"red":"green"},[t._v(t._s(i.profitAndLose))]),t._v(" "),s("span",{class:i.profitAndLossRatio>0?"red":"green"},[t._v(t._s(i.profitAndLossRatio)+"%")])])])]})],2)],1)]):t._e(),t._v(" "),1==t.itemIndex?s("div",{staticClass:"list"},[t._m(4),t._v(" "),s("div",{staticClass:"list_container"},[s("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(i){return t.getOrderList(1)}},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},[t._l(t.list,function(i){return[s("div",{key:i.id,staticStyle:{"border-bottom":"solid 1px rgba(223, 223, 223, 1)","padding-bottom":"0.3488rem"}},[s("div",{staticClass:"item",staticStyle:{border:"none"}},[s("div",{staticClass:"ebox",staticStyle:{"justify-content":"left"}},[s("div",{staticClass:"stock"},[s("div",{staticClass:"name"},[t._v(t._s(i.stockName))]),t._v(" "),s("div",{staticClass:"child"},[i.stockGid.indexOf("sz")>-1?s("div",{staticClass:"tag"},[t._v("深")]):t._e(),t._v(" "),i.stockGid.indexOf("sh")>-1?s("div",{staticClass:"tag"},[t._v("沪")]):t._e(),t._v(" "),i.stockGid.indexOf("bj")>-1?s("div",{staticClass:"tag"},[t._v("北")]):t._e(),t._v(" "),s("div",[t._v(t._s(i.stockCode))])])])]),t._v(" "),s("div",{staticClass:"cbox"},[s("span",[t._v(t._s(t.parseNumber(i.buyPrice)))]),t._v(" "),s("span",[t._v(t._s(t.parseNumber(i.buyNum)))])]),t._v(" "),s("div",{staticClass:"cbox"},[s("span",[t._v(t._s(t.parseNumber(i.buyOrderPrice)))]),t._v(" "),s("span",[t._v(t._s(t.parseNumber(i.sellOrderPrice)))])]),t._v(" "),s("div",{class:"cbox "+(i.profitAndLossRatio>0?"red":"green")},[s("span",[t._v(t._s(i.profitAndLose))]),t._v(" "),s("span",[t._v(t._s(i.profitAndLossRatio)+"%")])])]),t._v(" "),s("div",{staticClass:"time"},[s("div",[t._v(t._s(t.dayjs(i.buyOrderTime).format("YYYY-MM-DD HH:mm:ss")))]),t._v(" "),s("div",[t._v(t._s(t.dayjs(i.sellOrderTime).format("YYYY-MM-DD HH:mm:ss")))])]),t._v(" "),s("div",{staticClass:"dbtn",on:{click:function(s){return t.chicangDetail(i)}}},[t._v("查看详情")])])]})],2)],1)]):t._e(),t._v(" "),2==t.itemIndex?s("div",{staticClass:"weituo_list"},[s("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(i){return t.getOrderList(2)}},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},[t._l(t.list,function(i){return[s("div",{key:i.id,on:{click:function(s){return t.weituoDetail(i)}}},[s("div",{staticClass:"stock"},[s("div",{staticClass:"name"},[t._v(t._s(i.stockName))]),t._v(" "),s("div",{staticClass:"child"},[i.stockGid.indexOf("sz")>-1?s("div",{staticClass:"tag"},[t._v("深")]):t._e(),t._v(" "),i.stockGid.indexOf("sh")>-1?s("div",{staticClass:"tag"},[t._v("沪")]):t._e(),t._v(" "),i.stockGid.indexOf("bj")>-1?s("div",{staticClass:"tag"},[t._v("北")]):t._e(),t._v(" "),s("div",[t._v(t._s(i.stockCode))])])]),t._v(" "),s("div",{staticClass:"info"},[s("div",{staticClass:"item"},[s("div",[t._v("买卖类别")]),t._v(" "),s("div",[t._v("证券买入")])]),t._v(" "),s("div",{staticClass:"item"},[s("div",[t._v("当前状态")]),t._v(" "),s("div",[t._v("挂单")])]),t._v(" "),s("div",{staticClass:"item"},[s("div",[t._v("委托手数")]),t._v(" "),s("div",[t._v(t._s(i.orderNum/100))])]),t._v(" "),s("div",{staticClass:"item"},[s("div",[t._v("委托价格")]),t._v(" "),s("div",[t._v(t._s(t.parseNumber(i.buyOrderPrice)))])])])])]})],2)],1):t._e()])},staticRenderFns:[function(){var t=this.$createElement,i=this._self._c||t;return i("div",[i("span",[this._v("我的持仓")]),this._v(" "),i("span")])},function(){var t=this.$createElement,i=this._self._c||t;return i("div",[i("span",[this._v("交易记录")]),this._v(" "),i("span")])},function(){var t=this.$createElement,i=this._self._c||t;return i("div",[i("span",[this._v("我的委托")]),this._v(" "),i("span")])},function(){var t=this.$createElement,i=this._self._c||t;return i("div",{staticClass:"list_title"},[i("div",{staticClass:"item"},[this._v("名称")]),this._v(" "),i("div",{staticClass:"item"},[this._v("持仓 | 市值")]),this._v(" "),i("div",{staticClass:"item"},[this._v("现价 | 成本")]),this._v(" "),i("div",{staticClass:"item"},[this._v("盈亏 | 涨幅")])])},function(){var t=this.$createElement,i=this._self._c||t;return i("div",{staticClass:"list_title"},[i("div",{staticClass:"item"},[this._v("股票 | 代码")]),this._v(" "),i("div",{staticClass:"item"},[this._v("本金 | 数量")]),this._v(" "),i("div",{staticClass:"item"},[this._v("买入 | 卖出价")]),this._v(" "),i("div",{staticClass:"item"},[this._v("收益 | 涨幅")])])}]};var l=s("VU/8")(d,r,!1,function(t){s("vMow")},"data-v-beccd3a4",null);i.default=l.exports},vMow:function(t,i){}});