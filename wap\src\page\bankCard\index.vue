<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="绑定银行卡" left-arrow fixed @click-left="$router.go(-1)" />
        </div>
        <div class="layout">
            <div class="form">
                <div class="clabel">银行名称</div>
                <div class="input">
                    <input type="text" :disabled="!addBank" v-model="bankName" placeholder="请输入银行名称" />
                </div>
            </div>
            <div class="form">
                <div class="clabel">开户支行</div>
                <div class="input">
                    <input type="text" :disabled="!addBank" v-model="bankAddress" placeholder="请输入开户支行" />
                </div>
            </div>
            <div class="form">
                <div class="clabel">卡号</div>
                <div class="input">
                    <input type="text" placeholder="请输入卡号" :disabled="!addBank" v-model="bankNo" />
                </div>
            </div>
            <div class="ebtn" @click="toSure" v-if="addBank">提交</div>
            <div class="ebtn" @click="$router.push('/bankUpDate')" v-if="!addBank">更换</div>
        </div>
    </div>
</template>

<script>
import * as api from '@/axios/api'
import { Toast } from 'mint-ui'
import { isNull, bankNoReg, isName } from '@/utils/utils'

export default {
    name: 'bankCard',
    data() {
        return {
            bankName: '', // 银行名称,
            bankAddress: '', // 需要精确到分行或者支行,
            bankNo: '', // 印象卡号
            addBank: false
        }
    },
    created() {
        this.getCardDetail()
    },
    methods: {
        async toSure() {
            // 添加银行卡
            // 去除输入字段中的所有空格
            const trimmedBankNo = String(this.bankNo).replace(/\s+/g, '')
            const trimmedBankName = String(this.bankName).replace(/\s+/g, '')
            const trimmedBankAddress = String(this.bankAddress).replace(/\s+/g, '')

            console.log(trimmedBankNo, 'trimmedBankNo')
            console.log(trimmedBankName, 'trimmedBankName')
            console.log(trimmedBankAddress, 'trimmedBankAddress')

            if (isNull(trimmedBankNo) || !bankNoReg(trimmedBankNo)) {
                Toast(this.$t('hj217'))
            } else if (isNull(trimmedBankName) || !isName(trimmedBankName)) {
                Toast(this.$t('hj218'))
            } else if (isNull(trimmedBankAddress) || !isName(trimmedBankAddress)) {
                Toast(this.$t('hj219'))
            } else {
                let opts = {
                    bankName: trimmedBankName,
                    bankNo: trimmedBankNo,
                    bankAddress: trimmedBankAddress
                }
                let data = await api.addBankCard(opts)
                if (data.status === 0) {
                    Toast(this.$t('hj220'))
                    this.$router.push('/newUser')
                } else {
                    Toast(data.msg)
                }
            }
        },
        async getCardDetail() {
            // 获取银行卡信息
            let data = await api.getBankCard()
            if (data.status === 0) {
                const { bankAddress, bankName, bankNo } = data.data
                this.bankAddress = bankAddress
                this.bankName = bankName
                this.bankNo = bankNo
                this.addBank = false
            } else {
                this.addBank = true
            }
        }
    }
}
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .layout {
        margin-top: 0.3488rem;
        min-height: calc(100vh - 1.4188rem);
        background: #fff;

        .auth-status {
            padding: 0.3488rem;
            text-align: center;

            .status-tag {
                display: inline-block;
                padding: 0.1163rem 0.2326rem;
                border-radius: 0.1163rem;
                font-size: 0.3023rem;
                font-weight: bold;
            }

            .status-pending {
                background-color: #e6e6e6;
                color: #666;
            }

            .status-processing {
                background-color: #e6f7ff;
                color: #1890ff;
                border: 1px solid #91d5ff;
            }

            .status-success {
                background-color: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
            }

            .status-failed {
                background-color: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
            }
        }

        .title {
            font-size: 0.372rem;
            padding-left: 0.3488rem;
            padding-top: 0.3488rem;
        }

        .form {
            padding: 0.3488rem;
            padding-bottom: 0;

            .clabel {
                font-size: 0.372rem;
            }

            .input {
                margin-top: 0.3488rem;
                background: rgba(245, 247, 250, 1);
                height: 1.1162rem;
                border-radius: 0.2325rem;
                padding: 0 0.3488rem;

                input {
                    height: 1.1162rem;
                    width: 100%;
                }
            }
        }

        .sfz {
            width: 9.2115rem;
            background: rgba(245, 247, 250, 1);
            margin: 0 auto;
            margin-top: 0.3488rem;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: justify;
            -ms-flex-pack: justify;
            justify-content: space-between;
            border-radius: 0.1602rem;

            .let {
                width: 4.1652rem;
                padding-left: 0.3488rem;

                // text-align: center;
                h6 {
                    font-size: 0.4005rem;
                    font-weight: 500;
                    color: #000;
                    margin-top: 0.3488rem;
                }

                p {
                    font-size: 0.267rem;
                    font-weight: 500;
                    color: #333;
                    opacity: 0.5;
                    margin-top: 0.3488rem;
                }
            }

            .rih {
                width: 4.1652rem;
                height: 2.6967rem;
                background: url(~@/assets/images/qiquan26/sfz_zheng.png) no-repeat;
                background-size: cover;
                margin-right: 0.2937rem;
                margin-top: 0.3471rem;
                margin-bottom: 0.3471rem;
                position: relative;

                &.fan {
                    background-image: url(~@/assets/images/qiquan26/sfz_fan.png);
                }

                .inp {
                    opacity: 0;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    top: 0;
                }

                img {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    top: 0;
                }
            }
        }

        .ebtn {
            border-radius: 8px;
            background: rgba(215, 12, 24, 1);
            box-shadow: 0px 2px 4px rgba(224, 57, 54, 0.49);
            height: 1.1162rem;
            line-height: 1.1162rem;
            text-align: center;
            color: #fff;
            margin: 0.3488rem;
        }
    }
}
</style>
