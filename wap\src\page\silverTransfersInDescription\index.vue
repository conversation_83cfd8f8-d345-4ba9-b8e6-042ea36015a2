<template>
    <div class="silver-transfers-in-description">
        <div class="head">
            <div rech="true">
                <h2>
                    <span class="hbnh"><a class="fan" @click="$router.go(-1)"></a></span>
                    银证转入说明
                </h2>
            </div>
        </div>

        <div class="content">
            <div class="title">银证通道简介</div>
            <div class="text">
                <p>由于该平台受证券监管部门监管，根据证监部门2021反洗黑钱金融条例，为了避免出现不法分子黑钱投入券商OTC账户，导致OTC账户全面冻结带来的损失，在面对OTC账户开放挂靠用户的情况下，采取更高效的审查机制支付通道
                </p>
                <p>用户须通过券商OTC账户和银行之间搭建的支付通道风控系统，进行银证转入和银证转出。OTC银证指定的承兑商风控审查账户根据该用户资金量开放了对公账户以及对私账户，进行一道关卡过滤黑钱。</p>
                <p>银行提供给OTC账户专属资金风控系统会在5分钟内完成审核，由银行指定承兑商确认该笔资金合理合法后，自动电汇到指定的OTC账户席位账户，自动转入券商OTC公有账户，期间不影响您的正常银证转出和银证转入以及操作。
                </p>
                <p>注意事项：</p>
                <p>1：由于支付收款上限限制，每次收款账户可能不一样，请每次银证转入前获取最新账户卡号仅限当日申请时段（30分钟内）使用，逾期失效为保证您的资金安全以及第一时间供您交易使用，请您获取卡号第一时间进行银证转入存款，其他时间不可擅自向该卡号转账，若违规转入对您造成任何损失均有您本人自行承担。
                </p>
                <p>2：如果您的网络环境不稳定或由于设备、环境、偏好、行为、关系、账户、身份等维度问题，可能导致账户风控系统提示风险，请您在安全的环境下发起银证转入。</p>
                <p>3：为确保银证转入及时到账，请确认您输入的金额和提交的银证转入资金一致。</p>
                <p>4：受银行到账通知时间影响，银证转入到账时间可能会延迟，请耐心等待。</p>
                <p>5：如有疑问请联系您的专属顾问。</p>
                <van-button type="info" size="large" round class="bt-s-class"
                    @click="silverTransfersInDescription">联系客服转入</van-button>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'silver-transfers-in-description',
    data() {
        return {}
    },
    created() { },
    methods: {
        silverTransfersInDescription() {
            this.$router.push('/service')
        }
    }
}
</script>
<style lang="less">
.silver-transfers-in-description {
    .content {
        padding: 30px;

        .title {
            text-align: center;
            font-size: 0.4272rem;
            font-weight: bolder;
            margin-bottom: 20px;
        }

        .text {
            p {
                font-size: 0.3467rem;
                line-height: 0.5333rem;
                color: #333;
                margin-bottom: 0.2667rem;
                text-indent: 2em;
            }
        }
    }

    .head {
        width: 100%;
        background: linear-gradient(-55deg, rgb(223, 48, 28), rgb(223, 48, 28));
        text-align: center;
    }

    .head h2 {
        text-align: center;
        height: 1.2549rem;
        width: 100%;
        position: relative;
        line-height: 1.2549rem;
        font-size: 0.4806rem;
        color: #fff;
        background: transparent;
        font-weight: 500;
        z-index: 3;
    }

    .hbnh {
        position: absolute;
        left: 0.4005rem;
        font-size: 0.4272rem;
        font-weight: 500;
    }

    .fan {
        width: 0.2403rem;
        height: 0.4272rem;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAAAXNSR0IArs4c6QAAAVdJREFUSEut1usqBVEYxvH/c1MuRJJDkhwTEpIkJUlyCEk++CJJckpycw49Gs3Wa+y9Z82ePd/Xr5n38KwRNR7bE0APMK1OHdtTwAWQGZcdQbZngPMc+QImK0O2Z4GzgIxLuqoE2Z4DTgMyJuk6K08yZHseOAnIqKSbRo2TINsLwHFARiTdxkaVQrYXgaOADEu6K3a7LWR7CTjMD2XdaYq0rZHtFWA/Rz6BIUn3reau6RvZXgX28kMfOfLQbnj/QbbXgN380HuOPJZtwB/I9jqwE5ABSc9lyJ8a2d4AtgPSL+klBfmFbG8CWwHpk/SaivxAtjMgg7Inq0mvpLcqSHehTOvKpzU+oSvFDlj99gesOJCDkp7Kip+yIknT3XL7C0tbum9lMbIMHKQkQEqwJWVSKZTPWTElq0dt6GYxt6uHf8DqX0cBq39BBqz+lR2w+j8RAfv9rfkGqF24CUdT9E4AAAAASUVORK5CYII=) no-repeat 50%;
        background-size: 100%;
        display: inline-block;
        margin-right: 0.1335rem;
        vertical-align: middle;
        margin-top: -0.0534rem;
    }
}
</style>
