
const ENV = process.env.NODE_ENV

// 生产

export default {
  // DOMAIN: 'https://api.gdjis.vip/',

  //正式用的接口域名，发布正式的时候需要打开

  DOMAIN: ENV == 'development' ? 'https://ky-api.ghjikopeld.top/' : '/',
  baseURL: ENV == 'development' ? 'https://ky-api.ghjikopeld.top/' : '/',

  //测试环境的接口域名，发布正式的时候关闭注释

  // DOMAIN: ENV == 'development' ? 'https://api-caida.dev6688.com/' : 'https://api-caida.dev6688.com/',
  // baseURL: ENV == 'development' ? 'https://api-caida.dev6688.com/' : 'https://api-caida.dev6688.com/',

  // baseURL: 'http://localhost:8070',
  /* Util API */
  // baseURL: 'http://*************:8091/',
  util: {
    image: '/util/image.html' // 图片上传
  }
}



// 测试

// export default {
//   // DOMAIN: 'https://api.gdjis.vip/',
//   DOMAIN:ENV == 'development'?'https://api-caida.dev6688.com/':'https://api-caida.dev6688.com/',
//   baseURL: ENV == 'development'?'https://api-caida.dev6688.com/':'https://api-caida.dev6688.com/',
//   // baseURL: 'http://localhost:8070',
//   /* Util API */
//   // baseURL: 'http://*************:8091/',
//   util: {
//     image: '/util/image.html' // 图片上传
//   }
// }



// http://*************:8091
// https://api-caida.dev6688.com/
// https://api-jdr.dev6688.com/
