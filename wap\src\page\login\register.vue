<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="注册" />
        </div>
        <div class="form">
            <div class="form_item">
                <div class="form_label">
                    <span>*</span>
                    <span>手机号</span>
                </div>
                <div class="form_input">
                    <input type="text" placeholder="请输入手机号" v-model="phone" />
                </div>
            </div>
            <div class="form_item">
                <div class="form_label">
                    <span>*</span>
                    <span>密码</span>
                </div>
                <div class="form_input">
                    <input type="password" placeholder="请输入密码" v-model="userPassword" />
                </div>
            </div>
            <div class="form_item">
                <div class="form_label">
                    <span>*</span>
                    <span>确认密码</span>
                </div>
                <div class="form_input">
                    <input type="password" placeholder="请再次输入密码" v-model="rePassword" />
                </div>
            </div>
            <div class="form_item">
                <div class="form_label">
                    <span>*</span>
                    <span>邀请码</span>
                </div>
                <div class="form_input">
                    <input type="text" placeholder="请输入邀请码" v-model="userName" />
                </div>
            </div>
        </div>
        <div class="chbox">
            <van-checkbox v-model="checked" checked-color="#ee0011">我已阅读并同意<span @click="$router.push({ path: '/xieyiMianze' })" style="color: #ff7a00;">《用户协议》</span></van-checkbox>
        </div>
        <div class="cbtn">
            <div class="reg_btn" @click="gook()">注册</div>
            <div class="login_btn" @click="$router.push({ path: '/login' })">登录账号</div>
        </div>
        <div class="service">
            <img src="~@/assets/images/qiquan26/service.png" @click="$router.push({ path: '/service' })" />
        </div>
    </div>
</template>

<script>
import headers from "./components/header.vue";
import Logo from "@/assets/img/LOGO2.png";
import { isNull, isPhone, pwdReg } from "@/utils/utils";
import { Toast } from "mint-ui";
import * as api from "@/axios/api";
export default {
    components: {
        headers,
    },
    name: "newRegister",
    data() {
        return {
            checked: false,
            verification: this.$t("hj25"),
            loginWay: this.$t("hj26"),
            loginWay: this.$t("hj26"),
            placeholder: this.$t("hj27"),
            Logo,
            phone: "",
            userName: "",
            code: "",
            userPassword: "",
            btnClass: false,
            codeshow: true,
            count: "", // 倒计时
            clickFalg: 0, //  点击次数
            rePassword: "",
            agree: false,
            dengl: false,
            alertShow: false,
            closable: false,
            texts: "",
            elType: "warning",
        };
    },
    mounted() {
        console.log("222222");
        const agentCode = this.$route.query.agentCode;
        console.log("" + this.$route.query.agentCode);

        if (agentCode !== undefined && agentCode !== "") {
            this.userName = agentCode;
        }
    },
    methods: {
        handleInput() {
            if (
                this.userPassword !== "" &&
                this.phone !== "" &&
                this.userPassword == this.rePassword &&
                this.userName != ""
            ) {
                this.btnClass = true;
            } else {
                this.btnClass = false;
            }
        },
        checkCodeBox() {
            if (isNull(this.phone) || !isPhone(this.phone)) {
                // Toast('请输入正确的手机号')
                // this.texts = this.$t('hj28')
                // this.alertShow = true
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(this.$t("hj28"));
            } else {
                this.checkPhone();
            }
        },
        async is_login() {
            // 获取localStorage中的token
            let token = localStorage.getItem("USERTOEKN");
            if (token) {
                this.$router.push("/home");
            }
        },

        async getcode() {
            // if(!this.checkCode()){
            //     // 验证图形码是否正确
            //     Toast('请输入正确图形验证码')
            //     return
            // }
            // 获取验证码
            if (this.clickFalg !== 0) {
                this.clickFalg = 0;
                return;
            }
            this.clickFalg++;
            //   var reg = 11 && /^((13|14|15|17|18)[0-9]{1}\d{8})$/
            let reg = /^[0-9]{11}$/; // 手机号码验证
            if (isNull(this.phone)) {
                // this.texts = this.$t('hj29')
                // this.alertShow = true
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(this.$t("hj29"));
            } else {
                if (!reg.test(this.phone)) {
                    // this.texts = this.$t('hj28')
                    // this.alertShow = true
                    // setTimeout(() => {
                    //   this.alertShow = false
                    // }, 2000)
                    Toast(this.$t("hj28"));
                } else {
                    //   var sign  = this.$md5(this.phone+'W&WzL42v').toUpperCase()
                    let result = await api.getCode({ phoneNum: this.phone });
                    if (result.status === 0) {
                        const TIME_COUNT = 60;
                        if (!this.timer) {
                            this.count = TIME_COUNT;
                            this.codeshow = false;
                            this.clickFalg = 0;
                            this.timer = setInterval(() => {
                                if (
                                    this.count > 0 &&
                                    this.count <= TIME_COUNT
                                ) {
                                    this.count--;
                                } else {
                                    this.codeshow = true;
                                    clearInterval(this.timer);
                                    this.timer = null;
                                }
                            }, 1000);
                        } else {
                            Toast(result.msg);
                            // this.texts = result.msg
                            // this.elType = "success"
                            // this.alertShow = true
                            // setTimeout(() => {
                            //   this.alertShow = false
                            //   this.elType = "warning"
                            // }, 2000)
                        }
                    } else {
                        // this.texts = result.msg
                        // this.alertShow = true
                        // setTimeout(() => {
                        //   this.alertShow = false
                        // }, 2000)
                        Toast(result.msg);
                    }
                }
            }
        },
        async gook() {
            this.dengl = true;
            if (!this.checked) {
                Toast("需同意注册协议才能注册!");
                return;
            }
            setTimeout(() => {
                this.dengl = false;
            }, 1000);
            if (this.loginBtn) {
                return;
            }
            this.loginBtn = true;
            // 注册
            // if (!this.agree) {
            //   Toast('需同意注册协议才能注册!')
            //   this.loginBtn = false;
            // } else
            if (isNull(this.phone) || !isPhone(this.phone)) {
                // this.texts = this.$t('hj28')
                // this.alertShow = true
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(this.$t("hj28"));
                this.loginBtn = false;
            } else if (isNull(this.userPassword)) {
                // this.texts = this.$t('hj30')
                // this.alertShow = true
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(this.$t("hj30"));
                this.loginBtn = false;
            } else if (isNull(this.rePassword)) {
                // this.texts = this.$t('hj31')
                // this.alertShow = true
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(this.$t("hj31"));
                this.loginBtn = false;
            } else {
                if (this.userPassword !== this.rePassword) {
                    // this.texts = this.$t('hj32')
                    // this.alertShow = true
                    // setTimeout(() => {
                    //   this.alertShow = false
                    // }, 2000)
                    Toast(this.$t("hj32"));
                    this.password = 0;
                    this.password2 = 0;
                    this.loginBtn = false;
                } else if (!pwdReg(this.userPassword)) {
                    // this.texts = this.$t('hj19')
                    // this.alertShow = true
                    // setTimeout(() => {
                    //   this.alertShow = false
                    // }, 2000)
                    Toast(this.$t("hj19"));
                    this.loginBtn = false;
                } else if (isNull(this.userName)) {
                    // this.texts = this.$t('hj33')
                    // this.alertShow = true
                    // setTimeout(() => {
                    //   this.alertShow = false
                    // }, 2000)
                    Toast(this.$t("hj33"));
                    this.loginBtn = false;
                } else {
                    let opts = {
                        // agentCode:'4023', // SR330001
                        phone: this.phone,
                        yzmCode: "6666",
                        userPwd: this.userPassword,
                        agentCode: this.userName,
                    };
                    let data = await api.register(opts);
                    if (data.status === 0) {
                        // this.texts = this.$t('hj34')
                        // this.elType = "success"
                        // this.alertShow = true
                        Toast(this.$t("hj34"));
                        setTimeout(() => {
                            this.$router.push("/login");
                            // this.alertShow = false
                            // this.elType = "warning"
                        }, 1000);
                        this.loginBtn = false;
                    } else {
                        Toast(data.msg);
                        // this.texts = data.msg
                        // this.alertShow = true
                        // setTimeout(() => {
                        //   this.alertShow = false
                        // }, 2000)
                        this.loginBtn = false;
                    }
                }
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
            //   if (isNull(this.code)) {
            //     this.texts="请输入验证码"
            // this.alertShow=true
            // setTimeout(()=>{
            //   this.alertShow=false
            // },2000)
            //     this.loginBtn = false;
            //   } else
        },
        isAgree() {
            this.agree = !this.agree;
        },
        async checkPhone() {
            // 先验证是否已经注册
            let data = await api.checkPhone({ phoneNum: this.phone });
            if (data.status === 0) {
                // 如果用户已存在返回 0
                Toast(this.$t("hj35"));
                // this.texts = this.$t('hj35')
                //     this.alertShow = true
                //     setTimeout(() => {
                //       this.alertShow = false
                //     }, 2000)
                this.$router.push("/login");
            } else {
                // this.dialogShow = false
                // this.adminUrl = process.env.API_HOST
                // if (this.adminUrl === undefined) {
                //   this.adminUrl = ''
                // }
                // this.gook()
                this.getcode();
            }
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    background: #fff;
    min-height: 100vh;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .form {
        padding: 0 0.9302rem;

        .form_item {
            display: flex;
            background: #f6f6f6;
            margin-top: 0.3488rem;
            padding: 0.3488rem;

            .form_label {
                font-size: 0.372rem;
                width: 2.3255rem;

                span:nth-of-type(1) {
                    color: #e82020;
                }
            }

            .form_input {
                margin-left: 0.2325rem;
                flex: 1;

                input {
                    width: 100%;
                }
            }
        }
    }

    .chbox {
        margin: 0.3488rem 0.9302rem;
    }

    .cbtn {
        .reg_btn {
            background: rgba(238, 0, 17, 1);
            color: #fff;
            text-align: center;
            height: 1.0232rem;
            line-height: 1.0232rem;
            border-radius: 0.2325rem;
            margin: 0.3488rem 0.9302rem;
        }

        .login_btn {
            text-align: center;
            height: 1.0232rem;
            line-height: 1.0232rem;
            border-radius: 0.2325rem;
            margin: 0.3488rem 0.9302rem;
            border: solid 1px #e1e1e1;
        }
    }

    .service {
        padding: 0.3488rem 0.9302rem;
        padding-top: 0;

        img {
            width: 0.7906rem;
            height: 0.7906rem;
            float: right;
        }
    }
}
</style>