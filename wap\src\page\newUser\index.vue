<template>
    <div class="container">
        <div class="header">
            <!-- <van-nav-bar title="个人中心" fixed @click-right="$router.push({ path: '/setting' })">
                <template #right>
                    
                </template>
            </van-nav-bar> -->
            <van-icon name="setting" color="#1e2433" @click="$router.push({ path: '/setting' })" />
        </div>
        <div class="top">
            <div class="avatar">
                <img src="~@/assets/images/qiquan26/17336181294013AA00582.png" />
            </div>
            <div class="info">
                <div class="text_1">
                    <div>{{ maskPhoneNumber(userInfo.phone ? userInfo.phone : userInfo.phone) }}</div>
                    <div :class="`text_1_status ${userInfo.isActive == 2 ? 'success' : ''}`">{{userInfo.isActive == 2 ? '已实名' : '未实名'}}</div>
                </div>
                <div class="text_2">{{ maskRealName(userInfo.realName) }}</div>
            </div>
        </div>
        <div class="layout">
            <div class="wallet">
                <div class="wallet_container">
                    <div class="title">
                        <div class="text">总市值 <span class="eye-icon" @click="toggleMoneyDisplay">
                                <van-icon :name="isShowMoney ? 'eye-o' : 'closed-eye'" size="16" />
                            </span></div>
                        <div class="item">
                            <div class="item_price">{{ isShowMoney ? useFormatMoney(($store.state.userInfo.userAmt || 0)) : '******'
              }}
                            </div>
                            <div class="item_text">CNY</div>
                        </div>
                        <!-- <div class="eye-icon" @click="toggleMoneyDisplay">
              <van-icon :name="isShowMoney ? 'eye-o' : 'closed-eye'" size="20" />
            </div> -->
                    </div>
                    <div class="radio">
                        <div class="item">
                            <p>可用余额</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.enableAmt || 0) : '******' }}</span>
                        </div>
                        <div class="item">
                            <p>新股申购</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.newStockAmount || 0) : '******' }}</span>
                        </div>
                        <div class="item">
                            <p>可取金额</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.withdrawFunds || 0) : '******' }}</span>
                        </div>
                        <div class="item">
                            <p>总盈亏</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.accountAllProfitAndLose || 0) : '******'
              }}</span>
                        </div>
                        <div class="item">
                            <p>浮动盈亏</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.allProfitAndLose || 0) : '******' }}</span>
                        </div>
                        <div class="item">
                            <p>持仓总市值</p>
                            <span>{{ isShowMoney ? useFormatMoney($store.state.userInfo.allFreezAmt || 0) : '******' }}</span>
                        </div>
                        <div class="clear"></div>
                    </div>
                    <div class="action">
                        <div class="abtn" @click="$router.push('/recharge')">银转证</div>
                        <div class="abtn" @click="$router.push('/withdraw')">证转银</div>
                    </div>
                </div>
            </div>
            <div class="item_menu">
                <div class="item" @click="$router.push('/chicang')">
                    <img src="~@/assets/images/qiquan26/chicang.png" alt="">
                    <div>我的持仓</div>
                </div>
                <div class="item" @click="$router.push('/peishouhistory?type=1')">
                    <img src="~@/assets/images/qiquan26/new-sg-record.png" alt="">
                    <div>打新记录</div>
                </div>
                <div class="item" @click="$router.push('/peishouhistory')">
                    <img src="~@/assets/images/qiquan26/history.png" alt="">
                    <div>配售记录</div>
                </div>
                <div class="item" @click="$router.push('/dazong?type=2')">
                    <img src="~@/assets/images/qiquan26/dazongjy.png" alt="">
                    <div>天启护盘</div>
                </div>
            </div>
            <div class="menu_list">
                <div class="item" @click="$router.push('/chicang?type=2')">
                    <img src="~@/assets/images/qiquan26/icon-history.png" class="icon">
                    <span>历史持仓</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push('/chicang?type=3')">
                    <img src="~@/assets/images/qiquan26/icon-weituo.png" class="icon">
                    <span>委托记录</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push('/smrz')">
                    <img src="~@/assets/images/qiquan26/icon-real.png" class="icon">
                    <span>实名认证</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push('/bankCard')">
                    <img src="~@/assets/images/qiquan26/icon-bankcard.png" class="icon">
                    <span>银行卡</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push(`/accountOpeningContract?id=${userInfo.id}`)">
                    <img src="~@/assets/images/qiquan26/bb2.png" class="icon">
                    <span>线上合同</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push('/FundingDetails')">
                    <img src="~@/assets/images/qiquan26/icon-bankhistory.png" class="icon">
                    <span>银证记录</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="item" @click="$router.push('/setPassword')">
                    <img src="~@/assets/images/qiquan26/icon-pay.png" class="icon">
                    <span>支付密码</span>
                    <div class="arrow">
                        <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4265" width="64" height="64">
                            <path d="M716.617 477.941L355.519 142.045c-14.661-13.091-37.097-12.05-50.488 2.341-13.389 14.392-12.811 36.845 1.306 50.527L639.633 504.95 305.797 828.643a36.097 36.097 0 0 0-9.874 34.718 36.098 36.098 0 0 0 25.137 25.907 36.093 36.093 0 0 0 35.004-8.81l361.099-350.122a36.056 36.056 0 0 0 10.981-26.294 36.052 36.052 0 0 0-11.527-26.063" fill="#333333" p-id="4266"></path>
                        </svg>
                    </div>
                    <div class="clear"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { Toast, MessageBox } from "mint-ui";
import { isNull, pwdReg } from "@/utils/utils";
import { compress } from "@/utils/imgupload";
import { Toast as VantToast } from "vant";

export default {
    name: "newUser",
    data() {
        return {
            name: "",
            selectUserFlag: true,
            settingDialog: false,
            showPopover: false,
            oldPassword: "", // 旧密码
            newPassword: "", // 新密码
            cirNewPassword: "", // 确认新密码
            userInfo: [],
            actions: [],
            onlineService: "",
            currentRate: 100,
            rate: 0,
            showBtn: true,
            isShowMoney: true, // 控制金额显示状态
        };
    },
    components: {},
    created() {
        this.getUserInfo();
        this.getInfoSite();
    },
    methods: {
        // 切换金额显示状态
        toggleMoneyDisplay() {
            this.isShowMoney = !this.isShowMoney;
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        useFormatMoney(price, useCurrencySymbol = true, currency = "CNY") {
            const options = {
                minimumFractionDigits: 2, // 最少显示 0 位小数
                maximumFractionDigits: 6, // 最多显示 6 位小数
            };
            if (useCurrencySymbol) {
                options.style = "currency";
                options.currency = currency;
            }
            const number = Number(price || 0); // 确保 price 为数字，即使为 0
            if (isNaN(number)) {
                throw new Error(
                    "Invalid input: price must be a number--->" + price
                );
            }
            // 格式化数字，根据是否包含货币符号
            return number.toLocaleString(undefined, options);
        },
        onSelect() {},
        goOnline() {
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
            this.$router.push("/service");
        },
        async getInfoSite() {
            let data = await api.getInfoSite();
            if (data.status === 0) {
                this.onlineService = data.data.onlineService;
            } else {
                Toast(data.msg);
            }
        },
        goWall() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/wallet");
        },
        handleZh() {
            this.selectUserFlag = !this.selectUserFlag;

            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        async getUserInfo() {
            // if (this.$posthog) {
            //   const userPhone = window.localStorage.getItem("phone");
            //   console.log(userPhone)
            //     this.$posthog.identify(
            //       `东吴-${userPhone}`,  // Replace 'distinct_id' with your user's unique identifier
            //       { email: `东吴-${userPhone}`, name:`东吴-${userPhone}`} // optional: set additional person properties
            //     );
            //   }

            // 获取用户信息
            const toastInfo = VantToast.loading({
                message: "加载中...",
                duration: 0,
                forbidClick: true,
            });
            let data = await api.getUserInfo();
            toastInfo.clear();
            if (data.status === 0) {
                // 判断是否登录
                this.$store.commit("dialogVisible", false);
                this.$store.state.userInfo = data.data;
                this.userInfo = data.data;
                this.rate = (data.data.enableAmt / data.data.userAmt) * 100;
                if (data.data.isActive === 1 || data.data.isActive === 2) {
                    this.showBtn = false;
                }
            } else {
                this.$store.commit("dialogVisible", true);
            }
        },
        goToTopUp() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
            this.$router.push("/wallet");
        },
        handleOutLoginClick() {
            // 退出登录
            MessageBox.confirm(this.$t("hj149") + "?", this.$t("hj165"), {
                confirmButtonText: this.$t("hj161"),
                cancelButtonText: this.$t("hj106"),
            })
                .then(() => {
                    this.toRegister();
                })
                .catch(() => {});
        },
        goToSettings() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            // 每次打开dialog 清空密码数据
            this.settingDialog = !this.settingDialog;
            if (this.settingDialog) {
                this.oldPassword = "";
                this.newPassword = "";
                this.cirNewPassword = "";
            }
        },
        handleGoToTransfer() {
            if (this.userInfo.length == 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/transfers");
        },
        handleGoToAuthentication() {
            if (this.userInfo.length == 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/authentications");
        },
        handleGoToBankCard() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/bankCard");
        },
        async toRegister() {
            // 注销登录
            window.localStorage.removeItem("USERTOKEN"); // 清空本地存储 USERTOKEN字段
            this.clearCookie();
            let data = await api.logout();
            if (data.status === 0) {
                // Toast(data.msg)
                this.$router.push("/login");
            } else {
                Toast(data.msg);
            }
            this.$router.push("/login");
        },
        async changeLoginPsd() {
            // 修改密码
            if (
                isNull(this.oldPassword) ||
                isNull(this.newPassword) ||
                isNull(this.cirNewPassword)
            ) {
                Toast(this.$t("hj154"));
                this.settingDialog = false;
            } else if (!pwdReg(this.newPassword)) {
                Toast(this.$t("hj19"));
                this.settingDialog = false;
            } else {
                // 修改密码
                if (this.newPassword === this.cirNewPassword) {
                    let opts = {
                        oldPwd: this.oldPassword,
                        newPwd: this.newPassword,
                    };
                    let data = await api.changePassword(opts);
                    if (data.status === 0) {
                        this.changeLoginPsdBox = false;
                        Toast(data.msg);
                        this.settingDialog = false;
                    } else {
                        Toast(data.msg);
                        this.settingDialog = false;
                    }
                } else {
                    Toast(this.$t("hj155"));
                    this.settingDialog = false;
                }
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        maskPhoneNumber(phoneNumber) {
            if (!phoneNumber) return "";
            return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
        },
        maskRealName(name) {
            if (!name) return "";
            if (name.length === 2) {
                // 如果是两个字，第二个字变成*
                return name.charAt(0) + "*";
            } else if (name.length > 2) {
                // 如果两个字以上，前后不变，中间变成*
                const firstChar = name.charAt(0);
                const lastChar = name.charAt(name.length - 1);
                const middleStars = "*".repeat(name.length - 2);
                return firstChar + middleStars + lastChar;
            }
            return name;
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    // font-size: 0.32558139534883723rem;
    padding: 0;
    padding-bottom: calc(1.3488rem + 0.4651rem);

    .header {
        height: 1.0698rem;
        font-size: 0.4651rem;
        display: flex;
        align-items: center;
        justify-content: end;
        .van-icon {
            margin-right: 0.3488rem;
        }
    }

    .top {
        // background: #fff;
        // position: fixed;
        // left: 0;
        // right: 0;
        // top: 1.0698rem;
        padding: 0.3488rem;
        display: flex;
        align-items: center;
        z-index: 9999;

        .avatar {
            img {
                width: 1.3488rem;
                height: 1.3488rem;
                border-radius: 50%;
            }
        }

        .info {
            margin-left: 0.2326rem;
            line-height: 0.4651rem;

            .text_1 {
                font-size: 0.3721rem;
                font-weight: 700;
                display: flex;
                align-items: center;
                .text_1_status {
                    border: solid 1px rgba(224, 57, 54, 1);
                    color: rgba(224, 57, 54, 1);
                    font-size: 0.279rem;
                    margin-left: 0.1162rem;
                    border-radius: 0.2325rem;
                    padding: 0.1162rem;
                    &.success {
                        border-color: rgba(14, 201, 177, 1);
                        color: rgba(14, 201, 177, 1);
                    }
                }
            }
        }
    }

    .layout {
        // padding-top: 2.1395rem;
        padding-top: 0.3488rem;

        .wallet {
            color: #fff;
            margin: 0.3488rem;
            margin-bottom: 0;

            .wallet_container {
                position: relative;
                background: url("data:image/png;base64,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");
                font-size: 0.3255rem;
                border-radius: 0.2326rem;
                padding: 0.3488rem;
                color: rgba(255, 255, 255, 1);
                box-shadow: 0px 0px 15px #e15251;

                .action {
                    position: absolute;
                    right: 0.3488rem;
                    top: 0.3488rem;
                    display: flex;

                    .abtn {
                        background: linear-gradient(
                            180deg,
                            rgba(255, 255, 255, 1) 0%,
                            rgba(255, 255, 255, 0.85) 100%
                        );
                        box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
                        color: rgba(215, 12, 24, 1);
                        width: 1.7442rem;
                        height: 0.6511rem;
                        line-height: 0.6511rem;
                        border-radius: 0.2326rem;
                        text-align: center;

                        &:last-of-type {
                            margin-left: 0.3255rem;
                        }
                    }
                }

                .title {
                    .text {
                        font-weight: 700;
                    }

                    .item {
                        margin-top: 0.2326rem;
                        line-height: 0.4651rem;

                        .item_price {
                            font-weight: 700;
                        }

                        .item_text {
                            line-height: 0.4651rem;
                            display: block;
                            font-weight: 700;
                        }
                    }

                    .eye-icon {
                        // position: absolute;
                        // right: 0.3488rem;
                        // top: 0.3488rem;
                        color: #fff;
                        z-index: 1;
                    }
                }

                .radio {
                    .item {
                        width: calc(100% / 3);
                        float: left;
                        line-height: 0.4651rem;

                        p {
                            font-weight: 700;
                        }

                        span {
                            line-height: 0.4651rem;
                            display: block;
                            font-weight: 700;
                        }

                        &:nth-of-type(4),
                        &:nth-of-type(5),
                        &:nth-of-type(6) {
                            margin-top: 0.2326rem;
                        }

                        &:nth-of-type(2),
                        &:nth-of-type(5) {
                            text-align: center;
                        }

                        &:nth-of-type(3),
                        &:nth-of-type(6) {
                            text-align: right;
                        }
                    }

                    .clear {
                        clear: both;
                    }
                }
            }
        }

        .item_menu {
            // box-shadow: 0 0 0.3488rem #0003;
            background: #fff;
            margin: 0.3488rem;
            margin-bottom: 0;
            display: flex;
            padding: 0.3488rem 0;
            border-radius: 0.2326rem;

            .item {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;

                div {
                    margin-top: 0.2326rem;
                    font-size: 0.3255rem;
                }

                img {
                    display: block;
                    width: 0.6977rem;
                    height: 0.6977rem;
                }
            }
        }

        .menu_list {
            background: #fff;
            margin-top: 0.3488rem;
            .item {
                // box-shadow: 0 0 0.3488rem #0003;
                // margin-top: 0.3488rem;
                // padding: 0.3488rem;
                // border-radius: 0.2326rem;
                height: 1.1627rem;
                display: flex;
                align-items: center;
                padding: 0 0.3488rem;
                font-size: 0.3255rem;
                span {
                    flex: 1;
                    margin-left: 0.3488rem;
                }

                img {
                    width: 0.5581rem;
                    // height: 0.5581rem;
                }

                .arrow {
                    svg {
                        width: 0.3255rem;
                        height: 0.3255rem;
                    }
                }

                // img {
                //     float: left;
                //     width: 0.3488rem;
                //     height: 0.3488rem;
                // }

                // span {
                //     float: left;
                //     display: block;
                //     line-height: 0.3488rem;
                //     margin-left: 0.3488rem;
                // }

                // .arrow {
                //     float: right;

                //     svg {
                //         width: 0.3488rem;
                //         height: 0.3488rem;
                //     }
                // }
            }
        }
    }
}
</style>
