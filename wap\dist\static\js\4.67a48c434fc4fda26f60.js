webpackJsonp([4],{"1nuA":function(e,t,r){"use strict";t.decode=t.parse=r("kMPS"),t.encode=t.stringify=r("xaZU")},2:function(e,t){},3:function(e,t){},"3IRH":function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},4:function(e,t){},"4WTo":function(e,t,r){var n=r("NWt+");e.exports=function(e,t){var r=[];return n(e,!1,r.push,r,t),r}},5:function(e,t){},"7Doy":function(e,t,r){var n=r("EqjI"),i=r("7UMu"),a=r("dSzd")("species");e.exports=function(e){var t;return i(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!i(t.prototype)||(t=void 0),n(t)&&null===(t=t[a])&&(t=void 0)),void 0===t?Array:t}},"8O0F":function(e,t){},"9Bbf":function(e,t,r){"use strict";var n=r("kM2E");e.exports=function(e){n(n.S,e,{of:function(){for(var e=arguments.length,t=new Array(e);e--;)t[e]=arguments[e];return new this(t)}})}},"9C8M":function(e,t,r){"use strict";var n=r("evD5").f,i=r("Yobk"),a=r("xH/j"),o=r("+ZMJ"),s=r("2KxR"),u=r("NWt+"),c=r("vIB/"),l=r("EGZi"),h=r("bRrM"),f=r("+E39"),d=r("06OY").fastKey,p=r("LIJb"),v=f?"_s":"size",m=function(e,t){var r,n=d(t);if("F"!==n)return e._i[n];for(r=e._f;r;r=r.n)if(r.k==t)return r};e.exports={getConstructor:function(e,t,r,c){var l=e(function(e,n){s(e,l,t,"_i"),e._t=t,e._i=i(null),e._f=void 0,e._l=void 0,e[v]=0,void 0!=n&&u(n,r,e[c],e)});return a(l.prototype,{clear:function(){for(var e=p(this,t),r=e._i,n=e._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete r[n.i];e._f=e._l=void 0,e[v]=0},delete:function(e){var r=p(this,t),n=m(r,e);if(n){var i=n.n,a=n.p;delete r._i[n.i],n.r=!0,a&&(a.n=i),i&&(i.p=a),r._f==n&&(r._f=i),r._l==n&&(r._l=a),r[v]--}return!!n},forEach:function(e){p(this,t);for(var r,n=o(e,arguments.length>1?arguments[1]:void 0,3);r=r?r.n:this._f;)for(n(r.v,r.k,this);r&&r.r;)r=r.p},has:function(e){return!!m(p(this,t),e)}}),f&&n(l.prototype,"size",{get:function(){return p(this,t)[v]}}),l},def:function(e,t,r){var n,i,a=m(e,t);return a?a.v=r:(e._l=a={i:i=d(t,!0),k:t,v:r,p:n=e._l,n:void 0,r:!1},e._f||(e._f=a),n&&(n.n=a),e[v]++,"F"!==i&&(e._i[i]=a)),e},getEntry:m,setStrong:function(e,t,r){c(e,t,function(e,r){this._t=p(e,t),this._k=r,this._l=void 0},function(){for(var e=this._k,t=this._l;t&&t.r;)t=t.p;return this._t&&(this._l=t=t?t.n:this._t._f)?l(0,"keys"==e?t.k:"values"==e?t.v:[t.k,t.v]):(this._t=void 0,l(1))},r?"entries":"values",!r,!0),h(t)}}},ALrJ:function(e,t,r){var n=r("+ZMJ"),i=r("MU5D"),a=r("sB3e"),o=r("QRG4"),s=r("oeOm");e.exports=function(e,t){var r=1==e,u=2==e,c=3==e,l=4==e,h=6==e,f=5==e||h,d=t||s;return function(t,s,p){for(var v,m,g=a(t),y=i(g),_=n(s,p,3),b=o(y.length),A=0,S=r?d(t,b):u?d(t,0):void 0;b>A;A++)if((f||A in y)&&(m=_(v=y[A],A,g),e))if(r)S[A]=m;else if(m)switch(e){case 3:return!0;case 5:return v;case 6:return A;case 2:S.push(v)}else if(l)return!1;return h?-1:c||l?l:S}}},BDhv:function(e,t,r){var n=r("kM2E");n(n.P+n.R,"Set",{toJSON:r("m9gC")("Set")})},EKta:function(e,t,r){"use strict";t.byteLength=function(e){var t=c(e),r=t[0],n=t[1];return 3*(r+n)/4-n},t.toByteArray=function(e){var t,r,n=c(e),o=n[0],s=n[1],u=new a(function(e,t,r){return 3*(t+r)/4-r}(0,o,s)),l=0,h=s>0?o-4:o;for(r=0;r<h;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],u[l++]=t>>16&255,u[l++]=t>>8&255,u[l++]=255&t;2===s&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,u[l++]=255&t);1===s&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,u[l++]=t>>8&255,u[l++]=255&t);return u},t.fromByteArray=function(e){for(var t,r=e.length,i=r%3,a=[],o=0,s=r-i;o<s;o+=16383)a.push(l(e,o,o+16383>s?s:o+16383));1===i?(t=e[r-1],a.push(n[t>>2]+n[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],a.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"="));return a.join("")};for(var n=[],i=[],a="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,u=o.length;s<u;++s)n[s]=o[s],i[o.charCodeAt(s)]=s;function c(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function l(e,t,r){for(var i,a,o=[],s=t;s<r;s+=3)i=(e[s]<<16&16711680)+(e[s+1]<<8&65280)+(255&e[s+2]),o.push(n[(a=i)>>18&63]+n[a>>12&63]+n[a>>6&63]+n[63&a]);return o.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},EuP9:function(e,t,r){"use strict";(function(e){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var n=r("EKta"),i=r("ujcs"),a=r("sOR5");function o(){return u.TYPED_ARRAY_SUPPORT?**********:**********}function s(e,t){if(o()<t)throw new RangeError("Invalid typed array length");return u.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=u.prototype:(null===e&&(e=new u(t)),e.length=t),e}function u(e,t,r){if(!(u.TYPED_ARRAY_SUPPORT||this instanceof u))return new u(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return h(this,e)}return c(this,e,t,r)}function c(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n);u.TYPED_ARRAY_SUPPORT?(e=t).__proto__=u.prototype:e=f(e,t);return e}(e,t,r,n):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!u.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|p(t,r),i=(e=s(e,n)).write(t,r);i!==n&&(e=e.slice(0,i));return e}(e,t,r):function(e,t){if(u.isBuffer(t)){var r=0|d(t.length);return 0===(e=s(e,r)).length?e:(t.copy(e,0,0,r),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(n=t.length)!=n?s(e,0):f(e,t);if("Buffer"===t.type&&a(t.data))return f(e,t.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function l(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function h(e,t){if(l(t),e=s(e,t<0?0:0|d(t)),!u.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function f(e,t){var r=t.length<0?0:0|d(t.length);e=s(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function d(e){if(e>=o())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|e}function p(e,t){if(u.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return U(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return W(e).length;default:if(n)return U(e).length;t=(""+t).toLowerCase(),n=!0}}function v(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function m(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=u.from(t,n)),u.isBuffer(t))return 0===t.length?-1:g(e,t,r,n,i);if("number"==typeof t)return t&=255,u.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):g(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function g(e,t,r,n,i){var a,o=1,s=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;o=2,s/=2,u/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var l=-1;for(a=r;a<s;a++)if(c(e,a)===c(t,-1===l?0:a-l)){if(-1===l&&(l=a),a-l+1===u)return l*o}else-1!==l&&(a-=a-l),l=-1}else for(r+u>s&&(r=s-u),a=r;a>=0;a--){for(var h=!0,f=0;f<u;f++)if(c(e,a+f)!==c(t,f)){h=!1;break}if(h)return a}return-1}function y(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=t.length;if(a%2!=0)throw new TypeError("Invalid hex string");n>a/2&&(n=a/2);for(var o=0;o<n;++o){var s=parseInt(t.substr(2*o,2),16);if(isNaN(s))return o;e[r+o]=s}return o}function _(e,t,r,n){return B(U(t,e.length-r),e,r,n)}function b(e,t,r,n){return B(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function A(e,t,r,n){return b(e,t,r,n)}function S(e,t,r,n){return B(W(t),e,r,n)}function w(e,t,r,n){return B(function(e,t){for(var r,n,i,a=[],o=0;o<e.length&&!((t-=2)<0);++o)r=e.charCodeAt(o),n=r>>8,i=r%256,a.push(i),a.push(n);return a}(t,e.length-r),e,r,n)}function k(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function x(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var a,o,s,u,c=e[i],l=null,h=c>239?4:c>223?3:c>191?2:1;if(i+h<=r)switch(h){case 1:c<128&&(l=c);break;case 2:128==(192&(a=e[i+1]))&&(u=(31&c)<<6|63&a)>127&&(l=u);break;case 3:a=e[i+1],o=e[i+2],128==(192&a)&&128==(192&o)&&(u=(15&c)<<12|(63&a)<<6|63&o)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:a=e[i+1],o=e[i+2],s=e[i+3],128==(192&a)&&128==(192&o)&&128==(192&s)&&(u=(15&c)<<18|(63&a)<<12|(63&o)<<6|63&s)>65535&&u<1114112&&(l=u)}null===l?(l=65533,h=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=h}return function(e){var t=e.length;if(t<=C)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=C));return r}(n)}t.Buffer=u,t.SlowBuffer=function(e){+e!=e&&(e=0);return u.alloc(+e)},t.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=o(),u.poolSize=8192,u._augment=function(e){return e.__proto__=u.prototype,e},u.from=function(e,t,r){return c(null,e,t,r)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(e,t,r){return function(e,t,r,n){return l(t),t<=0?s(e,t):void 0!==r?"string"==typeof n?s(e,t).fill(r,n):s(e,t).fill(r):s(e,t)}(null,e,t,r)},u.allocUnsafe=function(e){return h(null,e)},u.allocUnsafeSlow=function(e){return h(null,e)},u.isBuffer=function(e){return!(null==e||!e._isBuffer)},u.compare=function(e,t){if(!u.isBuffer(e)||!u.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(e,t){if(!a(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return u.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=u.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(!u.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},u.byteLength=p,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)v(this,t,t+1);return this},u.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)v(this,t,t+3),v(this,t+1,t+2);return this},u.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)v(this,t,t+7),v(this,t+1,t+6),v(this,t+2,t+5),v(this,t+3,t+4);return this},u.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?x(this,0,e):function(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return T(this,t,r);case"utf8":case"utf-8":return x(this,t,r);case"ascii":return P(this,t,r);case"latin1":case"binary":return R(this,t,r);case"base64":return k(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}.apply(this,arguments)},u.prototype.equals=function(e){if(!u.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===u.compare(this,e)},u.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},u.prototype.compare=function(e,t,r,n,i){if(!u.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var a=i-n,o=r-t,s=Math.min(a,o),c=this.slice(n,i),l=e.slice(t,r),h=0;h<s;++h)if(c[h]!==l[h]){a=c[h],o=l[h];break}return a<o?-1:o<a?1:0},u.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},u.prototype.indexOf=function(e,t,r){return m(this,e,t,r,!0)},u.prototype.lastIndexOf=function(e,t,r){return m(this,e,t,r,!1)},u.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var a=!1;;)switch(n){case"hex":return y(this,e,t,r);case"utf8":case"utf-8":return _(this,e,t,r);case"ascii":return b(this,e,t,r);case"latin1":case"binary":return A(this,e,t,r);case"base64":return S(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return w(this,e,t,r);default:if(a)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),a=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var C=4096;function P(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function R(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function T(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",a=t;a<r;++a)i+=q(e[a]);return i}function E(e,t,r){for(var n=e.slice(t,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}function I(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function O(e,t,r,n,i,a){if(!u.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<a)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function L(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,a=Math.min(e.length-r,2);i<a;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function F(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,a=Math.min(e.length-r,4);i<a;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function j(e,t,r,n,i,a){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function N(e,t,r,n,a){return a||j(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function M(e,t,r,n,a){return a||j(e,0,r,8),i.write(e,t,r,n,52,8),r+8}u.prototype.slice=function(e,t){var r,n=this.length;if(e=~~e,t=void 0===t?n:~~t,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),t<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),u.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=u.prototype;else{var i=t-e;r=new u(i,void 0);for(var a=0;a<i;++a)r[a]=this[a+e]}return r},u.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||I(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n},u.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||I(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},u.prototype.readUInt8=function(e,t){return t||I(e,1,this.length),this[e]},u.prototype.readUInt16LE=function(e,t){return t||I(e,2,this.length),this[e]|this[e+1]<<8},u.prototype.readUInt16BE=function(e,t){return t||I(e,2,this.length),this[e]<<8|this[e+1]},u.prototype.readUInt32LE=function(e,t){return t||I(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},u.prototype.readUInt32BE=function(e,t){return t||I(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},u.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||I(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},u.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||I(e,t,this.length);for(var n=t,i=1,a=this[e+--n];n>0&&(i*=256);)a+=this[e+--n]*i;return a>=(i*=128)&&(a-=Math.pow(2,8*t)),a},u.prototype.readInt8=function(e,t){return t||I(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},u.prototype.readInt16LE=function(e,t){t||I(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(e,t){t||I(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(e,t){return t||I(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},u.prototype.readInt32BE=function(e,t){return t||I(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},u.prototype.readFloatLE=function(e,t){return t||I(e,4,this.length),i.read(this,e,!0,23,4)},u.prototype.readFloatBE=function(e,t){return t||I(e,4,this.length),i.read(this,e,!1,23,4)},u.prototype.readDoubleLE=function(e,t){return t||I(e,8,this.length),i.read(this,e,!0,52,8)},u.prototype.readDoubleBE=function(e,t){return t||I(e,8,this.length),i.read(this,e,!1,52,8)},u.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||O(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,a=0;for(this[t]=255&e;++a<r&&(i*=256);)this[t+a]=e/i&255;return t+r},u.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||O(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,a=1;for(this[t+i]=255&e;--i>=0&&(a*=256);)this[t+i]=e/a&255;return t+r},u.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,1,255,0),u.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},u.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):L(this,e,t,!0),t+2},u.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):L(this,e,t,!1),t+2},u.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):F(this,e,t,!0),t+4},u.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):F(this,e,t,!1),t+4},u.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);O(this,e,t,r,i-1,-i)}var a=0,o=1,s=0;for(this[t]=255&e;++a<r&&(o*=256);)e<0&&0===s&&0!==this[t+a-1]&&(s=1),this[t+a]=(e/o>>0)-s&255;return t+r},u.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);O(this,e,t,r,i-1,-i)}var a=r-1,o=1,s=0;for(this[t+a]=255&e;--a>=0&&(o*=256);)e<0&&0===s&&0!==this[t+a+1]&&(s=1),this[t+a]=(e/o>>0)-s&255;return t+r},u.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,1,127,-128),u.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},u.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):L(this,e,t,!0),t+2},u.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):L(this,e,t,!1),t+2},u.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,**********,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):F(this,e,t,!0),t+4},u.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):F(this,e,t,!1),t+4},u.prototype.writeFloatLE=function(e,t,r){return N(this,e,t,!0,r)},u.prototype.writeFloatBE=function(e,t,r){return N(this,e,t,!1,r)},u.prototype.writeDoubleLE=function(e,t,r){return M(this,e,t,!0,r)},u.prototype.writeDoubleBE=function(e,t,r){return M(this,e,t,!1,r)},u.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,a=n-r;if(this===e&&r<t&&t<n)for(i=a-1;i>=0;--i)e[i+t]=this[i+r];else if(a<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<a;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+a),t);return a},u.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var a;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(a=t;a<r;++a)this[a]=e;else{var o=u.isBuffer(e)?e:U(new u(e,n).toString()),s=o.length;for(a=0;a<r-t;++a)this[a+t]=o[a%s]}return this};var D=/[^+\/0-9A-Za-z-_]/g;function q(e){return e<16?"0"+e.toString(16):e.toString(16)}function U(e,t){var r;t=t||1/0;for(var n=e.length,i=null,a=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&a.push(239,191,189);continue}if(o+1===n){(t-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return a}function W(e){return n.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(D,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function B(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}}).call(t,r("DuR2"))},GIdV:function(e,t){},GX5a:function(e,t){},HpRW:function(e,t,r){"use strict";var n=r("kM2E"),i=r("lOnJ"),a=r("+ZMJ"),o=r("NWt+");e.exports=function(e){n(n.S,e,{from:function(e){var t,r,n,s,u=arguments[1];return i(this),(t=void 0!==u)&&i(u),void 0==e?new this:(r=[],t?(n=0,s=a(u,arguments[2],2),o(e,!1,function(e){r.push(s(e,n++))})):o(e,!1,r.push,r),new this(r))}})}},LIJb:function(e,t,r){var n=r("EqjI");e.exports=function(e,t){if(!n(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},MsCo:function(e,t,r){(function(e,n){var i;/*! https://mths.be/punycode v1.4.1 by @mathias */!function(a){"object"==typeof t&&t&&t.nodeType,"object"==typeof e&&e&&e.nodeType;var o="object"==typeof n&&n;o.global!==o&&o.window!==o&&o.self;var s,u=**********,c=36,l=1,h=26,f=38,d=700,p=72,v=128,m="-",g=/^xn--/,y=/[^\x20-\x7E]/,_=/[\x2E\u3002\uFF0E\uFF61]/g,b={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},A=c-l,S=Math.floor,w=String.fromCharCode;function k(e){throw new RangeError(b[e])}function x(e,t){for(var r=e.length,n=[];r--;)n[r]=t(e[r]);return n}function C(e,t){var r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+x((e=e.replace(_,".")).split("."),t).join(".")}function P(e){for(var t,r,n=[],i=0,a=e.length;i<a;)(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<a?56320==(64512&(r=e.charCodeAt(i++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),i--):n.push(t);return n}function R(e){return x(e,function(e){var t="";return e>65535&&(t+=w((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=w(e)}).join("")}function T(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function E(e,t,r){var n=0;for(e=r?S(e/d):e>>1,e+=S(e/t);e>A*h>>1;n+=c)e=S(e/A);return S(n+(A+1)*e/(e+f))}function I(e){var t,r,n,i,a,o,s,f,d,g,y,_=[],b=e.length,A=0,w=v,x=p;for((r=e.lastIndexOf(m))<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&k("not-basic"),_.push(e.charCodeAt(n));for(i=r>0?r+1:0;i<b;){for(a=A,o=1,s=c;i>=b&&k("invalid-input"),((f=(y=e.charCodeAt(i++))-48<10?y-22:y-65<26?y-65:y-97<26?y-97:c)>=c||f>S((u-A)/o))&&k("overflow"),A+=f*o,!(f<(d=s<=x?l:s>=x+h?h:s-x));s+=c)o>S(u/(g=c-d))&&k("overflow"),o*=g;x=E(A-a,t=_.length+1,0==a),S(A/t)>u-w&&k("overflow"),w+=S(A/t),A%=t,_.splice(A++,0,w)}return R(_)}function O(e){var t,r,n,i,a,o,s,f,d,g,y,_,b,A,x,C=[];for(_=(e=P(e)).length,t=v,r=0,a=p,o=0;o<_;++o)(y=e[o])<128&&C.push(w(y));for(n=i=C.length,i&&C.push(m);n<_;){for(s=u,o=0;o<_;++o)(y=e[o])>=t&&y<s&&(s=y);for(s-t>S((u-r)/(b=n+1))&&k("overflow"),r+=(s-t)*b,t=s,o=0;o<_;++o)if((y=e[o])<t&&++r>u&&k("overflow"),y==t){for(f=r,d=c;!(f<(g=d<=a?l:d>=a+h?h:d-a));d+=c)x=f-g,A=c-g,C.push(w(T(g+x%A,0))),f=S(x/A);C.push(w(T(f,0))),a=E(r,b,n==i),r=0,++n}++r,++t}return C.join("")}s={version:"1.4.1",ucs2:{decode:P,encode:R},decode:I,encode:O,toASCII:function(e){return C(e,function(e){return y.test(e)?"xn--"+O(e):e})},toUnicode:function(e){return C(e,function(e){return g.test(e)?I(e.slice(4).toLowerCase()):e})}},void 0===(i=function(){return s}.call(t,r,t,e))||(e.exports=i)}()}).call(t,r("3IRH")(e),r("DuR2"))},NWVd:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r("Xxa5"),i=r.n(n),a=r("exGp"),o=r.n(a),s=r("Au9i"),u=r("c2Ch"),c={components:{},props:{},data:function(){return{artList:"",siteInfo:{}}},watch:{},computed:{},created:function(){},mounted:function(){this.getArtList(),this.getInfoSite()},methods:{getInfoSite:function(){var e=this;return o()(i.a.mark(function t(){var r;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,u.J();case 2:0==+(r=t.sent).status?e.siteInfo=r.data:e.$message.error(r.msg);case 4:case"end":return t.stop()}},t,e)}))()},getArtList:function(){var e=this;return o()(i.a.mark(function t(){var r;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,u.v({artId:e.$route.query.id});case 2:0==+(r=t.sent).status?e.artList=r.data:Object(s.Toast)(r.msg);case 4:case"end":return t.stop()}},t,e)}))()}}},l={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"wrapper"},[r("div",{staticClass:"header"},[r("mt-header",{attrs:{title:"公告"}},[r("router-link",{attrs:{slot:"left",to:"/home"},slot:"left"},[r("mt-button",{attrs:{icon:"back"}},[e._v("返回")])],1)],1)],1),e._v(" "),r("div",{staticClass:"introduce"},[r("h2",{staticClass:"title text-center"},[e._v(e._s(e.artList.artTitle))]),e._v(" "),r("div",{staticClass:"introduce-body"},[r("p",{staticClass:"text-center origin"},[e._v("\n        "+e._s(e.artList.author)+" "+e._s(e._f("timeFormat")(new Date(e.artList.addTime)))+"\n        ")]),e._v(" "),r("div",{staticClass:"summary"},[r("pre",[e._v(e._s(e.artList.artSummary))])]),e._v(" "),r("div",{staticClass:"content"},[r("pre",[e._v(e._s(e.artList.artCnt))])]),e._v(" "),r("div",{staticClass:"bottom-box"},[r("p",{staticClass:"pull-right"},[e._v(e._s(e.siteInfo.siteName))]),e._v(" "),r("div",{staticClass:"clearfix"},[r("p",{staticClass:"pull-right"},[e._v(e._s(e._f("timeFormat")(new Date(e.artList.addTime))))])])])])]),e._v(" "),r("div",{staticClass:"alert-box"},[e._v("\n    免责声明：本文观点仅代表作者个人观点，不构成平台的投资建议，本平台不对文章的准确性、完整性和及时性做出任何保证，亦不对因使用或信赖文章信息引发的任何损失承担责任。\n  ")])])},staticRenderFns:[]};var h=r("VU/8")(c,l,!1,function(e){r("8O0F")},"data-v-0effc1e0",null);t.default=h.exports},Oixe:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r("Xxa5"),i=r.n(n),a=r("exGp"),o=r.n(a),s=r("c2Ch"),u=(r("Au9i"),{name:"trBuy",created:function(){this.$route.query.t&&(this.tabsCurrentIndex=Number(this.$route.query.t),console.log(this.tabsCurrentIndex)),this.$route.query.m&&(this.nowPrice=Number(this.$route.query.m),this.profitTarget=Number(this.$route.query.m),this.zhisun=Number(this.$route.query.m),this.nums=Number(this.$route.query.m)),this.$route.query.id&&(this.id=this.$route.query.id),this.$route.query.code&&(this.code=this.$route.query.code),this.$route.query.name&&(this.name=this.$route.query.name),this.$route.query.type&&(this.type=this.$route.query.type),this.$route.query.if_us&&(this.if_us=this.$route.query.if_us)},data:function(){return{tradingArr:[this.$t("hj84"),this.$t("hj85")],tabsCurrentIndex:0,priceTabs:[this.$t("hj108"),this.$t("hj109")],priceTabsCurrentIndex:0,num:1,nums:1,profit:!1,nowPrice:0,type:0,code:"",name:"",settingInfo:[],selectCycle:20,siteLeverList:[],checkedZy:!1,checkedZs:!1,buying:!1,zhisun:0,showGg:!1,profitTarget:0,if_us:0,id:"",actions:[{name:"100X",subname:this.$t("hj102")},{name:"200X",subname:this.$t("hj102")},{name:"300X",subname:this.$t("hj102")}],profitArr:[{name:this.$t("hj104"),checked:!1},{name:this.$t("hj105"),checked:!1},{name:this.$t("hj110"),checked:!1}]}},mounted:function(){this.getUserInfo(),this.getSettingInfo()},watch:{checkedZy:function(e){navigator.vibrate&&navigator.vibrate([55])},checkedZs:function(e){navigator.vibrate&&navigator.vibrate([55])}},methods:{handleBack:function(){this.$router.go(-1)},handleTradingClick:function(e){this.tabsCurrentIndex=e},handleTabsClick:function(e,t){this.priceTabsCurrentIndex=t},jyslJian:function(){"string"==typeof this.num?this.num=1:this.num>1?(this.num,this.num=Number(this.num-1)):this.num=1},gdJian:function(){this.nums>1?(this.nums,this.nums=Number(Number(this.nums)-1).toFixed(2)):this.nums=this.nowPrice},zYjian:function(){(Number(this.profitTarget)-1).toFixed(2)<this.nowPrice?this.profitTarget=this.nowPrice:this.profitTarget=(Number(this.profitTarget)-1).toFixed(2)},zSjia:function(){(Number(this.zhisun)+1).toFixed(2)>this.nowPrice?this.zhisun=this.nowPrice:this.zhisun++},zsInt:function(e){"string"==typeof e.target.value?this.zhisun=this.nowPrice:Number(e.target.value)>this.nowPrice&&(this.zhisun=this.nowPrice)},zyInt:function(e){"string"==typeof e.target.value?this.profitTarget=this.nowPrice:Number(e.target.value)<Number(this.nowPrice)&&(this.profitTarget=Number(this.nowPrice))},onSelect:function(e){this.selectCycle=e.gg},gdOrSetBuy:function(){0==this.priceTabsCurrentIndex?this.setBuy():this.gdBuy(),navigator.vibrate&&navigator.vibrate([55])},gdBuy:function(){var e=this;return o()(i.a.mark(function t(){var r,n;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.buying){t.next=2;break}return t.abrupt("return");case 2:return e.buying=!0,r={stockId:e.code,buyNum:100*e.num,lever:e.selectCycle?e.selectCycle:0,targetPrice:e.nums},0==e.tabsCurrentIndex?r.buyType=1:r.buyType=0,e.checkedZy&&(r.profitTarget=e.profitTarget),e.checkedZs&&(r.stopTarget=e.zhisun),t.next=9,s._13(r);case 9:n=t.sent,e.buying=!1,0===n.status?(e.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:n.msg,elAlertType:"success"}),e.getUserInfo(),e.$router.push("/warehouse?index=1")):e.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:n.msg});case 12:case"end":return t.stop()}},t,e)}))()},setBuy:function(){if(!this.$store.state.userInfo.idCard)return this.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:this.$t("hj111")}),void this.$router.push("/authentications");if(!this.buying){this.buying=!0;var e={};e={buyNum:100*this.num,lever:this.selectCycle?this.selectCycle:0},0==this.tabsCurrentIndex?e.buyType=1:e.buyType=0,this.checkedZy&&(e.profitTarget=this.profitTarget),this.checkedZs&&(e.stopLoss=this.zhisun),0==this.type?this.gpBuy(e):this.zsBuy(e)}},gpBuy:function(e){var t=this;return o()(i.a.mark(function r(){var n;return i.a.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return e.stockId=t.id,r.next=3,s.f(e);case 3:n=r.sent,t.buying=!1,0===n.status?(t.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:n.msg,elAlertType:"success"}),t.getUserInfo(),t.$router.push("/warehouse?index=0")):n.msg.indexOf("不在交易时段内")>-1?t.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:t.$t("hj113")}):t.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:n.msg});case 6:case"end":return r.stop()}},r,t)}))()},zsBuy:function(e){var t=this;return o()(i.a.mark(function r(){var n;return i.a.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return e.indexId=t.id,r.next=3,s._15(e);case 3:n=r.sent,t.buying=!1,0===n.status?(t.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:n.msg,elAlertType:"success"}),t.getUserInfo(),t.$router.push("/warehouse?index=0")):t.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:n.msg});case 6:case"end":return r.stop()}},r,t)}))()},getUserInfo:function(){var e=this;return o()(i.a.mark(function t(){var r;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,s._7();case 2:0===(r=t.sent).status?e.$store.state.userInfo=r.data:e.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:r.msg}),e.$store.state.user=e.user;case 5:case"end":return t.stop()}},t,e)}))()},getSettingInfo:function(){var e=this;return o()(i.a.mark(function t(){var r,n,a,o,u,c,l,h;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,s.X();case 2:if(0===(r=t.sent).status)if(e.settingInfo=r.data,e.selectCycle=r.data.siteLever,void 0!==e.$store.state.userInfo&&null!==e.$store.state.userInfo&&""!==e.$store.state.userInfo.phone&&null!=e.$store.state.userInfo.siteLever)for(e.selectCycle=e.$store.state.userInfo.siteLever.split("/")[0],e.siteLeverList=[],n=0;n<e.$store.state.userInfo.siteLever.split("/").length;n++)a=e.$store.state.userInfo.siteLever.split("/")[n],o={label:a+e.$t("hj112"),value:a},e.siteLeverList.push(o);else for(e.selectCycle=r.data.siteLever.split("/")[0],e.siteLeverList=[],u=0;u<r.data.siteLever.split("/").length;u++)c=r.data.siteLever.split("/")[u],l=(Number(e.nowPrice)/Number(c)).toFixed(2),h={name:c+"X",subname:e.$t("hj102")+"："+(100*l).toFixed(2),gg:c,bzj:l},e.siteLeverList.push(h);else e.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:r.msg});case 4:case"end":return t.stop()}},t,e)}))()}},filters:{getName:function(e){return e.length>15?e.substring(0,14):e}}}),c={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"buy_page"},[n("div",{staticClass:"content"},[n("div",{staticClass:"top_cny"},[n("div",{staticClass:"top_back"},[n("div",{staticClass:"left_back",on:{click:function(t){return e.handleBack()}}},[n("img",{attrs:{src:r("owqv"),alt:""}})]),e._v(" "),n("div",{staticClass:"right_title"},[n("div",{staticClass:"t_t",staticStyle:{"white-space":"nowrap"}},[n("span",[e._v(e._s(e._f("getName")(e.name)))])]),e._v(" "),n("div",{staticClass:"b_t"},[n("span",[e._v(e._s(e.code))])])])]),e._v(" "),n("div",{staticClass:"bottom_buy_price"},[n("div",{staticClass:"cot"},[n("div",{staticClass:"lefts"},[n("div",{staticClass:"top_new"},[n("span",[e._v(e._s(e.nowPrice))])]),e._v(" "),n("div",{staticClass:"bottom_es"},[n("span",[e._v(e._s(e.$t("hj98")))])])]),e._v(" "),n("div",{staticClass:"rights"},[n("div",{staticClass:"ese"},[n("div",{staticClass:"mc",class:0===e.tabsCurrentIndex?"actives":"",on:{click:function(t){return e.handleTradingClick(0)}}},[n("span",[e._v(e._s(e.$t("hj84")))])]),e._v(" "),n("div",{staticClass:"mr",class:1===e.tabsCurrentIndex?"active":"",on:{click:function(t){return e.handleTradingClick(1)}}},[n("span",[e._v(e._s(e.$t("hj85")))])])])])])])]),e._v(" "),n("div",{staticClass:"price_tabs"},[n("div",{staticClass:"tabs"},e._l(e.priceTabs,function(t,r){return n("div",{key:r,staticClass:"tab_item",class:e.priceTabsCurrentIndex===r?"active":"",on:{click:function(n){return e.handleTabsClick(t,r)}}},[n("span",[e._v(e._s(t))])])}),0),e._v(" "),n("div",{staticClass:"tabs_content"},[n("div",{directives:[{name:"show",rawName:"v-show",value:1==e.priceTabsCurrentIndex,expression:"priceTabsCurrentIndex == 1"}],staticClass:"num"},[n("span",[e._v(e._s(e.$t("hj99")))])]),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:1==e.priceTabsCurrentIndex,expression:"priceTabsCurrentIndex == 1"}],staticClass:"tr_es flexJy"},[n("div",{staticClass:"top_input"},[n("input",{directives:[{name:"model",rawName:"v-model",value:e.nums,expression:"nums"}],attrs:{type:"Number"},domProps:{value:e.nums},on:{input:function(t){t.target.composing||(e.nums=t.target.value)}}})]),e._v(" "),n("div",{staticClass:"right_sw flexJy",staticStyle:{width:"auto"}},[n("div",{staticClass:"addorj",on:{click:e.gdJian}},[n("img",{attrs:{src:r("glPx"),alt:""}})]),e._v(" "),n("div",{staticClass:"addorj"}),e._v(" "),n("div",{staticClass:"addorj",on:{click:function(t){"string"==typeof e.nums?e.nums=Number(Number(e.nums)+1).toFixed(2):e.nums=(Number(e.nums)+1).toFixed(2)}}},[n("img",{attrs:{src:r("Sree")}})])])]),e._v(" "),n("div",{staticClass:"num"},[n("span",[e._v(e._s(e.$t("hj100")))])]),e._v(" "),n("div",{staticClass:"tr_es flexJy"},[n("div",{staticClass:"top_input"},[n("input",{directives:[{name:"model",rawName:"v-model",value:e.num,expression:"num"}],attrs:{type:"number",onkeyup:"value=value.replace(/[^\\d]/g,'')"},domProps:{value:e.num},on:{input:function(t){t.target.composing||(e.num=t.target.value)}}})]),e._v(" "),n("div",{staticClass:"right_sw flexJy",staticStyle:{width:"auto"}},[n("div",{staticClass:"addorj",on:{click:e.jyslJian}},[n("img",{attrs:{src:r("glPx")}})]),e._v(" "),n("div",{staticClass:"addorj"}),e._v(" "),n("div",{staticClass:"addorj",on:{click:function(t){e.num,e.num=Number(Number(e.num)+1)}}},[n("img",{attrs:{src:r("Sree")}})])])]),e._v(" "),n("div",{staticClass:"tr_rs gg",on:{click:function(t){e.showGg=!0}}},[n("div",{staticClass:"top_bzz"},[n("span",[e._v(e._s(e.$t("hj101")))]),e._v(" "),n("span",[e._v(e._s(e.selectCycle+"X"))])])]),e._v(" "),n("div",{staticClass:"tr_rs"},[n("div",{staticClass:"top_bzz",staticStyle:{"padding-bottom":"0.2rem"}},[n("span",{staticStyle:{"text-align":"left"}},[e._v(e._s(1==e.if_us?e.$t("hj102")+"($)":"2"==e.if_us?e.$t("hj102")+"(HK$)":e.$t("hj102")+"(¥)"))]),e._v(" "),n("span",{staticStyle:{"text-align":"right"}},[e._v(e._s(1==e.if_us?e.$t("hj103")+"($)":"2"==e.if_us?e.$t("hj103")+"(HK$)":e.$t("hj103")+"(¥)"))])]),e._v(" "),n("div",{staticClass:"bottom_bzz"},[n("span",[e._v(e._s((e.nowPrice/e.selectCycle*e.num*100).toFixed(2)))]),e._v(" "),void 0==e.$store.state.userInfo.userAmt?n("span",[e._v("¥0.00")]):e._e(),e._v(" "),void 0!=e.$store.state.userInfo.userIndexAmt&&"0"!=e.type?n("span",{staticStyle:{"white-space":"nowarp"}},[e._v("\n              "+e._s(1==e.if_us?"$"+(Number(e.$store.state.userInfo.userIndexAmt)/7.1).toFixed(2):"2"==e.if_us?"HK$"+Number(e.$store.state.userInfo.userIndexAmt/.9).toFixed(2):"¥"+e.$store.state.userInfo.userIndexAmt)+"\n            ")]):e._e(),e._v(" "),void 0!=e.$store.state.userInfo.userAmt&&"0"==e.type?n("span",{staticStyle:{"white-space":"nowarp"}},[e._v("\n              "+e._s(1==e.if_us?"$"+(Number(e.$store.state.userInfo.userAmt)/7.1).toFixed(2):"2"==e.if_us?"HK$"+Number(e.$store.state.userInfo.userAmt/.9).toFixed(2):"¥ "+e.$store.state.userInfo.userAmt)+"\n            ")]):e._e()])])])]),e._v(" "),n("div",{staticClass:"switchs"},[n("div",{staticClass:"zy"},[n("div",{staticClass:"left_zy"},[n("span",[e._v(e._s(e.$t("hj104")))])]),e._v(" "),n("div",{staticClass:"right_sw"},[n("van-switch",{model:{value:e.checkedZy,callback:function(t){e.checkedZy=t},expression:"checkedZy"}})],1)]),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.checkedZy,expression:"checkedZy"}],staticClass:"zy"},[n("div",{staticClass:"left_zy"},[n("input",{directives:[{name:"model",rawName:"v-model",value:e.profitTarget,expression:"profitTarget"}],attrs:{onkeyup:"value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, '$1')"},domProps:{value:e.profitTarget},on:{input:[function(t){t.target.composing||(e.profitTarget=t.target.value)},e.zyInt]}})]),e._v(" "),n("div",{staticClass:"right_sw",staticStyle:{width:"auto"}},[n("div",{staticClass:"addorj",on:{click:e.zYjian}},[n("img",{attrs:{src:r("glPx")}})]),e._v(" "),n("div",{staticClass:"addorj"}),e._v(" "),n("div",{staticClass:"addorj",on:{click:function(t){e.profitTarget=(Number(e.profitTarget)+1).toFixed(2)}}},[n("img",{attrs:{src:r("Sree")}})])])]),e._v(" "),n("div",{staticClass:"zy"},[n("div",{staticClass:"left_zy"},[n("span",[e._v(e._s(e.$t("hj105")))])]),e._v(" "),n("div",{staticClass:"right_sw"},[n("van-switch",{model:{value:e.checkedZs,callback:function(t){e.checkedZs=t},expression:"checkedZs"}})],1)]),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.checkedZs,expression:"checkedZs"}],staticClass:"zy"},[n("div",{staticClass:"left_zy"},[n("input",{directives:[{name:"model",rawName:"v-model",value:e.zhisun,expression:"zhisun"}],attrs:{onkeyup:"value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, '$1')"},domProps:{value:e.zhisun},on:{input:[function(t){t.target.composing||(e.zhisun=t.target.value)},e.zsInt]}})]),e._v(" "),n("div",{staticClass:"right_sw",staticStyle:{width:"auto"}},[n("div",{staticClass:"addorj",on:{click:function(t){e.zhisun--}}},[n("img",{attrs:{src:r("glPx")}})]),e._v(" "),n("div",{staticClass:"addorj"}),e._v(" "),n("div",{staticClass:"addorj",on:{click:e.zSjia}},[n("img",{attrs:{src:r("Sree")}})])])])]),e._v(" "),n("div",{staticClass:"btn_buy",on:{click:function(t){return e.gdOrSetBuy()}}},[n("div",{class:0==e.tabsCurrentIndex?"maichu":""},[n("span",[e._v(e._s(0==e.tabsCurrentIndex?e.$t("hj84"):e.$t("hj85")))])])])]),e._v(" "),n("van-action-sheet",{attrs:{actions:e.siteLeverList,"cancel-text":e.$t("hj106"),description:e.$t("hj107"),"close-on-click-action":""},on:{select:e.onSelect},model:{value:e.showGg,callback:function(t){e.showGg=t},expression:"showGg"}})],1)},staticRenderFns:[]};var l=r("VU/8")(u,c,!1,function(e){r("GIdV")},"data-v-20e314ed",null);t.default=l.exports},Sree:function(e,t,r){e.exports=r.p+"static/img/ic_number_add.8eafca9.png"},UZ5h:function(e,t,r){"use strict";var n=r("MsCo"),i=r("qOJP");function a(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}t.parse=_,t.resolve=function(e,t){return _(e,!1,!0).resolve(t)},t.resolveObject=function(e,t){return e?_(e,!1,!0).resolveObject(t):t},t.format=function(e){i.isString(e)&&(e=_(e));return e instanceof a?e.format():a.prototype.format.call(e)},t.Url=a;var o=/^([a-z0-9.+-]+:)/i,s=/:[0-9]*$/,u=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,c=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),l=["'"].concat(c),h=["%","/","?",";","#"].concat(l),f=["/","?","#"],d=/^[+a-z0-9A-Z_-]{0,63}$/,p=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,v={javascript:!0,"javascript:":!0},m={javascript:!0,"javascript:":!0},g={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},y=r("1nuA");function _(e,t,r){if(e&&i.isObject(e)&&e instanceof a)return e;var n=new a;return n.parse(e,t,r),n}a.prototype.parse=function(e,t,r){if(!i.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var a=e.indexOf("?"),s=-1!==a&&a<e.indexOf("#")?"?":"#",c=e.split(s);c[0]=c[0].replace(/\\/g,"/");var _=e=c.join(s);if(_=_.trim(),!r&&1===e.split("#").length){var b=u.exec(_);if(b)return this.path=_,this.href=_,this.pathname=b[1],b[2]?(this.search=b[2],this.query=t?y.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var A=o.exec(_);if(A){var S=(A=A[0]).toLowerCase();this.protocol=S,_=_.substr(A.length)}if(r||A||_.match(/^\/\/[^@\/]+@[^@\/]+/)){var w="//"===_.substr(0,2);!w||A&&m[A]||(_=_.substr(2),this.slashes=!0)}if(!m[A]&&(w||A&&!g[A])){for(var k,x,C=-1,P=0;P<f.length;P++){-1!==(R=_.indexOf(f[P]))&&(-1===C||R<C)&&(C=R)}-1!==(x=-1===C?_.lastIndexOf("@"):_.lastIndexOf("@",C))&&(k=_.slice(0,x),_=_.slice(x+1),this.auth=decodeURIComponent(k)),C=-1;for(P=0;P<h.length;P++){var R;-1!==(R=_.indexOf(h[P]))&&(-1===C||R<C)&&(C=R)}-1===C&&(C=_.length),this.host=_.slice(0,C),_=_.slice(C),this.parseHost(),this.hostname=this.hostname||"";var T="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!T)for(var E=this.hostname.split(/\./),I=(P=0,E.length);P<I;P++){var O=E[P];if(O&&!O.match(d)){for(var L="",F=0,j=O.length;F<j;F++)O.charCodeAt(F)>127?L+="x":L+=O[F];if(!L.match(d)){var N=E.slice(0,P),M=E.slice(P+1),D=O.match(p);D&&(N.push(D[1]),M.unshift(D[2])),M.length&&(_="/"+M.join(".")+_),this.hostname=N.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),T||(this.hostname=n.toASCII(this.hostname));var q=this.port?":"+this.port:"",U=this.hostname||"";this.host=U+q,this.href+=this.host,T&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==_[0]&&(_="/"+_))}if(!v[S])for(P=0,I=l.length;P<I;P++){var W=l[P];if(-1!==_.indexOf(W)){var B=encodeURIComponent(W);B===W&&(B=escape(W)),_=_.split(W).join(B)}}var z=_.indexOf("#");-1!==z&&(this.hash=_.substr(z),_=_.slice(0,z));var G=_.indexOf("?");if(-1!==G?(this.search=_.substr(G),this.query=_.substr(G+1),t&&(this.query=y.parse(this.query)),_=_.slice(0,G)):t&&(this.search="",this.query={}),_&&(this.pathname=_),g[S]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){q=this.pathname||"";var H=this.search||"";this.path=q+H}return this.href=this.format(),this},a.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",r=this.pathname||"",n=this.hash||"",a=!1,o="";this.host?a=e+this.host:this.hostname&&(a=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(a+=":"+this.port)),this.query&&i.isObject(this.query)&&Object.keys(this.query).length&&(o=y.stringify(this.query));var s=this.search||o&&"?"+o||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||g[t])&&!1!==a?(a="//"+(a||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):a||(a=""),n&&"#"!==n.charAt(0)&&(n="#"+n),s&&"?"!==s.charAt(0)&&(s="?"+s),t+a+(r=r.replace(/[?#]/g,function(e){return encodeURIComponent(e)}))+(s=s.replace("#","%23"))+n},a.prototype.resolve=function(e){return this.resolveObject(_(e,!1,!0)).format()},a.prototype.resolveObject=function(e){if(i.isString(e)){var t=new a;t.parse(e,!1,!0),e=t}for(var r=new a,n=Object.keys(this),o=0;o<n.length;o++){var s=n[o];r[s]=this[s]}if(r.hash=e.hash,""===e.href)return r.href=r.format(),r;if(e.slashes&&!e.protocol){for(var u=Object.keys(e),c=0;c<u.length;c++){var l=u[c];"protocol"!==l&&(r[l]=e[l])}return g[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(e.protocol&&e.protocol!==r.protocol){if(!g[e.protocol]){for(var h=Object.keys(e),f=0;f<h.length;f++){var d=h[f];r[d]=e[d]}return r.href=r.format(),r}if(r.protocol=e.protocol,e.host||m[e.protocol])r.pathname=e.pathname;else{for(var p=(e.pathname||"").split("/");p.length&&!(e.host=p.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==p[0]&&p.unshift(""),p.length<2&&p.unshift(""),r.pathname=p.join("/")}if(r.search=e.search,r.query=e.query,r.host=e.host||"",r.auth=e.auth,r.hostname=e.hostname||e.host,r.port=e.port,r.pathname||r.search){var v=r.pathname||"",y=r.search||"";r.path=v+y}return r.slashes=r.slashes||e.slashes,r.href=r.format(),r}var _=r.pathname&&"/"===r.pathname.charAt(0),b=e.host||e.pathname&&"/"===e.pathname.charAt(0),A=b||_||r.host&&e.pathname,S=A,w=r.pathname&&r.pathname.split("/")||[],k=(p=e.pathname&&e.pathname.split("/")||[],r.protocol&&!g[r.protocol]);if(k&&(r.hostname="",r.port=null,r.host&&(""===w[0]?w[0]=r.host:w.unshift(r.host)),r.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===p[0]?p[0]=e.host:p.unshift(e.host)),e.host=null),A=A&&(""===p[0]||""===w[0])),b)r.host=e.host||""===e.host?e.host:r.host,r.hostname=e.hostname||""===e.hostname?e.hostname:r.hostname,r.search=e.search,r.query=e.query,w=p;else if(p.length)w||(w=[]),w.pop(),w=w.concat(p),r.search=e.search,r.query=e.query;else if(!i.isNullOrUndefined(e.search)){if(k)r.hostname=r.host=w.shift(),(T=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=T.shift(),r.host=r.hostname=T.shift());return r.search=e.search,r.query=e.query,i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!w.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var x=w.slice(-1)[0],C=(r.host||e.host||w.length>1)&&("."===x||".."===x)||""===x,P=0,R=w.length;R>=0;R--)"."===(x=w[R])?w.splice(R,1):".."===x?(w.splice(R,1),P++):P&&(w.splice(R,1),P--);if(!A&&!S)for(;P--;P)w.unshift("..");!A||""===w[0]||w[0]&&"/"===w[0].charAt(0)||w.unshift(""),C&&"/"!==w.join("/").substr(-1)&&w.push("");var T,E=""===w[0]||w[0]&&"/"===w[0].charAt(0);k&&(r.hostname=r.host=E?"":w.length?w.shift():"",(T=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=T.shift(),r.host=r.hostname=T.shift()));return(A=A||r.host&&w.length)&&!E&&w.unshift(""),w.length?r.pathname=w.join("/"):(r.pathname=null,r.path=null),i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},a.prototype.parseHost=function(){var e=this.host,t=s.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},glPx:function(e,t,r){e.exports=r.p+"static/img/ic_number_jian.16156d0.png"},i3w6:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r("lHA8"),i=r.n(n),a=r("mvHQ"),o=r.n(a),s=r("c2Ch"),u=r("Au9i"),c=r("mtWM"),l=r.n(c),h={data:function(){return{isShow:!1,itemIndex:0,list:[],pageNum:1,finished:!1,loading:!1,source:"",lastRequestTime:0}},mounted:function(){var e=this;this.getUserInfo(function(){e.changeItemIndex(0)})},beforeDestroy:function(){this.source&&this.source.cancel("组件销毁取消请求")},methods:{parseNumber:function(e){return parseFloat(e).toFixed(2)},getUserInfo:function(e){var t=this;s._7().then(function(r){0===r.status?(t.$store.state.userInfo=r.data,e()):Object(u.Toast)(r.msg)})},chicangDetail:function(e){this.$router.push({path:"/chicangDetail?type=dazong&item="+o()(e)})},useFormatMoney:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"CNY",n={minimumFractionDigits:2,maximumFractionDigits:6};t&&(n.style="currency",n.currency=r);var i=Number(e||0);if(isNaN(i))throw new Error("Invalid input: price must be a number---\x3e"+e);return i.toLocaleString(void 0,n)},changeItemIndex:function(e){this.source&&this.source.cancel("切换标签页取消请求"),this.list=[],this.pageNum=1,this.finished=!1,this.loading=!1,this.itemIndex=e,0==this.itemIndex&&this.getOrderList(0),1==this.itemIndex&&this.getOrderList(1),3==this.itemIndex&&this.getzqjl(1)},getOrderList:function(e){var t=this;if(!t.loading&&!t.finished){var r=Date.now();if(!(r-t.lastRequestTime<500)){t.lastRequestTime=r,t.source&&t.source.cancel("新请求取消旧请求"),t.source=l.a.CancelToken.source(),t.loading=!0;var n={state:e,stockCode:"",stockSpell:"",pageNum:this.pageNum,pageSize:15};s.T(n,{cancelToken:t.source.token}).then(function(e){if(t.loading=!1,e.data&&e.data.list){var r=e.data.list||[],n=new i.a(t.list.map(function(e){return e.id})),a=r.filter(function(e){return!n.has(e.id)});1===t.pageNum?t.list=a:a.length>0&&(t.list=t.list.concat(a)),r.length<15||0===a.length?t.finished=!0:t.pageNum++}else t.finished=!0}).catch(function(e){t.loading=!1,l.a.isCancel(e)||console.error("获取订单列表失败:",e)})}}},getUserNewGuList:function(){var e=this;s._8().then(function(t){e.list=t.data})},getzqjl:function(e){var t=this;if(!t.loading&&!t.finished){var r=Date.now();r-t.lastRequestTime<500||(t.lastRequestTime=r,t.source&&t.source.cancel("新请求取消旧请求"),t.source=l.a.CancelToken.source(),t.loading=!0,s._5({pageSize:15,pageNum:t.pageNum},{cancelToken:t.source.token}).then(function(e){if(t.loading=!1,e.data&&e.data.list){var r=e.data.list||[],n=new i.a(t.list.map(function(e){return e.id})),a=r.filter(function(e){return!n.has(e.id)});1===t.pageNum?t.list=a:a.length>0&&(t.list=t.list.concat(a)),r.length<15||0===a.length?t.finished=!0:t.pageNum++}else t.finished=!0}).catch(function(e){t.loading=!1,l.a.isCancel(e)||console.error("获取申购记录失败:",e)}))}},getrenjiao:function(e){var t=this;u.MessageBox.confirm(this.$t("hj251")+"?",this.$t("hj165"),{confirmButtonText:this.$t("hj161"),cancelButtonText:this.$t("hj106")}).then(function(){var r={id:e};s._38(r).then(function(e){Object(u.Toast)(e.msg),t.changeItemIndex(1)})}).catch(function(){})}}},f={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"container"},[r("div",{staticClass:"header"},[r("van-nav-bar",{attrs:{title:"我的交易",fixed:""},on:{"click-right":function(t){return e.$router.push({path:"/Searchlist"})}},scopedSlots:e._u([{key:"right",fn:function(){return[r("van-icon",{attrs:{name:"search"}})]},proxy:!0}])})],1),e._v(" "),r("div",{staticClass:"layout"},[r("div",{staticClass:"wallet"},[r("div",{staticClass:"wallet_container"},[r("div",{staticClass:"title"},[r("div",{staticClass:"text",staticStyle:{display:"flex","align-items":"center"}},[r("div",[e._v("总市值")]),e._v(" "),r("div",{staticStyle:{"margin-left":"0.1162rem"}},[e.isShow?r("van-icon",{attrs:{name:"eye-o"},on:{click:function(t){e.isShow=!1}}}):r("van-icon",{attrs:{name:"closed-eye"},on:{click:function(t){e.isShow=!0}}})],1)]),e._v(" "),r("div",{staticClass:"item"},[r("div",{staticClass:"item_price"},[e._v(e._s(e.isShow?e.useFormatMoney(e.$store.state.userInfo.userAmt||0):"******"))]),e._v(" "),r("div",{staticClass:"item_text"},[e._v("CNY")])])]),e._v(" "),r("div",{staticClass:"radio"},[r("div",{staticClass:"item"},[r("p",[e._v("可用余额")]),e._v(" "),r("span",[e._v(e._s(e.isShow?e.useFormatMoney(e.$store.state.userInfo.enableAmt||0):"******"))])]),e._v(" "),r("div",{staticClass:"item"},[r("p",[e._v("新股申购")]),e._v(" "),r("span",[e._v(e._s(e.isShow?e.useFormatMoney(e.$store.state.userInfo.newStockAmount||0):"******"))])]),e._v(" "),r("div",{staticClass:"item"},[r("p",[e._v("可取金额")]),e._v(" "),r("span",[e._v(e._s(e.isShow?e.useFormatMoney(e.$store.state.userInfo.withdrawFunds||0):"******"))])]),e._v(" "),r("div",{staticClass:"item"},[r("p",[e._v("总盈亏")]),e._v(" "),r("span",[e._v(e._s(e.isShow?e.useFormatMoney(e.$store.state.userInfo.accountAllProfitAndLose||0):"******"))])]),e._v(" "),r("div",{staticClass:"item"},[r("p",[e._v("浮动盈亏")]),e._v(" "),r("span",[e._v(e._s(e.isShow?e.useFormatMoney(e.$store.state.userInfo.allProfitAndLose||0):"******"))])]),e._v(" "),r("div",{staticClass:"item"},[r("p",[e._v("持仓总市值")]),e._v(" "),r("span",[e._v(e._s(e.isShow?e.useFormatMoney(e.$store.state.userInfo.allFreezAmt||0):"******"))])]),e._v(" "),r("div",{staticClass:"clear"})])])]),e._v(" "),r("div",{staticClass:"menu"},[r("div",{class:"item "+(0==e.itemIndex?"active":""),on:{click:function(t){return e.changeItemIndex(0)}}},[e._m(0)]),e._v(" "),r("div",{class:"item "+(1==e.itemIndex?"active":""),on:{click:function(t){return e.changeItemIndex(1)}}},[e._m(1)]),e._v(" "),r("div",{class:"item "+(2==e.itemIndex?"active":""),on:{click:function(t){return e.changeItemIndex(2)}}},[e._m(2)]),e._v(" "),r("div",{class:"item "+(3==e.itemIndex?"active":""),on:{click:function(t){return e.changeItemIndex(3)}}},[e._m(3)])]),e._v(" "),0==e.itemIndex?r("div",{staticClass:"list"},[e._m(4),e._v(" "),r("div",{staticClass:"list_container"},[r("van-list",{attrs:{finished:e.finished,"immediate-check":!1,"finished-text":e.$t("hj43"),offset:50},on:{load:function(t){return e.getOrderList(0)}},model:{value:e.loading,callback:function(t){e.loading=t},expression:"loading"}},e._l(e.list,function(t){return r("div",{key:t.id,staticClass:"item",on:{click:function(r){return e.chicangDetail(t)}}},[r("div",{staticClass:"ebox",staticStyle:{"justify-content":"left"}},[r("div",{staticClass:"stock"},[r("div",{staticClass:"name"},[e._v(e._s(t.stockName))]),e._v(" "),r("div",{staticClass:"child"},[t.stockGid.indexOf("sz")>-1?r("div",{staticClass:"tag"},[e._v("深")]):e._e(),e._v(" "),t.stockGid.indexOf("sh")>-1?r("div",{staticClass:"tag"},[e._v("沪")]):e._e(),e._v(" "),t.stockGid.indexOf("bj")>-1?r("div",{staticClass:"tag"},[e._v("北")]):e._e(),e._v(" "),r("div",[e._v(e._s(t.stockCode))])])])]),e._v(" "),r("div",{staticClass:"cbox"},[r("span",[e._v(e._s(e.parseNumber(t.buyNum)))]),e._v(" "),r("span",[e._v(e._s(e.parseNumber(t.buyPrice)))])]),e._v(" "),r("div",{staticClass:"cbox"},[r("span",[e._v(e._s(e.parseNumber(t.now_price)))]),e._v(" "),r("span",[e._v(e._s(e.parseNumber(t.buyOrderPrice)))])]),e._v(" "),r("div",{class:"cbox "+(t.profitAndLossRatio>0?"red":"green")},[r("span",[e._v(e._s(e.parseNumber(t.profitAndLose)))]),e._v(" "),r("span",[e._v(e._s(e.parseNumber(t.profitAndLossRatio))+"%")])])])}),0)],1)]):e._e(),e._v(" "),1==e.itemIndex?r("div",{staticClass:"list"},[e._m(5),e._v(" "),r("div",{staticClass:"list_container"},[r("van-list",{attrs:{finished:e.finished,"immediate-check":!1,"finished-text":e.$t("hj43"),offset:50},on:{load:function(t){return e.getOrderList(1)}},model:{value:e.loading,callback:function(t){e.loading=t},expression:"loading"}},e._l(e.list,function(t){return r("div",{key:t.id,staticStyle:{"border-bottom":"solid 1px rgba(223, 223, 223, 1)","padding-bottom":"0.3488rem"}},[r("div",{staticClass:"item",staticStyle:{border:"none"}},[r("div",{staticClass:"ebox",staticStyle:{"justify-content":"left"}},[r("div",{staticClass:"stock"},[r("div",{staticClass:"name"},[e._v(e._s(t.stockName))]),e._v(" "),r("div",{staticClass:"child"},[t.stockGid.indexOf("sz")>-1?r("div",{staticClass:"tag"},[e._v("深")]):e._e(),e._v(" "),t.stockGid.indexOf("sh")>-1?r("div",{staticClass:"tag"},[e._v("沪")]):e._e(),e._v(" "),t.stockGid.indexOf("bj")>-1?r("div",{staticClass:"tag"},[e._v("北")]):e._e(),e._v(" "),r("div",[e._v(e._s(t.stockCode))])])])]),e._v(" "),r("div",{staticClass:"cbox"},[r("span",[e._v(e._s(e.parseNumber(t.buyPrice)))]),e._v(" "),r("span",[e._v(e._s(e.parseNumber(t.buyNum)))])]),e._v(" "),r("div",{staticClass:"cbox"},[r("span",[e._v(e._s(e.parseNumber(t.buyOrderPrice)))]),e._v(" "),r("span",[e._v(e._s(e.parseNumber(t.sellOrderPrice)))])]),e._v(" "),r("div",{class:"cbox "+(t.profitAndLossRatio>0?"red":"green")},[r("span",[e._v(e._s(e.parseNumber(t.profitAndLose)))]),e._v(" "),r("span",[e._v(e._s(e.parseNumber(t.profitAndLossRatio))+"%")])])]),e._v(" "),r("div",{staticClass:"time"},[r("div",[e._v(e._s(e.dayjs(t.buyOrderTime).format("YYYY-MM-DD HH:mm:ss")))]),e._v(" "),r("div",[e._v(e._s(e.dayjs(t.sellOrderTime).format("YYYY-MM-DD HH:mm:ss")))])]),e._v(" "),r("div",{staticClass:"cbtn",on:{click:function(r){return e.chicangDetail(t)}}},[e._v("查看详情")])])}),0)],1)]):e._e(),e._v(" "),3==e.itemIndex?r("div",{staticClass:"slist"},[r("van-list",{attrs:{finished:e.finished,"finished-text":e.$t("hj43"),offset:50,direction:"down"},on:{load:function(t){return e.getzqjl(1)}},model:{value:e.loading,callback:function(t){e.loading=t},expression:"loading"}},e._l(e.list,function(t){return r("div",{key:t.id,staticClass:"item"},[r("div",{staticClass:"flex",staticStyle:{"font-size":"0.3720rem","border-bottom":"solid 1px #f1f1f1","margin-bottom":"0.3488rem"}},[r("div",{staticStyle:{display:"flex","align-items":"center"}},[t.stockGid.indexOf("sz")>-1?r("div",{staticClass:"tag"},[e._v("深")]):e._e(),e._v(" "),t.stockGid.indexOf("sh")>-1?r("div",{staticClass:"tag"},[e._v("沪")]):e._e(),e._v(" "),t.stockGid.indexOf("bj")>-1?r("div",{staticClass:"tag"},[e._v("北")]):e._e(),e._v(" "),r("span",{staticStyle:{"margin-left":"0.1162rem"}},[e._v(e._s(t.newName)+" ["+e._s(t.newCode)+"]")])])]),e._v(" "),r("div",{staticClass:"grid",staticStyle:{"font-size":"0.3255rem"}},[r("span",[e._v("￥"+e._s(t.buyPrice))]),e._v(" "),r("span",[e._v(e._s(t.winningRate))]),e._v(" "),r("span",[e._v(e._s(t.orderNumber)+"万股")])]),e._v(" "),r("div",{staticClass:"grid",staticStyle:{color:"#ccc","font-size":"0.2790rem"}},[r("span",[e._v("发行价格")]),e._v(" "),r("span",[e._v("中签率")]),e._v(" "),r("span",[e._v("发行总数")])])])}),0)],1):e._e()])])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",[t("span",[this._v("当前持仓")]),this._v(" "),t("span")])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",[t("span",[this._v("历史持仓")]),this._v(" "),t("span")])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",[t("span",[this._v("新股持仓")]),this._v(" "),t("span")])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",[t("span",[this._v("申购记录")]),this._v(" "),t("span")])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"list_title"},[t("div",{staticClass:"item"},[this._v("名称")]),this._v(" "),t("div",{staticClass:"item"},[this._v("持仓 | 市值")]),this._v(" "),t("div",{staticClass:"item"},[this._v("现价 | 成本")]),this._v(" "),t("div",{staticClass:"item"},[this._v("盈亏 | 涨幅")])])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"list_title"},[t("div",{staticClass:"item"},[this._v("股票 | 代码")]),this._v(" "),t("div",{staticClass:"item"},[this._v("本金 | 数量")]),this._v(" "),t("div",{staticClass:"item"},[this._v("买入 | 卖出价")]),this._v(" "),t("div",{staticClass:"item"},[this._v("收益 | 涨幅")])])}]};var d=r("VU/8")(h,f,!1,function(e){r("j415")},"data-v-3e0400d1",null);t.default=d.exports},ioQ5:function(e,t,r){r("HpRW")("Set")},j415:function(e,t){},kMPS:function(e,t,r){"use strict";function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,t,r,a){t=t||"&",r=r||"=";var o={};if("string"!=typeof e||0===e.length)return o;var s=/\+/g;e=e.split(t);var u=1e3;a&&"number"==typeof a.maxKeys&&(u=a.maxKeys);var c=e.length;u>0&&c>u&&(c=u);for(var l=0;l<c;++l){var h,f,d,p,v=e[l].replace(s,"%20"),m=v.indexOf(r);m>=0?(h=v.substr(0,m),f=v.substr(m+1)):(h=v,f=""),d=decodeURIComponent(h),p=decodeURIComponent(f),n(o,d)?i(o[d])?o[d].push(p):o[d]=[o[d],p]:o[d]=p}return o};var i=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},lHA8:function(e,t,r){e.exports={default:r("pPW7"),__esModule:!0}},m9gC:function(e,t,r){var n=r("RY/4"),i=r("4WTo");e.exports=function(e){return function(){if(n(this)!=e)throw TypeError(e+"#toJSON isn't generic");return i(this)}}},n5VT:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"wrapper"},[r("div",{staticClass:"header"},[r("mt-header",{attrs:{title:"合作协议"}},[r("router-link",{attrs:{slot:"left",to:"/user"},slot:"left"},[r("mt-button",{attrs:{icon:"back"}},[e._v("返回")])],1)],1)],1),e._v(" "),e._m(0),e._v(" "),r("div",{staticClass:"agree-footer text-center"},[r("div",{staticClass:"agree"},[r("p",{on:{click:e.isAgree}},[r("i",{class:e.agree?"glyphicon glyphicon glyphicon-ok-sign red":"glyphicon glyphicon-ok-circle"}),e._v("\n        我同意\n        "),r("a",{attrs:{href:"#"}},[e._v("《委托合作协议》")])])]),e._v(" "),r("div",{staticClass:"btn-box"},[r("mt-button",{class:e.agree?"btn btn-red":"btn btn-default",attrs:{size:"small"},on:{click:function(t){return e.toBuy()}}},[e._v("确定")]),e._v(" "),r("mt-button",{staticClass:"btn btn-cancel",attrs:{size:"small"},on:{click:e.toBuy}},[e._v("取消")])],1)])])},staticRenderFns:[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{margin:"12px 20px"}},[r("h1",[e._v("委托投资合作协议")]),e._v(" "),r("p"),e._v(" "),r("p",[e._v("本合作协议(下称“本协议”)由以下各方签署：")]),e._v(" "),r("p",[e._v("甲方")]),e._v(" "),r("p",[e._v("第一条 总则")]),e._v(" "),r("p",[e._v("\n      1，甲方是中金所、期货公司、期货风险子公司等规定的股指及商品期权合格机构投资者，并有参与交易的资格和权限，乙方是对金融市场投资风险有独立认知和投资经验的自然人，在金融市场有投资能力和抗风险能力，认可并了解中金所、期货公司、期货风险子公司的交易规则，委托甲方参与股指及商品期权投资。")])])}]};var i=r("VU/8")({data:function(){return{agree:!1}},methods:{isAgree:function(){this.agree=!this.agree},toBuy:function(){this.$router.push("/buy")}}},n,!1,function(e){r("GX5a")},"data-v-010fe004",null);t.default=i.exports},nKpR:function(e,t,r){(function(t,n,i){var a;a=function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}([function(e,t,r){"use strict";var n=r(1),i=r(147),a=r(162),o=r(163),s=r(151),u=r(164),c=r(156),l=r(153);if(r(4)()){var h=r(165).PDFNodeStream;i.setPDFNetworkStreamFactory(function(e){return new h(e)})}else{var f,d=r(168).PDFNetworkStream;s.isFetchSupported()&&(f=r(169).PDFFetchStream),i.setPDFNetworkStreamFactory(function(e){return f&&s.isValidFetchUrl(e.url)?new f(e):new d(e)})}t.build=i.build,t.version=i.version,t.getDocument=i.getDocument,t.LoopbackPort=i.LoopbackPort,t.PDFDataRangeTransport=i.PDFDataRangeTransport,t.PDFWorker=i.PDFWorker,t.renderTextLayer=a.renderTextLayer,t.AnnotationLayer=o.AnnotationLayer,t.createPromiseCapability=n.createPromiseCapability,t.PasswordResponses=n.PasswordResponses,t.InvalidPDFException=n.InvalidPDFException,t.MissingPDFException=n.MissingPDFException,t.SVGGraphics=u.SVGGraphics,t.NativeImageDecoding=n.NativeImageDecoding,t.CMapCompressionType=n.CMapCompressionType,t.PermissionFlag=n.PermissionFlag,t.UnexpectedResponseException=n.UnexpectedResponseException,t.OPS=n.OPS,t.VerbosityLevel=n.VerbosityLevel,t.UNSUPPORTED_FEATURES=n.UNSUPPORTED_FEATURES,t.createValidAbsoluteUrl=n.createValidAbsoluteUrl,t.createObjectURL=n.createObjectURL,t.removeNullCharacters=n.removeNullCharacters,t.shadow=n.shadow,t.Util=n.Util,t.ReadableStream=n.ReadableStream,t.URL=n.URL,t.RenderingCancelledException=s.RenderingCancelledException,t.getFilenameFromUrl=s.getFilenameFromUrl,t.LinkTarget=s.LinkTarget,t.addLinkAttributes=s.addLinkAttributes,t.loadScript=s.loadScript,t.PDFDateString=s.PDFDateString,t.GlobalWorkerOptions=c.GlobalWorkerOptions,t.apiCompatibilityParams=l.apiCompatibilityParams},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.arrayByteLength=b,t.arraysToBytes=function(e){if(1===e.length&&e[0]instanceof Uint8Array)return e[0];var t,r,n,i=0,a=e.length;for(t=0;t<a;t++)r=e[t],n=b(r),i+=n;var o=0,s=new Uint8Array(i);for(t=0;t<a;t++)(r=e[t])instanceof Uint8Array||(r="string"==typeof r?_(r):new Uint8Array(r)),n=r.byteLength,s.set(r,o),o+=n;return s},t.assert=l,t.bytesToString=function(e){l(null!==e&&"object"===a(e)&&void 0!==e.length,"Invalid argument for bytesToString");var t=e.length;if(t<8192)return String.fromCharCode.apply(null,e);for(var r=[],n=0;n<t;n+=8192){var i=Math.min(n+8192,t),o=e.subarray(n,i);r.push(String.fromCharCode.apply(null,o))}return r.join("")},t.createPromiseCapability=function(){var e=Object.create(null),t=!1;return Object.defineProperty(e,"settled",{get:function(){return t}}),e.promise=new Promise(function(r,n){e.resolve=function(e){t=!0,r(e)},e.reject=function(e){t=!0,n(e)}}),e},t.getVerbosityLevel=function(){return s},t.info=function(e){s>=o.INFOS&&console.log("Info: "+e)},t.isArrayBuffer=function(e){return"object"===a(e)&&null!==e&&void 0!==e.byteLength},t.isArrayEqual=function(e,t){if(e.length!==t.length)return!1;return e.every(function(e,r){return e===t[r]})},t.isBool=function(e){return"boolean"==typeof e},t.isEmptyObj=function(e){for(var t in e)return!1;return!0},t.isNum=function(e){return"number"==typeof e},t.isString=function(e){return"string"==typeof e},t.isSpace=function(e){return 32===e||9===e||13===e||10===e},t.isSameOrigin=function(e,t){try{var r=new i.URL(e);if(!r.origin||"null"===r.origin)return!1}catch(e){return!1}var n=new i.URL(t,r);return r.origin===n.origin},t.createValidAbsoluteUrl=function(e,t){if(!e)return null;try{var r=t?new i.URL(e,t):new i.URL(e);if(function(e){if(!e)return!1;switch(e.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(r))return r}catch(e){}return null},t.isLittleEndian=function(){var e=new Uint8Array(4);return e[0]=1,1===new Uint32Array(e.buffer,0,1)[0]},t.isEvalSupported=function(){try{return new Function(""),!0}catch(e){return!1}},t.log2=function(e){if(e<=0)return 0;return Math.ceil(Math.log2(e))},t.readInt8=function(e,t){return e[t]<<24>>24},t.readUint16=function(e,t){return e[t]<<8|e[t+1]},t.readUint32=function(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0},t.removeNullCharacters=function(e){if("string"!=typeof e)return u("The argument for removeNullCharacters must be a string."),e;return e.replace(y,"")},t.setVerbosityLevel=function(e){Number.isInteger(e)&&(s=e)},t.shadow=function(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!1}),r},t.string32=function(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e)},t.stringToBytes=_,t.stringToPDFString=function(e){var t,r=e.length,n=[];if("þ"===e[0]&&"ÿ"===e[1])for(t=2;t<r;t+=2)n.push(String.fromCharCode(e.charCodeAt(t)<<8|e.charCodeAt(t+1)));else for(t=0;t<r;++t){var i=S[e.charCodeAt(t)];n.push(i?String.fromCharCode(i):e.charAt(t))}return n.join("")},t.stringToUTF8String=function(e){return decodeURIComponent(escape(e))},t.utf8StringToString=function(e){return unescape(encodeURIComponent(e))},t.warn=u,t.unreachable=c,Object.defineProperty(t,"ReadableStream",{enumerable:!0,get:function(){return n.ReadableStream}}),Object.defineProperty(t,"URL",{enumerable:!0,get:function(){return i.URL}}),t.createObjectURL=t.FormatError=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.StreamType=t.PermissionFlag=t.PasswordResponses=t.PasswordException=t.NativeImageDecoding=t.MissingPDFException=t.InvalidPDFException=t.AbortException=t.CMapCompressionType=t.ImageKind=t.FontType=t.AnnotationType=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationBorderStyleType=t.UNSUPPORTED_FEATURES=t.VerbosityLevel=t.OPS=t.IDENTITY_MATRIX=t.FONT_IDENTITY_MATRIX=void 0,r(2);var n=r(143),i=r(145);function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.IDENTITY_MATRIX=[1,0,0,1,0,0];t.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0];t.NativeImageDecoding={NONE:"none",DECODE:"decode",DISPLAY:"display"};t.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};t.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};t.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};t.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};t.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};t.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};t.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};t.StreamType={UNKNOWN:0,FLATE:1,LZW:2,DCT:3,JPX:4,JBIG:5,A85:6,AHX:7,CCF:8,RL:9};t.FontType={UNKNOWN:0,TYPE1:1,TYPE1C:2,CIDFONTTYPE0:3,CIDFONTTYPE0C:4,TRUETYPE:5,CIDFONTTYPE2:6,TYPE3:7,OPENTYPE:8,TYPE0:9,MMTYPE1:10};var o={ERRORS:0,WARNINGS:1,INFOS:5};t.VerbosityLevel=o;t.CMapCompressionType={NONE:0,BINARY:1,STREAM:2};t.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};t.UNSUPPORTED_FEATURES={unknown:"unknown",forms:"forms",javaScript:"javaScript",smask:"smask",shadingPattern:"shadingPattern",font:"font"};t.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};var s=o.WARNINGS;function u(e){s>=o.WARNINGS&&console.log("Warning: "+e)}function c(e){throw new Error(e)}function l(e,t){e||c(t)}var h=function(){function e(e,t){this.name="PasswordException",this.message=e,this.code=t}return e.prototype=new Error,e.constructor=e,e}();t.PasswordException=h;var f=function(){function e(e,t){this.name="UnknownErrorException",this.message=e,this.details=t}return e.prototype=new Error,e.constructor=e,e}();t.UnknownErrorException=f;var d=function(){function e(e){this.name="InvalidPDFException",this.message=e}return e.prototype=new Error,e.constructor=e,e}();t.InvalidPDFException=d;var p=function(){function e(e){this.name="MissingPDFException",this.message=e}return e.prototype=new Error,e.constructor=e,e}();t.MissingPDFException=p;var v=function(){function e(e,t){this.name="UnexpectedResponseException",this.message=e,this.status=t}return e.prototype=new Error,e.constructor=e,e}();t.UnexpectedResponseException=v;var m=function(){function e(e){this.message=e}return e.prototype=new Error,e.prototype.name="FormatError",e.constructor=e,e}();t.FormatError=m;var g=function(){function e(e){this.name="AbortException",this.message=e}return e.prototype=new Error,e.constructor=e,e}();t.AbortException=g;var y=/\x00/g;function _(e){l("string"==typeof e,"Invalid argument for stringToBytes");for(var t=e.length,r=new Uint8Array(t),n=0;n<t;++n)r[n]=255&e.charCodeAt(n);return r}function b(e){return void 0!==e.length?e.length:(l(void 0!==e.byteLength),e.byteLength)}var A=function(){function e(){}var t=["rgb(",0,",",0,",",0,")"];return e.makeCssRgb=function(e,r,n){return t[1]=e,t[3]=r,t[5]=n,t.join("")},e.transform=function(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]},e.applyTransform=function(e,t){return[e[0]*t[0]+e[1]*t[2]+t[4],e[0]*t[1]+e[1]*t[3]+t[5]]},e.applyInverseTransform=function(e,t){var r=t[0]*t[3]-t[1]*t[2];return[(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/r,(-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/r]},e.getAxialAlignedBoundingBox=function(t,r){var n=e.applyTransform(t,r),i=e.applyTransform(t.slice(2,4),r),a=e.applyTransform([t[0],t[3]],r),o=e.applyTransform([t[2],t[1]],r);return[Math.min(n[0],i[0],a[0],o[0]),Math.min(n[1],i[1],a[1],o[1]),Math.max(n[0],i[0],a[0],o[0]),Math.max(n[1],i[1],a[1],o[1])]},e.inverseTransform=function(e){var t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]},e.apply3dTransform=function(e,t){return[e[0]*t[0]+e[1]*t[1]+e[2]*t[2],e[3]*t[0]+e[4]*t[1]+e[5]*t[2],e[6]*t[0]+e[7]*t[1]+e[8]*t[2]]},e.singularValueDecompose2dScale=function(e){var t=[e[0],e[2],e[1],e[3]],r=e[0]*t[0]+e[1]*t[2],n=e[0]*t[1]+e[1]*t[3],i=e[2]*t[0]+e[3]*t[2],a=e[2]*t[1]+e[3]*t[3],o=(r+a)/2,s=Math.sqrt((r+a)*(r+a)-4*(r*a-i*n))/2,u=o+s||1,c=o-s||1;return[Math.sqrt(u),Math.sqrt(c)]},e.normalizeRect=function(e){var t=e.slice(0);return e[0]>e[2]&&(t[0]=e[2],t[2]=e[0]),e[1]>e[3]&&(t[1]=e[3],t[3]=e[1]),t},e.intersect=function(t,r){function n(e,t){return e-t}var i=[t[0],t[2],r[0],r[2]].sort(n),a=[t[1],t[3],r[1],r[3]].sort(n),o=[];return t=e.normalizeRect(t),r=e.normalizeRect(r),(i[0]===t[0]&&i[1]===r[0]||i[0]===r[0]&&i[1]===t[0])&&(o[0]=i[1],o[2]=i[2],(a[0]===t[1]&&a[1]===r[1]||a[0]===r[1]&&a[1]===t[1])&&(o[1]=a[1],o[3]=a[2],o))},e}();t.Util=A;var S=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];var w,k=(w="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",function(e,t){if(!(arguments.length>2&&void 0!==arguments[2]&&arguments[2])&&i.URL.createObjectURL){var r=new Blob([e],{type:t});return i.URL.createObjectURL(r)}for(var n="data:"+t+";base64,",a=0,o=e.length;a<o;a+=3){var s=255&e[a],u=255&e[a+1],c=255&e[a+2];n+=w[s>>2]+w[(3&s)<<4|u>>4]+w[a+1<o?(15&u)<<2|c>>6:64]+w[a+2<o?63&c:64]}return n});t.createObjectURL=k},function(e,r,n){"use strict";function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var a=n(3);if(!a._pdfjsCompatibilityChecked){a._pdfjsCompatibilityChecked=!0;var o=n(4),s="object"===("undefined"==typeof window?"undefined":i(window))&&"object"===("undefined"==typeof document?"undefined":i(document));!a.btoa&&o()&&(a.btoa=function(e){return t.from(e,"binary").toString("base64")}),!a.atob&&o()&&(a.atob=function(e){return t.from(e,"base64").toString("binary")}),s&&void 0===Element.prototype.remove&&(Element.prototype.remove=function(){this.parentNode&&this.parentNode.removeChild(this)}),function(){if(s&&!o()){var e=document.createElement("div");if(e.classList.add("testOne","testTwo"),!0!==e.classList.contains("testOne")||!0!==e.classList.contains("testTwo")){var t=DOMTokenList.prototype.add,r=DOMTokenList.prototype.remove;DOMTokenList.prototype.add=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];for(var i=0,a=r;i<a.length;i++){var o=a[i];t.call(this,o)}},DOMTokenList.prototype.remove=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var i=0,a=t;i<a.length;i++){var o=a[i];r.call(this,o)}}}}}(),s&&!o()&&!1!==document.createElement("div").classList.toggle("test",0)&&(DOMTokenList.prototype.toggle=function(e){var t=arguments.length>1?!!arguments[1]:!this.contains(e);return this[t?"add":"remove"](e),t}),String.prototype.startsWith||n(5),String.prototype.endsWith||n(36),String.prototype.includes||n(38),Array.prototype.includes||n(40),Array.from||n(47),Object.assign||n(70),Math.log2||(Math.log2=n(75)),Number.isNaN||(Number.isNaN=n(77)),Number.isInteger||(Number.isInteger=n(79)),a.Promise&&a.Promise.prototype&&a.Promise.prototype.finally||(a.Promise=n(82)),a.WeakMap||(a.WeakMap=n(102)),a.WeakSet||(a.WeakSet=n(119)),String.codePointAt||(String.codePointAt=n(123)),String.fromCodePoint||(String.fromCodePoint=n(125)),a.Symbol||n(127),String.prototype.padStart||n(134),String.prototype.padEnd||n(138),Object.values||(Object.values=n(140))}},function(e,t,r){"use strict";e.exports="undefined"!=typeof window&&window.Math===Math?window:void 0!==n&&n.Math===Math?n:"undefined"!=typeof self&&self.Math===Math?self:{}},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}e.exports=function(){return"object"===(void 0===i?"undefined":n(i))&&i+""=="[object process]"&&!i.versions.nw&&!i.versions.electron}},function(e,t,r){"use strict";r(6),e.exports=r(9).String.startsWith},function(e,t,r){"use strict";var n=r(7),i=r(28),a=r(30),o="".startsWith;n(n.P+n.F*r(35)("startsWith"),"String",{startsWith:function(e){var t=a(this,e,"startsWith"),r=i(Math.min(arguments.length>1?arguments[1]:void 0,t.length)),n=String(e);return o?o.call(t,n,r):t.slice(r,r+n.length)===n}})},function(e,t,r){"use strict";var n=r(8),i=r(9),a=r(10),o=r(20),s=r(26),u=function e(t,r,u){var c,l,h,f,d=t&e.F,p=t&e.G,v=t&e.P,m=t&e.B,g=p?n:t&e.S?n[r]||(n[r]={}):(n[r]||{}).prototype,y=p?i:i[r]||(i[r]={}),_=y.prototype||(y.prototype={});for(c in p&&(u=r),u)h=((l=!d&&g&&void 0!==g[c])?g:u)[c],f=m&&l?s(h,n):v&&"function"==typeof h?s(Function.call,h):h,g&&o(g,c,h,t&e.U),y[c]!=h&&a(y,c,f),v&&_[c]!=h&&(_[c]=h)};n.core=i,u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},function(e,t,r){"use strict";var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t,r){"use strict";var n=e.exports={version:"2.6.9"};"number"==typeof __e&&(__e=n)},function(e,t,r){"use strict";var n=r(11),i=r(19);e.exports=r(15)?function(e,t,r){return n.f(e,t,i(1,r))}:function(e,t,r){return e[t]=r,e}},function(e,t,r){"use strict";var n=r(12),i=r(14),a=r(18),o=Object.defineProperty;t.f=r(15)?Object.defineProperty:function(e,t,r){if(n(e),t=a(t,!0),n(r),i)try{return o(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},function(e,t,r){"use strict";var n=r(13);e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}e.exports=function(e){return"object"===n(e)?null!==e:"function"==typeof e}},function(e,t,r){"use strict";e.exports=!r(15)&&!r(16)(function(){return 7!=Object.defineProperty(r(17)("div"),"a",{get:function(){return 7}}).a})},function(e,t,r){"use strict";e.exports=!r(16)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t,r){"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t,r){"use strict";var n=r(13),i=r(8).document,a=n(i)&&n(i.createElement);e.exports=function(e){return a?i.createElement(e):{}}},function(e,t,r){"use strict";var n=r(13);e.exports=function(e,t){if(!n(e))return e;var r,i;if(t&&"function"==typeof(r=e.toString)&&!n(i=r.call(e)))return i;if("function"==typeof(r=e.valueOf)&&!n(i=r.call(e)))return i;if(!t&&"function"==typeof(r=e.toString)&&!n(i=r.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},function(e,t,r){"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t,r){"use strict";var n=r(8),i=r(10),a=r(21),o=r(22)("src"),s=r(23),u=(""+s).split("toString");r(9).inspectSource=function(e){return s.call(e)},(e.exports=function(e,t,r,s){var c="function"==typeof r;c&&(a(r,"name")||i(r,"name",t)),e[t]!==r&&(c&&(a(r,o)||i(r,o,e[t]?""+e[t]:u.join(String(t)))),e===n?e[t]=r:s?e[t]?e[t]=r:i(e,t,r):(delete e[t],i(e,t,r)))})(Function.prototype,"toString",function(){return"function"==typeof this&&this[o]||s.call(this)})},function(e,t,r){"use strict";var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,r){"use strict";var n=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+i).toString(36))}},function(e,t,r){"use strict";e.exports=r(24)("native-function-to-string",Function.toString)},function(e,t,r){"use strict";var n=r(9),i=r(8),a=i["__core-js_shared__"]||(i["__core-js_shared__"]={});(e.exports=function(e,t){return a[e]||(a[e]=void 0!==t?t:{})})("versions",[]).push({version:n.version,mode:r(25)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(e,t,r){"use strict";e.exports=!1},function(e,t,r){"use strict";var n=r(27);e.exports=function(e,t,r){if(n(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,i){return e.call(t,r,n,i)}}return function(){return e.apply(t,arguments)}}},function(e,t,r){"use strict";e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t,r){"use strict";var n=r(29),i=Math.min;e.exports=function(e){return e>0?i(n(e),9007199254740991):0}},function(e,t,r){"use strict";var n=Math.ceil,i=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?i:n)(e)}},function(e,t,r){"use strict";var n=r(31),i=r(34);e.exports=function(e,t,r){if(n(t))throw TypeError("String#"+r+" doesn't accept regex!");return String(i(e))}},function(e,t,r){"use strict";var n=r(13),i=r(32),a=r(33)("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[a])?!!t:"RegExp"==i(e))}},function(e,t,r){"use strict";var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t,r){"use strict";var n=r(24)("wks"),i=r(22),a=r(8).Symbol,o="function"==typeof a;(e.exports=function(e){return n[e]||(n[e]=o&&a[e]||(o?a:i)("Symbol."+e))}).store=n},function(e,t,r){"use strict";e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,r){"use strict";var n=r(33)("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[n]=!1,!"/./"[e](t)}catch(e){}}return!0}},function(e,t,r){"use strict";r(37),e.exports=r(9).String.endsWith},function(e,t,r){"use strict";var n=r(7),i=r(28),a=r(30),o="".endsWith;n(n.P+n.F*r(35)("endsWith"),"String",{endsWith:function(e){var t=a(this,e,"endsWith"),r=arguments.length>1?arguments[1]:void 0,n=i(t.length),s=void 0===r?n:Math.min(i(r),n),u=String(e);return o?o.call(t,u,s):t.slice(s-u.length,s)===u}})},function(e,t,r){"use strict";r(39),e.exports=r(9).String.includes},function(e,t,r){"use strict";var n=r(7),i=r(30);n(n.P+n.F*r(35)("includes"),"String",{includes:function(e){return!!~i(this,e,"includes").indexOf(e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,r){"use strict";r(41),e.exports=r(9).Array.includes},function(e,t,r){"use strict";var n=r(7),i=r(42)(!0);n(n.P,"Array",{includes:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),r(46)("includes")},function(e,t,r){"use strict";var n=r(43),i=r(28),a=r(45);e.exports=function(e){return function(t,r,o){var s,u=n(t),c=i(u.length),l=a(o,c);if(e&&r!=r){for(;c>l;)if((s=u[l++])!=s)return!0}else for(;c>l;l++)if((e||l in u)&&u[l]===r)return e||l||0;return!e&&-1}}},function(e,t,r){"use strict";var n=r(44),i=r(34);e.exports=function(e){return n(i(e))}},function(e,t,r){"use strict";var n=r(32);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==n(e)?e.split(""):Object(e)}},function(e,t,r){"use strict";var n=r(29),i=Math.max,a=Math.min;e.exports=function(e,t){return(e=n(e))<0?i(e+t,0):a(e,t)}},function(e,t,r){"use strict";var n=r(33)("unscopables"),i=Array.prototype;void 0==i[n]&&r(10)(i,n,{}),e.exports=function(e){i[n][e]=!0}},function(e,t,r){"use strict";r(48),r(63),e.exports=r(9).Array.from},function(e,t,r){"use strict";var n=r(49)(!0);r(50)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,r=this._i;return r>=t.length?{value:void 0,done:!0}:(e=n(t,r),this._i+=e.length,{value:e,done:!1})})},function(e,t,r){"use strict";var n=r(29),i=r(34);e.exports=function(e){return function(t,r){var a,o,s=String(i(t)),u=n(r),c=s.length;return u<0||u>=c?e?"":void 0:(a=s.charCodeAt(u))<55296||a>56319||u+1===c||(o=s.charCodeAt(u+1))<56320||o>57343?e?s.charAt(u):a:e?s.slice(u,u+2):o-56320+(a-55296<<10)+65536}}},function(e,t,r){"use strict";var n=r(25),i=r(7),a=r(20),o=r(10),s=r(51),u=r(52),c=r(60),l=r(61),h=r(33)("iterator"),f=!([].keys&&"next"in[].keys()),d=function(){return this};e.exports=function(e,t,r,p,v,m,g){u(r,t,p);var y,_,b,A=function(e){if(!f&&e in x)return x[e];switch(e){case"keys":case"values":return function(){return new r(this,e)}}return function(){return new r(this,e)}},S=t+" Iterator",w="values"==v,k=!1,x=e.prototype,C=x[h]||x["@@iterator"]||v&&x[v],P=C||A(v),R=v?w?A("entries"):P:void 0,T="Array"==t&&x.entries||C;if(T&&(b=l(T.call(new e)))!==Object.prototype&&b.next&&(c(b,S,!0),n||"function"==typeof b[h]||o(b,h,d)),w&&C&&"values"!==C.name&&(k=!0,P=function(){return C.call(this)}),n&&!g||!f&&!k&&x[h]||o(x,h,P),s[t]=P,s[S]=d,v)if(y={values:w?P:A("values"),keys:m?P:A("keys"),entries:R},g)for(_ in y)_ in x||a(x,_,y[_]);else i(i.P+i.F*(f||k),t,y);return y}},function(e,t,r){"use strict";e.exports={}},function(e,t,r){"use strict";var n=r(53),i=r(19),a=r(60),o={};r(10)(o,r(33)("iterator"),function(){return this}),e.exports=function(e,t,r){e.prototype=n(o,{next:i(1,r)}),a(e,t+" Iterator")}},function(e,t,r){"use strict";var n=r(12),i=r(54),a=r(58),o=r(57)("IE_PROTO"),s=function(){},u=function(){var e,t=r(17)("iframe"),n=a.length;for(t.style.display="none",r(59).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;n--;)delete u.prototype[a[n]];return u()};e.exports=Object.create||function(e,t){var r;return null!==e?(s.prototype=n(e),r=new s,s.prototype=null,r[o]=e):r=u(),void 0===t?r:i(r,t)}},function(e,t,r){"use strict";var n=r(11),i=r(12),a=r(55);e.exports=r(15)?Object.defineProperties:function(e,t){i(e);for(var r,o=a(t),s=o.length,u=0;s>u;)n.f(e,r=o[u++],t[r]);return e}},function(e,t,r){"use strict";var n=r(56),i=r(58);e.exports=Object.keys||function(e){return n(e,i)}},function(e,t,r){"use strict";var n=r(21),i=r(43),a=r(42)(!1),o=r(57)("IE_PROTO");e.exports=function(e,t){var r,s=i(e),u=0,c=[];for(r in s)r!=o&&n(s,r)&&c.push(r);for(;t.length>u;)n(s,r=t[u++])&&(~a(c,r)||c.push(r));return c}},function(e,t,r){"use strict";var n=r(24)("keys"),i=r(22);e.exports=function(e){return n[e]||(n[e]=i(e))}},function(e,t,r){"use strict";e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,r){"use strict";var n=r(8).document;e.exports=n&&n.documentElement},function(e,t,r){"use strict";var n=r(11).f,i=r(21),a=r(33)("toStringTag");e.exports=function(e,t,r){e&&!i(e=r?e:e.prototype,a)&&n(e,a,{configurable:!0,value:t})}},function(e,t,r){"use strict";var n=r(21),i=r(62),a=r(57)("IE_PROTO"),o=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=i(e),n(e,a)?e[a]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?o:null}},function(e,t,r){"use strict";var n=r(34);e.exports=function(e){return Object(n(e))}},function(e,t,r){"use strict";var n=r(26),i=r(7),a=r(62),o=r(64),s=r(65),u=r(28),c=r(66),l=r(67);i(i.S+i.F*!r(69)(function(e){Array.from(e)}),"Array",{from:function(e){var t,r,i,h,f=a(e),d="function"==typeof this?this:Array,p=arguments.length,v=p>1?arguments[1]:void 0,m=void 0!==v,g=0,y=l(f);if(m&&(v=n(v,p>2?arguments[2]:void 0,2)),void 0==y||d==Array&&s(y))for(r=new d(t=u(f.length));t>g;g++)c(r,g,m?v(f[g],g):f[g]);else for(h=y.call(f),r=new d;!(i=h.next()).done;g++)c(r,g,m?o(h,v,[i.value,g],!0):i.value);return r.length=g,r}})},function(e,t,r){"use strict";var n=r(12);e.exports=function(e,t,r,i){try{return i?t(n(r)[0],r[1]):t(r)}catch(t){var a=e.return;throw void 0!==a&&n(a.call(e)),t}}},function(e,t,r){"use strict";var n=r(51),i=r(33)("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(n.Array===e||a[i]===e)}},function(e,t,r){"use strict";var n=r(11),i=r(19);e.exports=function(e,t,r){t in e?n.f(e,t,i(0,r)):e[t]=r}},function(e,t,r){"use strict";var n=r(68),i=r(33)("iterator"),a=r(51);e.exports=r(9).getIteratorMethod=function(e){if(void 0!=e)return e[i]||e["@@iterator"]||a[n(e)]}},function(e,t,r){"use strict";var n=r(32),i=r(33)("toStringTag"),a="Arguments"==n(function(){return arguments}());e.exports=function(e){var t,r,o;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),i))?r:a?n(t):"Object"==(o=n(t))&&"function"==typeof t.callee?"Arguments":o}},function(e,t,r){"use strict";var n=r(33)("iterator"),i=!1;try{var a=[7][n]();a.return=function(){i=!0},Array.from(a,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!i)return!1;var r=!1;try{var a=[7],o=a[n]();o.next=function(){return{done:r=!0}},a[n]=function(){return o},e(a)}catch(e){}return r}},function(e,t,r){"use strict";r(71),e.exports=r(9).Object.assign},function(e,t,r){"use strict";var n=r(7);n(n.S+n.F,"Object",{assign:r(72)})},function(e,t,r){"use strict";var n=r(15),i=r(55),a=r(73),o=r(74),s=r(62),u=r(44),c=Object.assign;e.exports=!c||r(16)(function(){var e={},t={},r=Symbol(),n="abcdefghijklmnopqrst";return e[r]=7,n.split("").forEach(function(e){t[e]=e}),7!=c({},e)[r]||Object.keys(c({},t)).join("")!=n})?function(e,t){for(var r=s(e),c=arguments.length,l=1,h=a.f,f=o.f;c>l;)for(var d,p=u(arguments[l++]),v=h?i(p).concat(h(p)):i(p),m=v.length,g=0;m>g;)d=v[g++],n&&!f.call(p,d)||(r[d]=p[d]);return r}:c},function(e,t,r){"use strict";t.f=Object.getOwnPropertySymbols},function(e,t,r){"use strict";t.f={}.propertyIsEnumerable},function(e,t,r){"use strict";r(76),e.exports=r(9).Math.log2},function(e,t,r){"use strict";var n=r(7);n(n.S,"Math",{log2:function(e){return Math.log(e)/Math.LN2}})},function(e,t,r){"use strict";r(78),e.exports=r(9).Number.isNaN},function(e,t,r){"use strict";var n=r(7);n(n.S,"Number",{isNaN:function(e){return e!=e}})},function(e,t,r){"use strict";r(80),e.exports=r(9).Number.isInteger},function(e,t,r){"use strict";var n=r(7);n(n.S,"Number",{isInteger:r(81)})},function(e,t,r){"use strict";var n=r(13),i=Math.floor;e.exports=function(e){return!n(e)&&isFinite(e)&&i(e)===e}},function(e,t,r){"use strict";r(83),r(48),r(84),r(87),r(100),r(101),e.exports=r(9).Promise},function(e,t,r){"use strict";var n=r(68),i={};i[r(33)("toStringTag")]="z",i+""!="[object z]"&&r(20)(Object.prototype,"toString",function(){return"[object "+n(this)+"]"},!0)},function(e,t,r){"use strict";for(var n=r(85),i=r(55),a=r(20),o=r(8),s=r(10),u=r(51),c=r(33),l=c("iterator"),h=c("toStringTag"),f=u.Array,d={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=i(d),v=0;v<p.length;v++){var m,g=p[v],y=d[g],_=o[g],b=_&&_.prototype;if(b&&(b[l]||s(b,l,f),b[h]||s(b,h,g),u[g]=f,y))for(m in n)b[m]||a(b,m,n[m],!0)}},function(e,t,r){"use strict";var n=r(46),i=r(86),a=r(51),o=r(43);e.exports=r(50)(Array,"Array",function(e,t){this._t=o(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,r=this._i++;return!e||r>=e.length?(this._t=void 0,i(1)):i(0,"keys"==t?r:"values"==t?e[r]:[r,e[r]])},"values"),a.Arguments=a.Array,n("keys"),n("values"),n("entries")},function(e,t,r){"use strict";e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,r){"use strict";var n,i,a,o,s=r(25),u=r(8),c=r(26),l=r(68),h=r(7),f=r(13),d=r(27),p=r(88),v=r(89),m=r(90),g=r(91).set,y=r(93)(),_=r(94),b=r(95),A=r(96),S=r(97),w=u.TypeError,k=u.process,x=k&&k.versions,C=x&&x.v8||"",P=u.Promise,R="process"==l(k),T=function(){},E=i=_.f,I=!!function(){try{var e=P.resolve(1),t=(e.constructor={})[r(33)("species")]=function(e){e(T,T)};return(R||"function"==typeof PromiseRejectionEvent)&&e.then(T)instanceof t&&0!==C.indexOf("6.6")&&-1===A.indexOf("Chrome/66")}catch(e){}}(),O=function(e){var t;return!(!f(e)||"function"!=typeof(t=e.then))&&t},L=function(e,t){if(!e._n){e._n=!0;var r=e._c;y(function(){for(var n=e._v,i=1==e._s,a=0,o=function(t){var r,a,o,s=i?t.ok:t.fail,u=t.resolve,c=t.reject,l=t.domain;try{s?(i||(2==e._h&&N(e),e._h=1),!0===s?r=n:(l&&l.enter(),r=s(n),l&&(l.exit(),o=!0)),r===t.promise?c(w("Promise-chain cycle")):(a=O(r))?a.call(r,u,c):u(r)):c(n)}catch(e){l&&!o&&l.exit(),c(e)}};r.length>a;)o(r[a++]);e._c=[],e._n=!1,t&&!e._h&&F(e)})}},F=function(e){g.call(u,function(){var t,r,n,i=e._v,a=j(e);if(a&&(t=b(function(){R?k.emit("unhandledRejection",i,e):(r=u.onunhandledrejection)?r({promise:e,reason:i}):(n=u.console)&&n.error&&n.error("Unhandled promise rejection",i)}),e._h=R||j(e)?2:1),e._a=void 0,a&&t.e)throw t.v})},j=function(e){return 1!==e._h&&0===(e._a||e._c).length},N=function(e){g.call(u,function(){var t;R?k.emit("rejectionHandled",e):(t=u.onrejectionhandled)&&t({promise:e,reason:e._v})})},M=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),L(t,!0))},D=function e(t){var r,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===t)throw w("Promise can't be resolved itself");(r=O(t))?y(function(){var i={_w:n,_d:!1};try{r.call(t,c(e,i,1),c(M,i,1))}catch(e){M.call(i,e)}}):(n._v=t,n._s=1,L(n,!1))}catch(e){M.call({_w:n,_d:!1},e)}}};I||(P=function(e){p(this,P,"Promise","_h"),d(e),n.call(this);try{e(c(D,this,1),c(M,this,1))}catch(e){M.call(this,e)}},(n=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=r(98)(P.prototype,{then:function(e,t){var r=E(m(this,P));return r.ok="function"!=typeof e||e,r.fail="function"==typeof t&&t,r.domain=R?k.domain:void 0,this._c.push(r),this._a&&this._a.push(r),this._s&&L(this,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),a=function(){var e=new n;this.promise=e,this.resolve=c(D,e,1),this.reject=c(M,e,1)},_.f=E=function(e){return e===P||e===o?new a(e):i(e)}),h(h.G+h.W+h.F*!I,{Promise:P}),r(60)(P,"Promise"),r(99)("Promise"),o=r(9).Promise,h(h.S+h.F*!I,"Promise",{reject:function(e){var t=E(this);return(0,t.reject)(e),t.promise}}),h(h.S+h.F*(s||!I),"Promise",{resolve:function(e){return S(s&&this===o?P:this,e)}}),h(h.S+h.F*!(I&&r(69)(function(e){P.all(e).catch(T)})),"Promise",{all:function(e){var t=this,r=E(t),n=r.resolve,i=r.reject,a=b(function(){var r=[],a=0,o=1;v(e,!1,function(e){var s=a++,u=!1;r.push(void 0),o++,t.resolve(e).then(function(e){u||(u=!0,r[s]=e,--o||n(r))},i)}),--o||n(r)});return a.e&&i(a.v),r.promise},race:function(e){var t=this,r=E(t),n=r.reject,i=b(function(){v(e,!1,function(e){t.resolve(e).then(r.resolve,n)})});return i.e&&n(i.v),r.promise}})},function(e,t,r){"use strict";e.exports=function(e,t,r,n){if(!(e instanceof t)||void 0!==n&&n in e)throw TypeError(r+": incorrect invocation!");return e}},function(e,t,r){"use strict";var n=r(26),i=r(64),a=r(65),o=r(12),s=r(28),u=r(67),c={},l={},h=e.exports=function(e,t,r,h,f){var d,p,v,m,g=f?function(){return e}:u(e),y=n(r,h,t?2:1),_=0;if("function"!=typeof g)throw TypeError(e+" is not iterable!");if(a(g)){for(d=s(e.length);d>_;_++)if((m=t?y(o(p=e[_])[0],p[1]):y(e[_]))===c||m===l)return m}else for(v=g.call(e);!(p=v.next()).done;)if((m=i(v,y,p.value,t))===c||m===l)return m};h.BREAK=c,h.RETURN=l},function(e,t,r){"use strict";var n=r(12),i=r(27),a=r(33)("species");e.exports=function(e,t){var r,o=n(e).constructor;return void 0===o||void 0==(r=n(o)[a])?t:i(r)}},function(e,t,r){"use strict";var n,i,a,o=r(26),s=r(92),u=r(59),c=r(17),l=r(8),h=l.process,f=l.setImmediate,d=l.clearImmediate,p=l.MessageChannel,v=l.Dispatch,m=0,g={},y=function(){var e=+this;if(g.hasOwnProperty(e)){var t=g[e];delete g[e],t()}},_=function(e){y.call(e.data)};f&&d||(f=function(e){for(var t=[],r=1;arguments.length>r;)t.push(arguments[r++]);return g[++m]=function(){s("function"==typeof e?e:Function(e),t)},n(m),m},d=function(e){delete g[e]},"process"==r(32)(h)?n=function(e){h.nextTick(o(y,e,1))}:v&&v.now?n=function(e){v.now(o(y,e,1))}:p?(a=(i=new p).port2,i.port1.onmessage=_,n=o(a.postMessage,a,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(n=function(e){l.postMessage(e+"","*")},l.addEventListener("message",_,!1)):n="onreadystatechange"in c("script")?function(e){u.appendChild(c("script")).onreadystatechange=function(){u.removeChild(this),y.call(e)}}:function(e){setTimeout(o(y,e,1),0)}),e.exports={set:f,clear:d}},function(e,t,r){"use strict";e.exports=function(e,t,r){var n=void 0===r;switch(t.length){case 0:return n?e():e.call(r);case 1:return n?e(t[0]):e.call(r,t[0]);case 2:return n?e(t[0],t[1]):e.call(r,t[0],t[1]);case 3:return n?e(t[0],t[1],t[2]):e.call(r,t[0],t[1],t[2]);case 4:return n?e(t[0],t[1],t[2],t[3]):e.call(r,t[0],t[1],t[2],t[3])}return e.apply(r,t)}},function(e,t,r){"use strict";var n=r(8),i=r(91).set,a=n.MutationObserver||n.WebKitMutationObserver,o=n.process,s=n.Promise,u="process"==r(32)(o);e.exports=function(){var e,t,r,c=function(){var n,i;for(u&&(n=o.domain)&&n.exit();e;){i=e.fn,e=e.next;try{i()}catch(n){throw e?r():t=void 0,n}}t=void 0,n&&n.enter()};if(u)r=function(){o.nextTick(c)};else if(!a||n.navigator&&n.navigator.standalone)if(s&&s.resolve){var l=s.resolve(void 0);r=function(){l.then(c)}}else r=function(){i.call(n,c)};else{var h=!0,f=document.createTextNode("");new a(c).observe(f,{characterData:!0}),r=function(){f.data=h=!h}}return function(n){var i={fn:n,next:void 0};t&&(t.next=i),e||(e=i,r()),t=i}}},function(e,t,r){"use strict";var n=r(27);e.exports.f=function(e){return new function(e){var t,r;this.promise=new e(function(e,n){if(void 0!==t||void 0!==r)throw TypeError("Bad Promise constructor");t=e,r=n}),this.resolve=n(t),this.reject=n(r)}(e)}},function(e,t,r){"use strict";e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},function(e,t,r){"use strict";var n=r(8).navigator;e.exports=n&&n.userAgent||""},function(e,t,r){"use strict";var n=r(12),i=r(13),a=r(94);e.exports=function(e,t){if(n(e),i(t)&&t.constructor===e)return t;var r=a.f(e);return(0,r.resolve)(t),r.promise}},function(e,t,r){"use strict";var n=r(20);e.exports=function(e,t,r){for(var i in t)n(e,i,t[i],r);return e}},function(e,t,r){"use strict";var n=r(8),i=r(11),a=r(15),o=r(33)("species");e.exports=function(e){var t=n[e];a&&t&&!t[o]&&i.f(t,o,{configurable:!0,get:function(){return this}})}},function(e,t,r){"use strict";var n=r(7),i=r(9),a=r(8),o=r(90),s=r(97);n(n.P+n.R,"Promise",{finally:function(e){var t=o(this,i.Promise||a.Promise),r="function"==typeof e;return this.then(r?function(r){return s(t,e()).then(function(){return r})}:e,r?function(r){return s(t,e()).then(function(){throw r})}:e)}})},function(e,t,r){"use strict";var n=r(7),i=r(94),a=r(95);n(n.S,"Promise",{try:function(e){var t=i.f(this),r=a(e);return(r.e?t.reject:t.resolve)(r.v),t.promise}})},function(e,t,r){"use strict";r(83),r(84),r(103),r(115),r(117),e.exports=r(9).WeakMap},function(e,t,r){"use strict";var n,i=r(8),a=r(104)(0),o=r(20),s=r(108),u=r(72),c=r(109),l=r(13),h=r(110),f=r(110),d=!i.ActiveXObject&&"ActiveXObject"in i,p=s.getWeak,v=Object.isExtensible,m=c.ufstore,g=function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},y={get:function(e){if(l(e)){var t=p(e);return!0===t?m(h(this,"WeakMap")).get(e):t?t[this._i]:void 0}},set:function(e,t){return c.def(h(this,"WeakMap"),e,t)}},_=e.exports=r(111)("WeakMap",g,y,c,!0,!0);f&&d&&(u((n=c.getConstructor(g,"WeakMap")).prototype,y),s.NEED=!0,a(["delete","has","get","set"],function(e){var t=_.prototype,r=t[e];o(t,e,function(t,i){if(l(t)&&!v(t)){this._f||(this._f=new n);var a=this._f[e](t,i);return"set"==e?this:a}return r.call(this,t,i)})}))},function(e,t,r){"use strict";var n=r(26),i=r(44),a=r(62),o=r(28),s=r(105);e.exports=function(e,t){var r=1==e,u=2==e,c=3==e,l=4==e,h=6==e,f=5==e||h,d=t||s;return function(t,s,p){for(var v,m,g=a(t),y=i(g),_=n(s,p,3),b=o(y.length),A=0,S=r?d(t,b):u?d(t,0):void 0;b>A;A++)if((f||A in y)&&(m=_(v=y[A],A,g),e))if(r)S[A]=m;else if(m)switch(e){case 3:return!0;case 5:return v;case 6:return A;case 2:S.push(v)}else if(l)return!1;return h?-1:c||l?l:S}}},function(e,t,r){"use strict";var n=r(106);e.exports=function(e,t){return new(n(e))(t)}},function(e,t,r){"use strict";var n=r(13),i=r(107),a=r(33)("species");e.exports=function(e){var t;return i(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!i(t.prototype)||(t=void 0),n(t)&&null===(t=t[a])&&(t=void 0)),void 0===t?Array:t}},function(e,t,r){"use strict";var n=r(32);e.exports=Array.isArray||function(e){return"Array"==n(e)}},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var i=r(22)("meta"),a=r(13),o=r(21),s=r(11).f,u=0,c=Object.isExtensible||function(){return!0},l=!r(16)(function(){return c(Object.preventExtensions({}))}),h=function(e){s(e,i,{value:{i:"O"+ ++u,w:{}}})},f=e.exports={KEY:i,NEED:!1,fastKey:function(e,t){if(!a(e))return"symbol"==n(e)?e:("string"==typeof e?"S":"P")+e;if(!o(e,i)){if(!c(e))return"F";if(!t)return"E";h(e)}return e[i].i},getWeak:function(e,t){if(!o(e,i)){if(!c(e))return!0;if(!t)return!1;h(e)}return e[i].w},onFreeze:function(e){return l&&f.NEED&&c(e)&&!o(e,i)&&h(e),e}}},function(e,t,r){"use strict";var n=r(98),i=r(108).getWeak,a=r(12),o=r(13),s=r(88),u=r(89),c=r(104),l=r(21),h=r(110),f=c(5),d=c(6),p=0,v=function(e){return e._l||(e._l=new m)},m=function(){this.a=[]},g=function(e,t){return f(e.a,function(e){return e[0]===t})};m.prototype={get:function(e){var t=g(this,e);if(t)return t[1]},has:function(e){return!!g(this,e)},set:function(e,t){var r=g(this,e);r?r[1]=t:this.a.push([e,t])},delete:function(e){var t=d(this.a,function(t){return t[0]===e});return~t&&this.a.splice(t,1),!!~t}},e.exports={getConstructor:function(e,t,r,a){var c=e(function(e,n){s(e,c,t,"_i"),e._t=t,e._i=p++,e._l=void 0,void 0!=n&&u(n,r,e[a],e)});return n(c.prototype,{delete:function(e){if(!o(e))return!1;var r=i(e);return!0===r?v(h(this,t)).delete(e):r&&l(r,this._i)&&delete r[this._i]},has:function(e){if(!o(e))return!1;var r=i(e);return!0===r?v(h(this,t)).has(e):r&&l(r,this._i)}}),c},def:function(e,t,r){var n=i(a(t),!0);return!0===n?v(e).set(t,r):n[e._i]=r,e},ufstore:v}},function(e,t,r){"use strict";var n=r(13);e.exports=function(e,t){if(!n(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},function(e,t,r){"use strict";var n=r(8),i=r(7),a=r(20),o=r(98),s=r(108),u=r(89),c=r(88),l=r(13),h=r(16),f=r(69),d=r(60),p=r(112);e.exports=function(e,t,r,v,m,g){var y=n[e],_=y,b=m?"set":"add",A=_&&_.prototype,S={},w=function(e){var t=A[e];a(A,e,"delete"==e?function(e){return!(g&&!l(e))&&t.call(this,0===e?0:e)}:"has"==e?function(e){return!(g&&!l(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return g&&!l(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,r){return t.call(this,0===e?0:e,r),this})};if("function"==typeof _&&(g||A.forEach&&!h(function(){(new _).entries().next()}))){var k=new _,x=k[b](g?{}:-0,1)!=k,C=h(function(){k.has(1)}),P=f(function(e){new _(e)}),R=!g&&h(function(){for(var e=new _,t=5;t--;)e[b](t,t);return!e.has(-0)});P||((_=t(function(t,r){c(t,_,e);var n=p(new y,t,_);return void 0!=r&&u(r,m,n[b],n),n})).prototype=A,A.constructor=_),(C||R)&&(w("delete"),w("has"),m&&w("get")),(R||x)&&w(b),g&&A.clear&&delete A.clear}else _=v.getConstructor(t,e,m,b),o(_.prototype,r),s.NEED=!0;return d(_,e),S[e]=_,i(i.G+i.W+i.F*(_!=y),S),g||v.setStrong(_,e,m),_}},function(e,t,r){"use strict";var n=r(13),i=r(113).set;e.exports=function(e,t,r){var a,o=t.constructor;return o!==r&&"function"==typeof o&&(a=o.prototype)!==r.prototype&&n(a)&&i&&i(e,a),e}},function(e,t,r){"use strict";var n=r(13),i=r(12),a=function(e,t){if(i(e),!n(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{(n=r(26)(Function.call,r(114).f(Object.prototype,"__proto__").set,2))(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,r){return a(e,r),t?e.__proto__=r:n(e,r),e}}({},!1):void 0),check:a}},function(e,t,r){"use strict";var n=r(74),i=r(19),a=r(43),o=r(18),s=r(21),u=r(14),c=Object.getOwnPropertyDescriptor;t.f=r(15)?c:function(e,t){if(e=a(e),t=o(t,!0),u)try{return c(e,t)}catch(e){}if(s(e,t))return i(!n.f.call(e,t),e[t])}},function(e,t,r){"use strict";r(116)("WeakMap")},function(e,t,r){"use strict";var n=r(7);e.exports=function(e){n(n.S,e,{of:function(){for(var e=arguments.length,t=new Array(e);e--;)t[e]=arguments[e];return new this(t)}})}},function(e,t,r){"use strict";r(118)("WeakMap")},function(e,t,r){"use strict";var n=r(7),i=r(27),a=r(26),o=r(89);e.exports=function(e){n(n.S,e,{from:function(e){var t,r,n,s,u=arguments[1];return i(this),(t=void 0!==u)&&i(u),void 0==e?new this:(r=[],t?(n=0,s=a(u,arguments[2],2),o(e,!1,function(e){r.push(s(e,n++))})):o(e,!1,r.push,r),new this(r))}})}},function(e,t,r){"use strict";r(83),r(84),r(120),r(121),r(122),e.exports=r(9).WeakSet},function(e,t,r){"use strict";var n=r(109),i=r(110);r(111)("WeakSet",function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},{add:function(e){return n.def(i(this,"WeakSet"),e,!0)}},n,!1,!0)},function(e,t,r){"use strict";r(116)("WeakSet")},function(e,t,r){"use strict";r(118)("WeakSet")},function(e,t,r){"use strict";r(124),e.exports=r(9).String.codePointAt},function(e,t,r){"use strict";var n=r(7),i=r(49)(!1);n(n.P,"String",{codePointAt:function(e){return i(this,e)}})},function(e,t,r){"use strict";r(126),e.exports=r(9).String.fromCodePoint},function(e,t,r){"use strict";var n=r(7),i=r(45),a=String.fromCharCode,o=String.fromCodePoint;n(n.S+n.F*(!!o&&1!=o.length),"String",{fromCodePoint:function(e){for(var t,r=[],n=arguments.length,o=0;n>o;){if(t=+arguments[o++],i(t,1114111)!==t)throw RangeError(t+" is not a valid code point");r.push(t<65536?a(t):a(55296+((t-=65536)>>10),t%1024+56320))}return r.join("")}})},function(e,t,r){"use strict";r(128),r(83),e.exports=r(9).Symbol},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var i=r(8),a=r(21),o=r(15),s=r(7),u=r(20),c=r(108).KEY,l=r(16),h=r(24),f=r(60),d=r(22),p=r(33),v=r(129),m=r(130),g=r(131),y=r(107),_=r(12),b=r(13),A=r(62),S=r(43),w=r(18),k=r(19),x=r(53),C=r(132),P=r(114),R=r(73),T=r(11),E=r(55),I=P.f,O=T.f,L=C.f,F=i.Symbol,j=i.JSON,N=j&&j.stringify,M=p("_hidden"),D=p("toPrimitive"),q={}.propertyIsEnumerable,U=h("symbol-registry"),W=h("symbols"),B=h("op-symbols"),z=Object.prototype,G="function"==typeof F&&!!R.f,H=i.QObject,Y=!H||!H.prototype||!H.prototype.findChild,X=o&&l(function(){return 7!=x(O({},"a",{get:function(){return O(this,"a",{value:7}).a}})).a})?function(e,t,r){var n=I(z,t);n&&delete z[t],O(e,t,r),n&&e!==z&&O(z,t,n)}:O,$=function(e){var t=W[e]=x(F.prototype);return t._k=e,t},V=G&&"symbol"==n(F.iterator)?function(e){return"symbol"==n(e)}:function(e){return e instanceof F},Q=function(e,t,r){return e===z&&Q(B,t,r),_(e),t=w(t,!0),_(r),a(W,t)?(r.enumerable?(a(e,M)&&e[M][t]&&(e[M][t]=!1),r=x(r,{enumerable:k(0,!1)})):(a(e,M)||O(e,M,k(1,{})),e[M][t]=!0),X(e,t,r)):O(e,t,r)},J=function(e,t){_(e);for(var r,n=g(t=S(t)),i=0,a=n.length;a>i;)Q(e,r=n[i++],t[r]);return e},K=function(e){var t=q.call(this,e=w(e,!0));return!(this===z&&a(W,e)&&!a(B,e))&&(!(t||!a(this,e)||!a(W,e)||a(this,M)&&this[M][e])||t)},Z=function(e,t){if(e=S(e),t=w(t,!0),e!==z||!a(W,t)||a(B,t)){var r=I(e,t);return!r||!a(W,t)||a(e,M)&&e[M][t]||(r.enumerable=!0),r}},ee=function(e){for(var t,r=L(S(e)),n=[],i=0;r.length>i;)a(W,t=r[i++])||t==M||t==c||n.push(t);return n},te=function(e){for(var t,r=e===z,n=L(r?B:S(e)),i=[],o=0;n.length>o;)!a(W,t=n[o++])||r&&!a(z,t)||i.push(W[t]);return i};G||(u((F=function(){if(this instanceof F)throw TypeError("Symbol is not a constructor!");var e=d(arguments.length>0?arguments[0]:void 0);return o&&Y&&X(z,e,{configurable:!0,set:function t(r){this===z&&t.call(B,r),a(this,M)&&a(this[M],e)&&(this[M][e]=!1),X(this,e,k(1,r))}}),$(e)}).prototype,"toString",function(){return this._k}),P.f=Z,T.f=Q,r(133).f=C.f=ee,r(74).f=K,R.f=te,o&&!r(25)&&u(z,"propertyIsEnumerable",K,!0),v.f=function(e){return $(p(e))}),s(s.G+s.W+s.F*!G,{Symbol:F});for(var re="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ne=0;re.length>ne;)p(re[ne++]);for(var ie=E(p.store),ae=0;ie.length>ae;)m(ie[ae++]);s(s.S+s.F*!G,"Symbol",{for:function(e){return a(U,e+="")?U[e]:U[e]=F(e)},keyFor:function(e){if(!V(e))throw TypeError(e+" is not a symbol!");for(var t in U)if(U[t]===e)return t},useSetter:function(){Y=!0},useSimple:function(){Y=!1}}),s(s.S+s.F*!G,"Object",{create:function(e,t){return void 0===t?x(e):J(x(e),t)},defineProperty:Q,defineProperties:J,getOwnPropertyDescriptor:Z,getOwnPropertyNames:ee,getOwnPropertySymbols:te});var oe=l(function(){R.f(1)});s(s.S+s.F*oe,"Object",{getOwnPropertySymbols:function(e){return R.f(A(e))}}),j&&s(s.S+s.F*(!G||l(function(){var e=F();return"[null]"!=N([e])||"{}"!=N({a:e})||"{}"!=N(Object(e))})),"JSON",{stringify:function(e){for(var t,r,n=[e],i=1;arguments.length>i;)n.push(arguments[i++]);if(r=t=n[1],(b(t)||void 0!==e)&&!V(e))return y(t)||(t=function(e,t){if("function"==typeof r&&(t=r.call(this,e,t)),!V(t))return t}),n[1]=t,N.apply(j,n)}}),F.prototype[D]||r(10)(F.prototype,D,F.prototype.valueOf),f(F,"Symbol"),f(Math,"Math",!0),f(i.JSON,"JSON",!0)},function(e,t,r){"use strict";t.f=r(33)},function(e,t,r){"use strict";var n=r(8),i=r(9),a=r(25),o=r(129),s=r(11).f;e.exports=function(e){var t=i.Symbol||(i.Symbol=a?{}:n.Symbol||{});"_"==e.charAt(0)||e in t||s(t,e,{value:o.f(e)})}},function(e,t,r){"use strict";var n=r(55),i=r(73),a=r(74);e.exports=function(e){var t=n(e),r=i.f;if(r)for(var o,s=r(e),u=a.f,c=0;s.length>c;)u.call(e,o=s[c++])&&t.push(o);return t}},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var i=r(43),a=r(133).f,o={}.toString,s="object"==("undefined"==typeof window?"undefined":n(window))&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return s&&"[object Window]"==o.call(e)?function(e){try{return a(e)}catch(e){return s.slice()}}(e):a(i(e))}},function(e,t,r){"use strict";var n=r(56),i=r(58).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,i)}},function(e,t,r){"use strict";r(135),e.exports=r(9).String.padStart},function(e,t,r){"use strict";var n=r(7),i=r(136),a=r(96),o=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(a);n(n.P+n.F*o,"String",{padStart:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0,!0)}})},function(e,t,r){"use strict";var n=r(28),i=r(137),a=r(34);e.exports=function(e,t,r,o){var s=String(a(e)),u=s.length,c=void 0===r?" ":String(r),l=n(t);if(l<=u||""==c)return s;var h=l-u,f=i.call(c,Math.ceil(h/c.length));return f.length>h&&(f=f.slice(0,h)),o?f+s:s+f}},function(e,t,r){"use strict";var n=r(29),i=r(34);e.exports=function(e){var t=String(i(this)),r="",a=n(e);if(a<0||a==1/0)throw RangeError("Count can't be negative");for(;a>0;(a>>>=1)&&(t+=t))1&a&&(r+=t);return r}},function(e,t,r){"use strict";r(139),e.exports=r(9).String.padEnd},function(e,t,r){"use strict";var n=r(7),i=r(136),a=r(96),o=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(a);n(n.P+n.F*o,"String",{padEnd:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0,!1)}})},function(e,t,r){"use strict";r(141),e.exports=r(9).Object.values},function(e,t,r){"use strict";var n=r(7),i=r(142)(!1);n(n.S,"Object",{values:function(e){return i(e)}})},function(e,t,r){"use strict";var n=r(15),i=r(55),a=r(43),o=r(74).f;e.exports=function(e){return function(t){for(var r,s=a(t),u=i(s),c=u.length,l=0,h=[];c>l;)r=u[l++],n&&!o.call(s,r)||h.push(e?[r,s[r]]:s[r]);return h}}},function(e,t,r){"use strict";var n=!1;if("undefined"!=typeof ReadableStream)try{new ReadableStream({start:function(e){e.close()}}),n=!0}catch(e){}t.ReadableStream=n?ReadableStream:r(144).ReadableStream},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e,t){for(var r in t)e[r]=t[r]}(t,function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.i=function(e){return e},r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=7)}([function(e,t,r){var i="function"==typeof Symbol&&"symbol"===n(Symbol.iterator)?function(e){return n(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":n(e)},a=r(1).assert;function o(e){return"string"==typeof e||"symbol"===(void 0===e?"undefined":i(e))}function s(e,t,r){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}t.typeIsObject=function(e){return"object"===(void 0===e?"undefined":i(e))&&null!==e||"function"==typeof e},t.createDataProperty=function(e,r,n){a(t.typeIsObject(e)),Object.defineProperty(e,r,{value:n,writable:!0,enumerable:!0,configurable:!0})},t.createArrayFromList=function(e){return e.slice()},t.ArrayBufferCopy=function(e,t,r,n,i){new Uint8Array(e).set(new Uint8Array(r,n,i),t)},t.CreateIterResultObject=function(e,t){a("boolean"==typeof t);var r={};return Object.defineProperty(r,"value",{value:e,enumerable:!0,writable:!0,configurable:!0}),Object.defineProperty(r,"done",{value:t,enumerable:!0,writable:!0,configurable:!0}),r},t.IsFiniteNonNegativeNumber=function(e){return!Number.isNaN(e)&&(e!==1/0&&!(e<0))},t.InvokeOrNoop=function(e,t,r){a(void 0!==e),a(o(t)),a(Array.isArray(r));var n=e[t];if(void 0!==n)return s(n,e,r)},t.PromiseInvokeOrNoop=function(e,r,n){a(void 0!==e),a(o(r)),a(Array.isArray(n));try{return Promise.resolve(t.InvokeOrNoop(e,r,n))}catch(e){return Promise.reject(e)}},t.PromiseInvokeOrPerformFallback=function(e,t,r,n,i){a(void 0!==e),a(o(t)),a(Array.isArray(r)),a(Array.isArray(i));var u=void 0;try{u=e[t]}catch(e){return Promise.reject(e)}if(void 0===u)return n.apply(null,i);try{return Promise.resolve(s(u,e,r))}catch(e){return Promise.reject(e)}},t.TransferArrayBuffer=function(e){return e.slice()},t.ValidateAndNormalizeHighWaterMark=function(e){if(e=Number(e),Number.isNaN(e)||e<0)throw new RangeError("highWaterMark property of a queuing strategy must be non-negative and non-NaN");return e},t.ValidateAndNormalizeQueuingStrategy=function(e,r){if(void 0!==e&&"function"!=typeof e)throw new TypeError("size property of a queuing strategy must be a function");return{size:e,highWaterMark:r=t.ValidateAndNormalizeHighWaterMark(r)}}},function(e,t,r){function n(e){this.name="AssertionError",this.message=e||"",this.stack=(new Error).stack}n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,e.exports={rethrowAssertionErrorRejection:function(e){e&&e.constructor===n&&setTimeout(function(){throw e},0)},AssertionError:n,assert:function(e,t){if(!e)throw new n(t)}}},function(e,t,r){var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=r(0),o=a.InvokeOrNoop,s=a.PromiseInvokeOrNoop,u=a.ValidateAndNormalizeQueuingStrategy,c=a.typeIsObject,l=r(1),h=l.assert,f=l.rethrowAssertionErrorRejection,d=r(3),p=d.DequeueValue,v=d.EnqueueValueWithSize,m=d.PeekQueueValue,g=d.ResetQueue,y=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.size,a=r.highWaterMark,o=void 0===a?1:a;if(i(this,e),this._state="writable",this._storedError=void 0,this._writer=void 0,this._writableStreamController=void 0,this._writeRequests=[],this._inFlightWriteRequest=void 0,this._closeRequest=void 0,this._inFlightCloseRequest=void 0,this._pendingAbortRequest=void 0,this._backpressure=!1,void 0!==t.type)throw new RangeError("Invalid type is specified");this._writableStreamController=new D(this,t,n,o),this._writableStreamController.__startSteps()}return n(e,[{key:"abort",value:function(e){return!1===b(this)?Promise.reject(G("abort")):!0===A(this)?Promise.reject(new TypeError("Cannot abort a stream that already has a writer")):S(this,e)}},{key:"getWriter",value:function(){if(!1===b(this))throw G("getWriter");return _(this)}},{key:"locked",get:function(){if(!1===b(this))throw G("locked");return A(this)}}]),e}();function _(e){return new I(e)}function b(e){return!!c(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")}function A(e){return h(!0===b(e),"IsWritableStreamLocked should only be used on known writable streams"),void 0!==e._writer}function S(e,t){var r=e._state;if("closed"===r)return Promise.resolve(void 0);if("errored"===r)return Promise.reject(e._storedError);var n=new TypeError("Requested to abort");if(void 0!==e._pendingAbortRequest)return Promise.reject(n);h("writable"===r||"erroring"===r,"state must be writable or erroring");var i=!1;"erroring"===r&&(i=!0,t=void 0);var a=new Promise(function(r,n){e._pendingAbortRequest={_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:i}});return!1===i&&k(e,n),a}function w(e,t){var r=e._state;"writable"!==r?(h("erroring"===r),x(e)):k(e,t)}function k(e,t){h(void 0===e._storedError,"stream._storedError === undefined"),h("writable"===e._state,"state must be writable");var r=e._writableStreamController;h(void 0!==r,"controller must not be undefined"),e._state="erroring",e._storedError=t;var n=e._writer;void 0!==n&&j(n,t),!1===R(e)&&!0===r._started&&x(e)}function x(e){h("erroring"===e._state,"stream._state === erroring"),h(!1===R(e),"WritableStreamHasOperationMarkedInFlight(stream) === false"),e._state="errored",e._writableStreamController.__errorSteps();for(var t=e._storedError,r=0;r<e._writeRequests.length;r++){e._writeRequests[r]._reject(t)}if(e._writeRequests=[],void 0!==e._pendingAbortRequest){var n=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,!0===n._wasAlreadyErroring)return n._reject(t),void T(e);e._writableStreamController.__abortSteps(n._reason).then(function(){n._resolve(),T(e)},function(t){n._reject(t),T(e)})}else T(e)}function C(e){h(void 0!==e._inFlightCloseRequest),e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0;var t=e._state;h("writable"===t||"erroring"===t),"erroring"===t&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&function(e){h(void 0!==e._closedPromise_resolve,"writer._closedPromise_resolve !== undefined"),h(void 0!==e._closedPromise_reject,"writer._closedPromise_reject !== undefined"),h("pending"===e._closedPromiseState,"writer._closedPromiseState is pending"),e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved"}(r),h(void 0===e._pendingAbortRequest,"stream._pendingAbortRequest === undefined"),h(void 0===e._storedError,"stream._storedError === undefined")}function P(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function R(e){return void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest}function T(e){h("errored"===e._state,'_stream_.[[state]] is `"errored"`'),void 0!==e._closeRequest&&(h(void 0===e._inFlightCloseRequest),e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var t=e._writer;void 0!==t&&($(t,e._storedError),t._closedPromise.catch(function(){}))}function E(e,t){h("writable"===e._state),h(!1===P(e));var r=e._writer;void 0!==r&&t!==e._backpressure&&(!0===t?function(e){h(void 0===e._readyPromise_resolve,"writer._readyPromise_resolve === undefined"),h(void 0===e._readyPromise_reject,"writer._readyPromise_reject === undefined"),e._readyPromise=new Promise(function(t,r){e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}(r):(h(!1===t),J(r))),e._backpressure=t}e.exports={AcquireWritableStreamDefaultWriter:_,IsWritableStream:b,IsWritableStreamLocked:A,WritableStream:y,WritableStreamAbort:S,WritableStreamDefaultControllerError:z,WritableStreamDefaultWriterCloseWithErrorPropagation:function(e){var t=e._ownerWritableStream;h(void 0!==t);var r=t._state;if(!0===P(t)||"closed"===r)return Promise.resolve();if("errored"===r)return Promise.reject(t._storedError);return h("writable"===r||"erroring"===r),L(e)},WritableStreamDefaultWriterRelease:N,WritableStreamDefaultWriterWrite:M,WritableStreamCloseQueuedOrInFlight:P};var I=function(){function e(t){if(i(this,e),!1===b(t))throw new TypeError("WritableStreamDefaultWriter can only be constructed with a WritableStream instance");if(!0===A(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;var r,n=t._state;if("writable"===n)!1===P(t)&&!0===t._backpressure?((r=this)._readyPromise=new Promise(function(e,t){r._readyPromise_resolve=e,r._readyPromise_reject=t}),r._readyPromiseState="pending"):Q(this),X(this);else if("erroring"===n)V(this,t._storedError),this._readyPromise.catch(function(){}),X(this);else if("closed"===n)Q(this),function(e){e._closedPromise=Promise.resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved"}(this);else{h("errored"===n,"state must be errored");var a=t._storedError;V(this,a),this._readyPromise.catch(function(){}),function(e,t){e._closedPromise=Promise.reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected"}(this,a),this._closedPromise.catch(function(){})}}return n(e,[{key:"abort",value:function(e){return!1===O(this)?Promise.reject(H("abort")):void 0===this._ownerWritableStream?Promise.reject(Y("abort")):function(e,t){var r=e._ownerWritableStream;return h(void 0!==r),S(r,t)}(this,e)}},{key:"close",value:function(){if(!1===O(this))return Promise.reject(H("close"));var e=this._ownerWritableStream;return void 0===e?Promise.reject(Y("close")):!0===P(e)?Promise.reject(new TypeError("cannot close an already-closing stream")):L(this)}},{key:"releaseLock",value:function(){if(!1===O(this))throw H("releaseLock");var e=this._ownerWritableStream;void 0!==e&&(h(void 0!==e._writer),N(this))}},{key:"write",value:function(e){return!1===O(this)?Promise.reject(H("write")):void 0===this._ownerWritableStream?Promise.reject(Y("write to")):M(this,e)}},{key:"closed",get:function(){return!1===O(this)?Promise.reject(H("closed")):this._closedPromise}},{key:"desiredSize",get:function(){if(!1===O(this))throw H("desiredSize");if(void 0===this._ownerWritableStream)throw Y("desiredSize");return function(e){var t=e._ownerWritableStream,r=t._state;if("errored"===r||"erroring"===r)return null;if("closed"===r)return 0;return q(t._writableStreamController)}(this)}},{key:"ready",get:function(){return!1===O(this)?Promise.reject(H("ready")):this._readyPromise}}]),e}();function O(e){return!!c(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")}function L(e){var t=e._ownerWritableStream;h(void 0!==t);var r=t._state;if("closed"===r||"errored"===r)return Promise.reject(new TypeError("The stream (in "+r+" state) is not in the writable state and cannot be closed"));h("writable"===r||"erroring"===r),h(!1===P(t));var n,i=new Promise(function(e,r){var n={_resolve:e,_reject:r};t._closeRequest=n});return!0===t._backpressure&&"writable"===r&&J(e),n=t._writableStreamController,v(n,"close",0),U(n),i}function F(e,t){"pending"===e._closedPromiseState?$(e,t):function(e,t){h(void 0===e._closedPromise_resolve,"writer._closedPromise_resolve === undefined"),h(void 0===e._closedPromise_reject,"writer._closedPromise_reject === undefined"),h("pending"!==e._closedPromiseState,"writer._closedPromiseState is not pending"),e._closedPromise=Promise.reject(t),e._closedPromiseState="rejected"}(e,t),e._closedPromise.catch(function(){})}function j(e,t){"pending"===e._readyPromiseState?function(e,t){h(void 0!==e._readyPromise_resolve,"writer._readyPromise_resolve !== undefined"),h(void 0!==e._readyPromise_reject,"writer._readyPromise_reject !== undefined"),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected"}(e,t):function(e,t){h(void 0===e._readyPromise_resolve,"writer._readyPromise_resolve === undefined"),h(void 0===e._readyPromise_reject,"writer._readyPromise_reject === undefined"),e._readyPromise=Promise.reject(t),e._readyPromiseState="rejected"}(e,t),e._readyPromise.catch(function(){})}function N(e){var t=e._ownerWritableStream;h(void 0!==t),h(t._writer===e);var r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");j(e,r),F(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function M(e,t){var r=e._ownerWritableStream;h(void 0!==r);var n=r._writableStreamController,i=function(e,t){var r=e._strategySize;if(void 0===r)return 1;try{return r(t)}catch(t){return W(e,t),1}}(n,t);if(r!==e._ownerWritableStream)return Promise.reject(Y("write to"));var a=r._state;if("errored"===a)return Promise.reject(r._storedError);if(!0===P(r)||"closed"===a)return Promise.reject(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return Promise.reject(r._storedError);h("writable"===a);var o=function(e){return h(!0===A(e)),h("writable"===e._state),new Promise(function(t,r){var n={_resolve:t,_reject:r};e._writeRequests.push(n)})}(r);return function(e,t,r){var n={chunk:t};try{v(e,n,r)}catch(t){return void W(e,t)}var i=e._controlledWritableStream;if(!1===P(i)&&"writable"===i._state){var a=B(e);E(i,a)}U(e)}(n,t,i),o}var D=function(){function e(t,r,n,a){if(i(this,e),!1===b(t))throw new TypeError("WritableStreamDefaultController can only be constructed with a WritableStream instance");if(void 0!==t._writableStreamController)throw new TypeError("WritableStreamDefaultController instances can only be created by the WritableStream constructor");this._controlledWritableStream=t,this._underlyingSink=r,this._queue=void 0,this._queueTotalSize=void 0,g(this),this._started=!1;var o=u(n,a);this._strategySize=o.size,this._strategyHWM=o.highWaterMark,E(t,B(this))}return n(e,[{key:"error",value:function(e){if(!1===function(e){if(!c(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_underlyingSink"))return!1;return!0}(this))throw new TypeError("WritableStreamDefaultController.prototype.error can only be used on a WritableStreamDefaultController");"writable"===this._controlledWritableStream._state&&z(this,e)}},{key:"__abortSteps",value:function(e){return s(this._underlyingSink,"abort",[e])}},{key:"__errorSteps",value:function(){g(this)}},{key:"__startSteps",value:function(){var e=this,t=o(this._underlyingSink,"start",[this]),r=this._controlledWritableStream;Promise.resolve(t).then(function(){h("writable"===r._state||"erroring"===r._state),e._started=!0,U(e)},function(t){h("writable"===r._state||"erroring"===r._state),e._started=!0,w(r,t)}).catch(f)}}]),e}();function q(e){return e._strategyHWM-e._queueTotalSize}function U(e){var t=e._controlledWritableStream;if(!1!==e._started&&void 0===t._inFlightWriteRequest){var r=t._state;if("closed"!==r&&"errored"!==r)if("erroring"!==r){if(0!==e._queue.length){var n=m(e);"close"===n?function(e){var t=e._controlledWritableStream;(function(e){h(void 0===e._inFlightCloseRequest),h(void 0!==e._closeRequest),e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),p(e),h(0===e._queue.length,"queue must be empty once the final write record is dequeued"),s(e._underlyingSink,"close",[]).then(function(){C(t)},function(e){!function(e,t){h(void 0!==e._inFlightCloseRequest),e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,h("writable"===e._state||"erroring"===e._state),void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),w(e,t)}(t,e)}).catch(f)}(e):function(e,t){var r=e._controlledWritableStream;(function(e){h(void 0===e._inFlightWriteRequest,"there must be no pending write request"),h(0!==e._writeRequests.length,"writeRequests must not be empty"),e._inFlightWriteRequest=e._writeRequests.shift()})(r),s(e._underlyingSink,"write",[t,e]).then(function(){!function(e){h(void 0!==e._inFlightWriteRequest),e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);var t=r._state;if(h("writable"===t||"erroring"===t),p(e),!1===P(r)&&"writable"===t){var n=B(e);E(r,n)}U(e)},function(e){!function(e,t){h(void 0!==e._inFlightWriteRequest),e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,h("writable"===e._state||"erroring"===e._state),w(e,t)}(r,e)}).catch(f)}(e,n.chunk)}}else x(t)}}function W(e,t){"writable"===e._controlledWritableStream._state&&z(e,t)}function B(e){return q(e)<=0}function z(e,t){var r=e._controlledWritableStream;h("writable"===r._state),k(r,t)}function G(e){return new TypeError("WritableStream.prototype."+e+" can only be used on a WritableStream")}function H(e){return new TypeError("WritableStreamDefaultWriter.prototype."+e+" can only be used on a WritableStreamDefaultWriter")}function Y(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function X(e){e._closedPromise=new Promise(function(t,r){e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}function $(e,t){h(void 0!==e._closedPromise_resolve,"writer._closedPromise_resolve !== undefined"),h(void 0!==e._closedPromise_reject,"writer._closedPromise_reject !== undefined"),h("pending"===e._closedPromiseState,"writer._closedPromiseState is pending"),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected"}function V(e,t){e._readyPromise=Promise.reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected"}function Q(e){e._readyPromise=Promise.resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled"}function J(e){h(void 0!==e._readyPromise_resolve,"writer._readyPromise_resolve !== undefined"),h(void 0!==e._readyPromise_reject,"writer._readyPromise_reject !== undefined"),e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled"}},function(e,t,r){var n=r(0).IsFiniteNonNegativeNumber,i=r(1).assert;t.DequeueValue=function(e){i("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: DequeueValue should only be used on containers with [[queue]] and [[queueTotalSize]]."),i(e._queue.length>0,"Spec-level failure: should never dequeue from an empty queue.");var t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value},t.EnqueueValueWithSize=function(e,t,r){if(i("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: EnqueueValueWithSize should only be used on containers with [[queue]] and [[queueTotalSize]]."),r=Number(r),!n(r))throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r},t.PeekQueueValue=function(e){return i("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: PeekQueueValue should only be used on containers with [[queue]] and [[queueTotalSize]]."),i(e._queue.length>0,"Spec-level failure: should never peek at an empty queue."),e._queue[0].value},t.ResetQueue=function(e){i("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: ResetQueue should only be used on containers with [[queue]] and [[queueTotalSize]]."),e._queue=[],e._queueTotalSize=0}},function(e,t,r){var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=r(0),o=a.ArrayBufferCopy,s=a.CreateIterResultObject,u=a.IsFiniteNonNegativeNumber,c=a.InvokeOrNoop,l=a.PromiseInvokeOrNoop,h=a.TransferArrayBuffer,f=a.ValidateAndNormalizeQueuingStrategy,d=a.ValidateAndNormalizeHighWaterMark,p=r(0),v=p.createArrayFromList,m=p.createDataProperty,g=p.typeIsObject,y=r(1),_=y.assert,b=y.rethrowAssertionErrorRejection,A=r(3),S=A.DequeueValue,w=A.EnqueueValueWithSize,k=A.ResetQueue,x=r(2),C=x.AcquireWritableStreamDefaultWriter,P=x.IsWritableStream,R=x.IsWritableStreamLocked,T=x.WritableStreamAbort,E=x.WritableStreamDefaultWriterCloseWithErrorPropagation,I=x.WritableStreamDefaultWriterRelease,O=x.WritableStreamDefaultWriterWrite,L=x.WritableStreamCloseQueuedOrInFlight,F=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.size,a=r.highWaterMark;i(this,e),this._state="readable",this._reader=void 0,this._storedError=void 0,this._disturbed=!1,this._readableStreamController=void 0;var o=t.type;if("bytes"===String(o))void 0===a&&(a=0),this._readableStreamController=new fe(this,t,a);else{if(void 0!==o)throw new RangeError("Invalid type is specified");void 0===a&&(a=1),this._readableStreamController=new ne(this,t,n,a)}}return n(e,[{key:"cancel",value:function(e){return!1===N(this)?Promise.reject(Te("cancel")):!0===M(this)?Promise.reject(new TypeError("Cannot cancel a stream that already has a reader")):W(this,e)}},{key:"getReader",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).mode;if(!1===N(this))throw Te("getReader");if(void 0===e)return j(this);if("byob"===(e=String(e)))return new Q(this);throw new RangeError("Invalid mode is specified")}},{key:"pipeThrough",value:function(e,t){var r=e.writable,n=e.readable;return function(e){try{Promise.prototype.then.call(e,void 0,function(){})}catch(e){}}(this.pipeTo(r,t)),n}},{key:"pipeTo",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.preventClose,i=r.preventAbort,a=r.preventCancel;if(!1===N(this))return Promise.reject(Te("pipeTo"));if(!1===P(e))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));if(n=Boolean(n),i=Boolean(i),a=Boolean(a),!0===M(this))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream"));if(!0===R(e))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream"));var o=j(this),s=C(e),u=!1,c=Promise.resolve();return new Promise(function(r,l){var h,f,d;if(m(t,o._closedPromise,function(t){!1===i?g(function(){return T(e,t)},!0,t):y(!0,t)}),m(e,s._closedPromise,function(e){!1===a?g(function(){return W(t,e)},!0,e):y(!0,e)}),h=t,f=o._closedPromise,d=function(){!1===n?g(function(){return E(s)}):y()},"closed"===h._state?d():f.then(d).catch(b),!0===L(e)||"closed"===e._state){var p=new TypeError("the destination writable stream closed before all data could be piped to it");!1===a?g(function(){return W(t,p)},!0,p):y(!0,p)}function v(){var e=c;return c.then(function(){return e!==c?v():void 0})}function m(e,t,r){"errored"===e._state?r(e._storedError):t.catch(r).catch(b)}function g(t,r,n){function i(){t().then(function(){return _(r,n)},function(e){return _(!0,e)}).catch(b)}!0!==u&&(u=!0,"writable"===e._state&&!1===L(e)?v().then(i):i())}function y(t,r){!0!==u&&(u=!0,"writable"===e._state&&!1===L(e)?v().then(function(){return _(t,r)}).catch(b):_(t,r))}function _(e,t){I(s),te(o),e?l(t):r(void 0)}(function e(){return c=Promise.resolve(),!0===u?Promise.resolve():s._readyPromise.then(function(){return re(o).then(function(e){var t=e.value;!0!==e.done&&(c=O(s,t).catch(function(){}))})}).then(e)})().catch(function(e){c=Promise.resolve(),b(e)})})}},{key:"tee",value:function(){if(!1===N(this))throw Te("tee");var e=D(this,!1);return v(e)}},{key:"locked",get:function(){if(!1===N(this))throw Te("locked");return M(this)}}]),e}();function j(e){return new V(e)}function N(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")}function M(e){return _(!0===N(e),"IsReadableStreamLocked should only be used on known readable streams"),void 0!==e._reader}function D(e,t){_(!0===N(e)),_("boolean"==typeof t);var r=j(e),n={closedOrErrored:!1,canceled1:!1,canceled2:!1,reason1:void 0,reason2:void 0};n.promise=new Promise(function(e){n._resolve=e});var i=function(){return function e(){var t=e._reader,r=e._branch1,n=e._branch2,i=e._teeState;return re(t).then(function(e){_(g(e));var t=e.value,a=e.done;if(_("boolean"==typeof a),!0===a&&!1===i.closedOrErrored&&(!1===i.canceled1&&oe(r),!1===i.canceled2&&oe(n),i.closedOrErrored=!0),!0!==i.closedOrErrored){var o=t,s=t;!1===i.canceled1&&se(r,o),!1===i.canceled2&&se(n,s)}})}}();i._reader=r,i._teeState=n,i._cloneForBranch2=t;var a=function(){return function e(t){var r=e._stream,n=e._teeState;n.canceled1=!0;n.reason1=t;if(!0===n.canceled2){var i=v([n.reason1,n.reason2]),a=W(r,i);n._resolve(a)}return n.promise}}();a._stream=e,a._teeState=n;var o=function(){return function e(t){var r=e._stream,n=e._teeState;n.canceled2=!0;n.reason2=t;if(!0===n.canceled1){var i=v([n.reason1,n.reason2]),a=W(r,i);n._resolve(a)}return n.promise}}();o._stream=e,o._teeState=n;var s=Object.create(Object.prototype);m(s,"pull",i),m(s,"cancel",a);var u=new F(s),c=Object.create(Object.prototype);m(c,"pull",i),m(c,"cancel",o);var l=new F(c);return i._branch1=u._readableStreamController,i._branch2=l._readableStreamController,r._closedPromise.catch(function(e){!0!==n.closedOrErrored&&(ue(i._branch1,e),ue(i._branch2,e),n.closedOrErrored=!0)}),[u,l]}function q(e){return _(!0===J(e._reader)),_("readable"===e._state||"closed"===e._state),new Promise(function(t,r){var n={_resolve:t,_reject:r};e._reader._readIntoRequests.push(n)})}function U(e){return _(!0===K(e._reader)),_("readable"===e._state),new Promise(function(t,r){var n={_resolve:t,_reject:r};e._reader._readRequests.push(n)})}function W(e,t){return e._disturbed=!0,"closed"===e._state?Promise.resolve(void 0):"errored"===e._state?Promise.reject(e._storedError):(B(e),e._readableStreamController.__cancelSteps(t).then(function(){}))}function B(e){_("readable"===e._state),e._state="closed";var t=e._reader;if(void 0!==t){if(!0===K(t)){for(var r=0;r<t._readRequests.length;r++){(0,t._readRequests[r]._resolve)(s(void 0,!0))}t._readRequests=[]}!function(e){_(void 0!==e._closedPromise_resolve),_(void 0!==e._closedPromise_reject),e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}(t)}}function z(e,t){_(!0===N(e),"stream must be ReadableStream"),_("readable"===e._state,"state must be readable"),e._state="errored",e._storedError=t;var r=e._reader;if(void 0!==r){if(!0===K(r)){for(var n=0;n<r._readRequests.length;n++){r._readRequests[n]._reject(t)}r._readRequests=[]}else{_(J(r),"reader must be ReadableStreamBYOBReader");for(var i=0;i<r._readIntoRequests.length;i++){r._readIntoRequests[i]._reject(t)}r._readIntoRequests=[]}Oe(r,t),r._closedPromise.catch(function(){})}}function G(e,t,r){var n=e._reader;_(n._readRequests.length>0),n._readRequests.shift()._resolve(s(t,r))}function H(e){return e._reader._readIntoRequests.length}function Y(e){return e._reader._readRequests.length}function X(e){var t=e._reader;return void 0!==t&&!1!==J(t)}function $(e){var t=e._reader;return void 0!==t&&!1!==K(t)}e.exports={ReadableStream:F,IsReadableStreamDisturbed:function(e){return _(!0===N(e),"IsReadableStreamDisturbed should only be used on known readable streams"),e._disturbed},ReadableStreamDefaultControllerClose:oe,ReadableStreamDefaultControllerEnqueue:se,ReadableStreamDefaultControllerError:ue,ReadableStreamDefaultControllerGetDesiredSize:le};var V=function(){function e(t){if(i(this,e),!1===N(t))throw new TypeError("ReadableStreamDefaultReader can only be constructed with a ReadableStream instance");if(!0===M(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");Z(this,t),this._readRequests=[]}return n(e,[{key:"cancel",value:function(e){return!1===K(this)?Promise.reject(Ie("cancel")):void 0===this._ownerReadableStream?Promise.reject(Ee("cancel")):ee(this,e)}},{key:"read",value:function(){return!1===K(this)?Promise.reject(Ie("read")):void 0===this._ownerReadableStream?Promise.reject(Ee("read from")):re(this)}},{key:"releaseLock",value:function(){if(!1===K(this))throw Ie("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");te(this)}}},{key:"closed",get:function(){return!1===K(this)?Promise.reject(Ie("closed")):this._closedPromise}}]),e}(),Q=function(){function e(t){if(i(this,e),!N(t))throw new TypeError("ReadableStreamBYOBReader can only be constructed with a ReadableStream instance given a byte source");if(!1===de(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");if(M(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");Z(this,t),this._readIntoRequests=[]}return n(e,[{key:"cancel",value:function(e){return J(this)?void 0===this._ownerReadableStream?Promise.reject(Ee("cancel")):ee(this,e):Promise.reject(Le("cancel"))}},{key:"read",value:function(e){return J(this)?void 0===this._ownerReadableStream?Promise.reject(Ee("read from")):ArrayBuffer.isView(e)?0===e.byteLength?Promise.reject(new TypeError("view must have non-zero byteLength")):function(e,t){var r=e._ownerReadableStream;if(_(void 0!==r),r._disturbed=!0,"errored"===r._state)return Promise.reject(r._storedError);return function(e,t){var r=e._controlledReadableStream,n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);var i=t.constructor,a={buffer:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,ctor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return a.buffer=h(a.buffer),e._pendingPullIntos.push(a),q(r);if("closed"===r._state){var o=new t.constructor(a.buffer,a.byteOffset,0);return Promise.resolve(s(o,!0))}if(e._queueTotalSize>0){if(!0===be(e,a)){var u=ye(a);return Se(e),Promise.resolve(s(u,!1))}if(!0===e._closeRequested){var c=new TypeError("Insufficient bytes to fill elements in the given buffer");return Pe(e,c),Promise.reject(c)}}a.buffer=h(a.buffer),e._pendingPullIntos.push(a);var l=q(r);return ve(e),l}(r._readableStreamController,t)}(this,e):Promise.reject(new TypeError("view must be an array buffer view")):Promise.reject(Le("read"))}},{key:"releaseLock",value:function(){if(!J(this))throw Le("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");te(this)}}},{key:"closed",get:function(){return J(this)?this._closedPromise:Promise.reject(Le("closed"))}}]),e}();function J(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")}function K(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")}function Z(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?function(e){e._closedPromise=new Promise(function(t,r){e._closedPromise_resolve=t,e._closedPromise_reject=r})}(e):"closed"===t._state?function(e){e._closedPromise=Promise.resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}(e):(_("errored"===t._state,"state must be errored"),function(e,t){e._closedPromise=Promise.reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}(e,t._storedError),e._closedPromise.catch(function(){}))}function ee(e,t){var r=e._ownerReadableStream;return _(void 0!==r),W(r,t)}function te(e){_(void 0!==e._ownerReadableStream),_(e._ownerReadableStream._reader===e),"readable"===e._ownerReadableStream._state?Oe(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,t){_(void 0===e._closedPromise_resolve),_(void 0===e._closedPromise_reject),e._closedPromise=Promise.reject(t)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._closedPromise.catch(function(){}),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function re(e){var t=e._ownerReadableStream;return _(void 0!==t),t._disturbed=!0,"closed"===t._state?Promise.resolve(s(void 0,!0)):"errored"===t._state?Promise.reject(t._storedError):(_("readable"===t._state),t._readableStreamController.__pullSteps())}var ne=function(){function e(t,r,n,a){if(i(this,e),!1===N(t))throw new TypeError("ReadableStreamDefaultController can only be constructed with a ReadableStream instance");if(void 0!==t._readableStreamController)throw new TypeError("ReadableStreamDefaultController instances can only be created by the ReadableStream constructor");this._controlledReadableStream=t,this._underlyingSource=r,this._queue=void 0,this._queueTotalSize=void 0,k(this),this._started=!1,this._closeRequested=!1,this._pullAgain=!1,this._pulling=!1;var o=f(n,a);this._strategySize=o.size,this._strategyHWM=o.highWaterMark;var s=this,u=c(r,"start",[this]);Promise.resolve(u).then(function(){s._started=!0,_(!1===s._pulling),_(!1===s._pullAgain),ae(s)},function(e){ce(s,e)}).catch(b)}return n(e,[{key:"close",value:function(){if(!1===ie(this))throw Fe("close");if(!0===this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");oe(this)}},{key:"enqueue",value:function(e){if(!1===ie(this))throw Fe("enqueue");if(!0===this._closeRequested)throw new TypeError("stream is closed or draining");var t=this._controlledReadableStream._state;if("readable"!==t)throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to");return se(this,e)}},{key:"error",value:function(e){if(!1===ie(this))throw Fe("error");var t=this._controlledReadableStream;if("readable"!==t._state)throw new TypeError("The stream is "+t._state+" and so cannot be errored");ue(this,e)}},{key:"__cancelSteps",value:function(e){return k(this),l(this._underlyingSource,"cancel",[e])}},{key:"__pullSteps",value:function(){var e=this._controlledReadableStream;if(this._queue.length>0){var t=S(this);return!0===this._closeRequested&&0===this._queue.length?B(e):ae(this),Promise.resolve(s(t,!1))}var r=U(e);return ae(this),r}},{key:"desiredSize",get:function(){if(!1===ie(this))throw Fe("desiredSize");return le(this)}}]),e}();function ie(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_underlyingSource")}function ae(e){!1!==function(e){var t=e._controlledReadableStream;if("closed"===t._state||"errored"===t._state)return!1;if(!0===e._closeRequested)return!1;if(!1===e._started)return!1;if(!0===M(t)&&Y(t)>0)return!0;if(le(e)>0)return!0;return!1}(e)&&(!0!==e._pulling?(_(!1===e._pullAgain),e._pulling=!0,l(e._underlyingSource,"pull",[e]).then(function(){if(e._pulling=!1,!0===e._pullAgain)return e._pullAgain=!1,ae(e)},function(t){ce(e,t)}).catch(b)):e._pullAgain=!0)}function oe(e){var t=e._controlledReadableStream;_(!1===e._closeRequested),_("readable"===t._state),e._closeRequested=!0,0===e._queue.length&&B(t)}function se(e,t){var r=e._controlledReadableStream;if(_(!1===e._closeRequested),_("readable"===r._state),!0===M(r)&&Y(r)>0)G(r,t,!1);else{var n=1;if(void 0!==e._strategySize){var i=e._strategySize;try{n=i(t)}catch(t){throw ce(e,t),t}}try{w(e,t,n)}catch(t){throw ce(e,t),t}}ae(e)}function ue(e,t){var r=e._controlledReadableStream;_("readable"===r._state),k(e),z(r,t)}function ce(e,t){"readable"===e._controlledReadableStream._state&&ue(e,t)}function le(e){var t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}var he=function(){function e(t,r){i(this,e),this._associatedReadableByteStreamController=t,this._view=r}return n(e,[{key:"respond",value:function(e){if(!1===pe(this))throw je("respond");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");!function(e,t){if(t=Number(t),!1===u(t))throw new RangeError("bytesWritten must be a finite");_(e._pendingPullIntos.length>0),xe(e,t)}(this._associatedReadableByteStreamController,e)}},{key:"respondWithNewView",value:function(e){if(!1===pe(this))throw je("respond");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");!function(e,t){_(e._pendingPullIntos.length>0);var r=e._pendingPullIntos[0];if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.byteLength!==t.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");r.buffer=t.buffer,xe(e,t.byteLength)}(this._associatedReadableByteStreamController,e)}},{key:"view",get:function(){return this._view}}]),e}(),fe=function(){function e(t,r,n){if(i(this,e),!1===N(t))throw new TypeError("ReadableByteStreamController can only be constructed with a ReadableStream instance given a byte source");if(void 0!==t._readableStreamController)throw new TypeError("ReadableByteStreamController instances can only be created by the ReadableStream constructor given a byte source");this._controlledReadableStream=t,this._underlyingByteSource=r,this._pullAgain=!1,this._pulling=!1,me(this),this._queue=this._queueTotalSize=void 0,k(this),this._closeRequested=!1,this._started=!1,this._strategyHWM=d(n);var a=r.autoAllocateChunkSize;if(void 0!==a&&(!1===Number.isInteger(a)||a<=0))throw new RangeError("autoAllocateChunkSize must be a positive integer");this._autoAllocateChunkSize=a,this._pendingPullIntos=[];var o=this,s=c(r,"start",[this]);Promise.resolve(s).then(function(){o._started=!0,_(!1===o._pulling),_(!1===o._pullAgain),ve(o)},function(e){"readable"===t._state&&Pe(o,e)}).catch(b)}return n(e,[{key:"close",value:function(){if(!1===de(this))throw Ne("close");if(!0===this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");!function(e){var t=e._controlledReadableStream;if(_(!1===e._closeRequested),_("readable"===t._state),e._queueTotalSize>0)return void(e._closeRequested=!0);if(e._pendingPullIntos.length>0){var r=e._pendingPullIntos[0];if(r.bytesFilled>0){var n=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Pe(e,n),n}}B(t)}(this)}},{key:"enqueue",value:function(e){if(!1===de(this))throw Ne("enqueue");if(!0===this._closeRequested)throw new TypeError("stream is closed or draining");var t=this._controlledReadableStream._state;if("readable"!==t)throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to");if(!ArrayBuffer.isView(e))throw new TypeError("You can only enqueue array buffer views when using a ReadableByteStreamController");!function(e,t){var r=e._controlledReadableStream;_(!1===e._closeRequested),_("readable"===r._state);var n=t.buffer,i=t.byteOffset,a=t.byteLength,o=h(n);if(!0===$(r))if(0===Y(r))_e(e,o,i,a);else{_(0===e._queue.length);var s=new Uint8Array(o,i,a);G(r,s,!1)}else!0===X(r)?(_e(e,o,i,a),ke(e)):(_(!1===M(r),"stream must not be locked"),_e(e,o,i,a))}(this,e)}},{key:"error",value:function(e){if(!1===de(this))throw Ne("error");var t=this._controlledReadableStream;if("readable"!==t._state)throw new TypeError("The stream is "+t._state+" and so cannot be errored");Pe(this,e)}},{key:"__cancelSteps",value:function(e){this._pendingPullIntos.length>0&&(this._pendingPullIntos[0].bytesFilled=0);return k(this),l(this._underlyingByteSource,"cancel",[e])}},{key:"__pullSteps",value:function(){var e=this._controlledReadableStream;if(_(!0===$(e)),this._queueTotalSize>0){_(0===Y(e));var t=this._queue.shift();this._queueTotalSize-=t.byteLength,Se(this);var r=void 0;try{r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}catch(e){return Promise.reject(e)}return Promise.resolve(s(r,!1))}var n=this._autoAllocateChunkSize;if(void 0!==n){var i=void 0;try{i=new ArrayBuffer(n)}catch(e){return Promise.reject(e)}var a={buffer:i,byteOffset:0,byteLength:n,bytesFilled:0,elementSize:1,ctor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(a)}var o=U(e);return ve(this),o}},{key:"byobRequest",get:function(){if(!1===de(this))throw Ne("byobRequest");if(void 0===this._byobRequest&&this._pendingPullIntos.length>0){var e=this._pendingPullIntos[0],t=new Uint8Array(e.buffer,e.byteOffset+e.bytesFilled,e.byteLength-e.bytesFilled);this._byobRequest=new he(this,t)}return this._byobRequest}},{key:"desiredSize",get:function(){if(!1===de(this))throw Ne("desiredSize");return Re(this)}}]),e}();function de(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_underlyingByteSource")}function pe(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")}function ve(e){!1!==function(e){var t=e._controlledReadableStream;if("readable"!==t._state)return!1;if(!0===e._closeRequested)return!1;if(!1===e._started)return!1;if(!0===$(t)&&Y(t)>0)return!0;if(!0===X(t)&&H(t)>0)return!0;if(Re(e)>0)return!0;return!1}(e)&&(!0!==e._pulling?(_(!1===e._pullAgain),e._pulling=!0,l(e._underlyingByteSource,"pull",[e]).then(function(){e._pulling=!1,!0===e._pullAgain&&(e._pullAgain=!1,ve(e))},function(t){"readable"===e._controlledReadableStream._state&&Pe(e,t)}).catch(b)):e._pullAgain=!0)}function me(e){we(e),e._pendingPullIntos=[]}function ge(e,t){_("errored"!==e._state,"state must not be errored");var r=!1;"closed"===e._state&&(_(0===t.bytesFilled),r=!0);var n=ye(t);"default"===t.readerType?G(e,n,r):(_("byob"===t.readerType),function(e,t,r){var n=e._reader;_(n._readIntoRequests.length>0),n._readIntoRequests.shift()._resolve(s(t,r))}(e,n,r))}function ye(e){var t=e.bytesFilled,r=e.elementSize;return _(t<=e.byteLength),_(t%r==0),new e.ctor(e.buffer,e.byteOffset,t/r)}function _e(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function be(e,t){var r=t.elementSize,n=t.bytesFilled-t.bytesFilled%r,i=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),a=t.bytesFilled+i,s=a-a%r,u=i,c=!1;s>n&&(u=s-t.bytesFilled,c=!0);for(var l=e._queue;u>0;){var h=l[0],f=Math.min(u,h.byteLength),d=t.byteOffset+t.bytesFilled;o(t.buffer,d,h.buffer,h.byteOffset,f),h.byteLength===f?l.shift():(h.byteOffset+=f,h.byteLength-=f),e._queueTotalSize-=f,Ae(e,f,t),u-=f}return!1===c&&(_(0===e._queueTotalSize,"queue must be empty"),_(t.bytesFilled>0),_(t.bytesFilled<t.elementSize)),c}function Ae(e,t,r){_(0===e._pendingPullIntos.length||e._pendingPullIntos[0]===r),we(e),r.bytesFilled+=t}function Se(e){_("readable"===e._controlledReadableStream._state),0===e._queueTotalSize&&!0===e._closeRequested?B(e._controlledReadableStream):ve(e)}function we(e){void 0!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=void 0,e._byobRequest=void 0)}function ke(e){for(_(!1===e._closeRequested);e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;var t=e._pendingPullIntos[0];!0===be(e,t)&&(Ce(e),ge(e._controlledReadableStream,t))}}function xe(e,t){var r=e._pendingPullIntos[0],n=e._controlledReadableStream;if("closed"===n._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream");!function(e,t){t.buffer=h(t.buffer),_(0===t.bytesFilled,"bytesFilled must be 0");var r=e._controlledReadableStream;if(!0===X(r))for(;H(r)>0;)ge(r,Ce(e))}(e,r)}else _("readable"===n._state),function(e,t,r){if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range");if(Ae(e,t,r),!(r.bytesFilled<r.elementSize)){Ce(e);var n=r.bytesFilled%r.elementSize;if(n>0){var i=r.byteOffset+r.bytesFilled,a=r.buffer.slice(i-n,i);_e(e,a,0,a.byteLength)}r.buffer=h(r.buffer),r.bytesFilled-=n,ge(e._controlledReadableStream,r),ke(e)}}(e,t,r)}function Ce(e){var t=e._pendingPullIntos.shift();return we(e),t}function Pe(e,t){var r=e._controlledReadableStream;_("readable"===r._state),me(e),k(e),z(r,t)}function Re(e){var t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function Te(e){return new TypeError("ReadableStream.prototype."+e+" can only be used on a ReadableStream")}function Ee(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function Ie(e){return new TypeError("ReadableStreamDefaultReader.prototype."+e+" can only be used on a ReadableStreamDefaultReader")}function Oe(e,t){_(void 0!==e._closedPromise_resolve),_(void 0!==e._closedPromise_reject),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function Le(e){return new TypeError("ReadableStreamBYOBReader.prototype."+e+" can only be used on a ReadableStreamBYOBReader")}function Fe(e){return new TypeError("ReadableStreamDefaultController.prototype."+e+" can only be used on a ReadableStreamDefaultController")}function je(e){return new TypeError("ReadableStreamBYOBRequest.prototype."+e+" can only be used on a ReadableStreamBYOBRequest")}function Ne(e){return new TypeError("ReadableByteStreamController.prototype."+e+" can only be used on a ReadableByteStreamController")}},function(e,t,r){var n=r(6),i=r(4),a=r(2);t.TransformStream=n.TransformStream,t.ReadableStream=i.ReadableStream,t.IsReadableStreamDisturbed=i.IsReadableStreamDisturbed,t.ReadableStreamDefaultControllerClose=i.ReadableStreamDefaultControllerClose,t.ReadableStreamDefaultControllerEnqueue=i.ReadableStreamDefaultControllerEnqueue,t.ReadableStreamDefaultControllerError=i.ReadableStreamDefaultControllerError,t.ReadableStreamDefaultControllerGetDesiredSize=i.ReadableStreamDefaultControllerGetDesiredSize,t.AcquireWritableStreamDefaultWriter=a.AcquireWritableStreamDefaultWriter,t.IsWritableStream=a.IsWritableStream,t.IsWritableStreamLocked=a.IsWritableStreamLocked,t.WritableStream=a.WritableStream,t.WritableStreamAbort=a.WritableStreamAbort,t.WritableStreamDefaultControllerError=a.WritableStreamDefaultControllerError,t.WritableStreamDefaultWriterCloseWithErrorPropagation=a.WritableStreamDefaultWriterCloseWithErrorPropagation,t.WritableStreamDefaultWriterRelease=a.WritableStreamDefaultWriterRelease,t.WritableStreamDefaultWriterWrite=a.WritableStreamDefaultWriterWrite},function(e,t,r){var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=r(1).assert,o=r(0),s=o.InvokeOrNoop,u=o.PromiseInvokeOrPerformFallback,c=o.PromiseInvokeOrNoop,l=o.typeIsObject,h=r(4),f=h.ReadableStream,d=h.ReadableStreamDefaultControllerClose,p=h.ReadableStreamDefaultControllerEnqueue,v=h.ReadableStreamDefaultControllerError,m=h.ReadableStreamDefaultControllerGetDesiredSize,g=r(2),y=g.WritableStream,_=g.WritableStreamDefaultControllerError;function b(e,t){if(!0===e._errored)throw new TypeError("TransformStream is already errored");if(!0===e._readableClosed)throw new TypeError("Readable side is already closed");var r=e._readableController;try{p(r,t)}catch(t){throw e._readableClosed=!0,S(e,t),e._storedError}!0===m(r)<=0&&!1===e._backpressure&&x(e,!0)}function A(e){a(!1===e._errored),a(!1===e._readableClosed);try{d(e._readableController)}catch(e){a(!1)}e._readableClosed=!0}function S(e,t){!1===e._errored&&w(e,t)}function w(e,t){a(!1===e._errored),e._errored=!0,e._storedError=t,!1===e._writableDone&&_(e._writableController,t),!1===e._readableClosed&&v(e._readableController,t)}function k(e){return a(void 0!==e._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),!1===e._backpressure?Promise.resolve():(a(!0===e._backpressure,"_backpressure should have been initialized"),e._backpressureChangePromise)}function x(e,t){a(e._backpressure!==t,"TransformStreamSetBackpressure() should be called only when backpressure is changed"),void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(t),e._backpressureChangePromise=new Promise(function(t){e._backpressureChangePromise_resolve=t}),e._backpressureChangePromise.then(function(e){a(e!==t,"_backpressureChangePromise should be fulfilled only when backpressure is changed")}),e._backpressure=t}function C(e,t){return b(t._controlledTransformStream,e),Promise.resolve()}function P(e){return!!l(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")}function R(e){return!!l(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")}var T=function(){function e(t,r){i(this,e),this._transformStream=t,this._startPromise=r}return n(e,[{key:"start",value:function(e){var t=this._transformStream;return t._writableController=e,this._startPromise.then(function(){return k(t)})}},{key:"write",value:function(e){return function(e,t){a(!1===e._errored),a(!1===e._transforming),a(!1===e._backpressure),e._transforming=!0;var r=e._transformer,n=e._transformStreamController;return u(r,"transform",[t,n],C,[t,n]).then(function(){return e._transforming=!1,k(e)},function(t){return S(e,t),Promise.reject(t)})}(this._transformStream,e)}},{key:"abort",value:function(){var e=this._transformStream;e._writableDone=!0,w(e,new TypeError("Writable side aborted"))}},{key:"close",value:function(){var e=this._transformStream;return a(!1===e._transforming),e._writableDone=!0,c(e._transformer,"flush",[e._transformStreamController]).then(function(){return!0===e._errored?Promise.reject(e._storedError):(!1===e._readableClosed&&A(e),Promise.resolve())}).catch(function(t){return S(e,t),Promise.reject(e._storedError)})}}]),e}(),E=function(){function e(t,r){i(this,e),this._transformStream=t,this._startPromise=r}return n(e,[{key:"start",value:function(e){var t=this._transformStream;return t._readableController=e,this._startPromise.then(function(){return a(void 0!==t._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),!0===t._backpressure?Promise.resolve():(a(!1===t._backpressure,"_backpressure should have been initialized"),t._backpressureChangePromise)})}},{key:"pull",value:function(){var e=this._transformStream;return a(!0===e._backpressure,"pull() should be never called while _backpressure is false"),a(void 0!==e._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),x(e,!1),e._backpressureChangePromise}},{key:"cancel",value:function(){var e=this._transformStream;e._readableClosed=!0,w(e,new TypeError("Readable side canceled"))}}]),e}(),I=function(){function e(t){if(i(this,e),!1===R(t))throw new TypeError("TransformStreamDefaultController can only be constructed with a TransformStream instance");if(void 0!==t._transformStreamController)throw new TypeError("TransformStreamDefaultController instances can only be created by the TransformStream constructor");this._controlledTransformStream=t}return n(e,[{key:"enqueue",value:function(e){if(!1===P(this))throw L("enqueue");b(this._controlledTransformStream,e)}},{key:"close",value:function(){if(!1===P(this))throw L("close");!function(e){if(!0===e._errored)throw new TypeError("TransformStream is already errored");if(!0===e._readableClosed)throw new TypeError("Readable side is already closed");A(e)}(this._controlledTransformStream)}},{key:"error",value:function(e){if(!1===P(this))throw L("error");!function(e,t){if(!0===e._errored)throw new TypeError("TransformStream is already errored");w(e,t)}(this._controlledTransformStream,e)}},{key:"desiredSize",get:function(){if(!1===P(this))throw L("desiredSize");var e=this._controlledTransformStream._readableController;return m(e)}}]),e}(),O=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};i(this,e),this._transformer=t;var r=t.readableStrategy,n=t.writableStrategy;this._transforming=!1,this._errored=!1,this._storedError=void 0,this._writableController=void 0,this._readableController=void 0,this._transformStreamController=void 0,this._writableDone=!1,this._readableClosed=!1,this._backpressure=void 0,this._backpressureChangePromise=void 0,this._backpressureChangePromise_resolve=void 0,this._transformStreamController=new I(this);var o=void 0,u=new Promise(function(e){o=e}),c=new E(this,u);this._readable=new f(c,r);var l=new T(this,u);this._writable=new y(l,n),a(void 0!==this._writableController),a(void 0!==this._readableController),x(this,m(this._readableController)<=0);var h=this,d=s(t,"start",[h._transformStreamController]);o(d),u.catch(function(e){!1===h._errored&&(h._errored=!0,h._storedError=e)})}return n(e,[{key:"readable",get:function(){if(!1===R(this))throw F("readable");return this._readable}},{key:"writable",get:function(){if(!1===R(this))throw F("writable");return this._writable}}]),e}();function L(e){return new TypeError("TransformStreamDefaultController.prototype."+e+" can only be used on a TransformStreamDefaultController")}function F(e){return new TypeError("TransformStream.prototype."+e+" can only be used on a TransformStream")}e.exports={TransformStream:O}},function(e,t,r){e.exports=r(5)}]))},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var i=!1;try{if("function"==typeof URL&&"object"===n(URL.prototype)&&"origin"in URL.prototype){var a=new URL("b","http://a");a.pathname="c%20d",i="http://a/c%20d"===a.href}}catch(e){}if(i)t.URL=URL;else{var o=r(146).URL,s=r(3).URL;s&&(o.createObjectURL=function(e){return s.createObjectURL.apply(s,arguments)},o.revokeObjectURL=function(e){s.revokeObjectURL(e)}),t.URL=o}},function(e,t,r){"use strict";!function(){var e=Object.create(null);e.ftp=21,e.file=0,e.gopher=70,e.http=80,e.https=443,e.ws=80,e.wss=443;var r=Object.create(null);function n(t){return void 0!==e[t]}function i(){f.call(this),this._isInvalid=!0}function a(e){return""===e&&i.call(this),e.toLowerCase()}function o(e){var t=e.charCodeAt(0);return t>32&&t<127&&-1===[34,35,60,62,63,96].indexOf(t)?e:encodeURIComponent(e)}function s(e){var t=e.charCodeAt(0);return t>32&&t<127&&-1===[34,35,60,62,96].indexOf(t)?e:encodeURIComponent(e)}r["%2e"]=".",r[".%2e"]="..",r["%2e."]="..",r["%2e%2e"]="..";var u,c=/[a-zA-Z]/,l=/[a-zA-Z0-9\+\-\.]/;function h(t,h,f){function d(e){_.push(e)}var p=h||"scheme start",v=0,m="",g=!1,y=!1,_=[];e:for(;(t[v-1]!==u||0===v)&&!this._isInvalid;){var b=t[v];switch(p){case"scheme start":if(!b||!c.test(b)){if(h){d("Invalid scheme.");break e}m="",p="no scheme";continue}m+=b.toLowerCase(),p="scheme";break;case"scheme":if(b&&l.test(b))m+=b.toLowerCase();else{if(":"!==b){if(h){if(b===u)break e;d("Code point not allowed in scheme: "+b);break e}m="",v=0,p="no scheme";continue}if(this._scheme=m,m="",h)break e;n(this._scheme)&&(this._isRelative=!0),p="file"===this._scheme?"relative":this._isRelative&&f&&f._scheme===this._scheme?"relative or authority":this._isRelative?"authority first slash":"scheme data"}break;case"scheme data":"?"===b?(this._query="?",p="query"):"#"===b?(this._fragment="#",p="fragment"):b!==u&&"\t"!==b&&"\n"!==b&&"\r"!==b&&(this._schemeData+=o(b));break;case"no scheme":if(f&&n(f._scheme)){p="relative";continue}d("Missing scheme."),i.call(this);break;case"relative or authority":if("/"!==b||"/"!==t[v+1]){d("Expected /, got: "+b),p="relative";continue}p="authority ignore slashes";break;case"relative":if(this._isRelative=!0,"file"!==this._scheme&&(this._scheme=f._scheme),b===u){this._host=f._host,this._port=f._port,this._path=f._path.slice(),this._query=f._query,this._username=f._username,this._password=f._password;break e}if("/"===b||"\\"===b)"\\"===b&&d("\\ is an invalid code point."),p="relative slash";else if("?"===b)this._host=f._host,this._port=f._port,this._path=f._path.slice(),this._query="?",this._username=f._username,this._password=f._password,p="query";else{if("#"!==b){var A=t[v+1],S=t[v+2];("file"!==this._scheme||!c.test(b)||":"!==A&&"|"!==A||S!==u&&"/"!==S&&"\\"!==S&&"?"!==S&&"#"!==S)&&(this._host=f._host,this._port=f._port,this._username=f._username,this._password=f._password,this._path=f._path.slice(),this._path.pop()),p="relative path";continue}this._host=f._host,this._port=f._port,this._path=f._path.slice(),this._query=f._query,this._fragment="#",this._username=f._username,this._password=f._password,p="fragment"}break;case"relative slash":if("/"!==b&&"\\"!==b){"file"!==this._scheme&&(this._host=f._host,this._port=f._port,this._username=f._username,this._password=f._password),p="relative path";continue}"\\"===b&&d("\\ is an invalid code point."),p="file"===this._scheme?"file host":"authority ignore slashes";break;case"authority first slash":if("/"!==b){d("Expected '/', got: "+b),p="authority ignore slashes";continue}p="authority second slash";break;case"authority second slash":if(p="authority ignore slashes","/"!==b){d("Expected '/', got: "+b);continue}break;case"authority ignore slashes":if("/"!==b&&"\\"!==b){p="authority";continue}d("Expected authority, got: "+b);break;case"authority":if("@"===b){g&&(d("@ already seen."),m+="%40"),g=!0;for(var w=0;w<m.length;w++){var k=m[w];if("\t"!==k&&"\n"!==k&&"\r"!==k)if(":"!==k||null!==this._password){var x=o(k);null!==this._password?this._password+=x:this._username+=x}else this._password="";else d("Invalid whitespace in authority.")}m=""}else{if(b===u||"/"===b||"\\"===b||"?"===b||"#"===b){v-=m.length,m="",p="host";continue}m+=b}break;case"file host":if(b===u||"/"===b||"\\"===b||"?"===b||"#"===b){2!==m.length||!c.test(m[0])||":"!==m[1]&&"|"!==m[1]?0===m.length?p="relative path start":(this._host=a.call(this,m),m="",p="relative path start"):p="relative path";continue}"\t"===b||"\n"===b||"\r"===b?d("Invalid whitespace in file host."):m+=b;break;case"host":case"hostname":if(":"!==b||y){if(b===u||"/"===b||"\\"===b||"?"===b||"#"===b){if(this._host=a.call(this,m),m="",p="relative path start",h)break e;continue}"\t"!==b&&"\n"!==b&&"\r"!==b?("["===b?y=!0:"]"===b&&(y=!1),m+=b):d("Invalid code point in host/hostname: "+b)}else if(this._host=a.call(this,m),m="",p="port","hostname"===h)break e;break;case"port":if(/[0-9]/.test(b))m+=b;else{if(b===u||"/"===b||"\\"===b||"?"===b||"#"===b||h){if(""!==m){var C=parseInt(m,10);C!==e[this._scheme]&&(this._port=C+""),m=""}if(h)break e;p="relative path start";continue}"\t"===b||"\n"===b||"\r"===b?d("Invalid code point in port: "+b):i.call(this)}break;case"relative path start":if("\\"===b&&d("'\\' not allowed in path."),p="relative path","/"!==b&&"\\"!==b)continue;break;case"relative path":var P;if(b!==u&&"/"!==b&&"\\"!==b&&(h||"?"!==b&&"#"!==b))"\t"!==b&&"\n"!==b&&"\r"!==b&&(m+=o(b));else"\\"===b&&d("\\ not allowed in relative path."),(P=r[m.toLowerCase()])&&(m=P),".."===m?(this._path.pop(),"/"!==b&&"\\"!==b&&this._path.push("")):"."===m&&"/"!==b&&"\\"!==b?this._path.push(""):"."!==m&&("file"===this._scheme&&0===this._path.length&&2===m.length&&c.test(m[0])&&"|"===m[1]&&(m=m[0]+":"),this._path.push(m)),m="","?"===b?(this._query="?",p="query"):"#"===b&&(this._fragment="#",p="fragment");break;case"query":h||"#"!==b?b!==u&&"\t"!==b&&"\n"!==b&&"\r"!==b&&(this._query+=s(b)):(this._fragment="#",p="fragment");break;case"fragment":b!==u&&"\t"!==b&&"\n"!==b&&"\r"!==b&&(this._fragment+=b)}v++}}function f(){this._scheme="",this._schemeData="",this._username="",this._password=null,this._host="",this._port="",this._path=[],this._query="",this._fragment="",this._isInvalid=!1,this._isRelative=!1}function d(e,t){void 0===t||t instanceof d||(t=new d(String(t))),this._url=e,f.call(this);var r=e.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g,"");h.call(this,r,null,t)}d.prototype={toString:function(){return this.href},get href(){if(this._isInvalid)return this._url;var e="";return""===this._username&&null===this._password||(e=this._username+(null!==this._password?":"+this._password:"")+"@"),this.protocol+(this._isRelative?"//"+e+this.host:"")+this.pathname+this._query+this._fragment},set href(e){f.call(this),h.call(this,e)},get protocol(){return this._scheme+":"},set protocol(e){this._isInvalid||h.call(this,e+":","scheme start")},get host(){return this._isInvalid?"":this._port?this._host+":"+this._port:this._host},set host(e){!this._isInvalid&&this._isRelative&&h.call(this,e,"host")},get hostname(){return this._host},set hostname(e){!this._isInvalid&&this._isRelative&&h.call(this,e,"hostname")},get port(){return this._port},set port(e){!this._isInvalid&&this._isRelative&&h.call(this,e,"port")},get pathname(){return this._isInvalid?"":this._isRelative?"/"+this._path.join("/"):this._schemeData},set pathname(e){!this._isInvalid&&this._isRelative&&(this._path=[],h.call(this,e,"relative path start"))},get search(){return this._isInvalid||!this._query||"?"===this._query?"":this._query},set search(e){!this._isInvalid&&this._isRelative&&(this._query="?","?"===e[0]&&(e=e.slice(1)),h.call(this,e,"query"))},get hash(){return this._isInvalid||!this._fragment||"#"===this._fragment?"":this._fragment},set hash(e){this._isInvalid||(this._fragment="#","#"===e[0]&&(e=e.slice(1)),h.call(this,e,"fragment"))},get origin(){var e;if(this._isInvalid||!this._scheme)return"";switch(this._scheme){case"data":case"file":case"javascript":case"mailto":return"null";case"blob":try{return new d(this._schemeData).origin||"null"}catch(e){}return"null"}return(e=this.host)?this._scheme+"://"+e:""}},t.URL=d}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDocument=function(e){var t,r=new L;if("string"==typeof e)t={url:e};else if((0,a.isArrayBuffer)(e))t={data:e};else if(e instanceof F)t={range:e};else{if("object"!==w(e))throw new Error("Invalid parameter in getDocument, need either Uint8Array, string or a parameter object");if(!e.url&&!e.data&&!e.range)throw new Error("Invalid parameter object: need either .data, .range or .url");t=e}var n=Object.create(null),i=null,s=null;for(var c in t)if("url"!==c||"undefined"==typeof window)if("range"!==c)if("worker"!==c)if("data"!==c||t[c]instanceof Uint8Array)n[c]=t[c];else{var l=t[c];if("string"==typeof l)n[c]=(0,a.stringToBytes)(l);else if("object"!==w(l)||null===l||isNaN(l.length)){if(!(0,a.isArrayBuffer)(l))throw new Error("Invalid PDF binary data: either typed array, string or array-like object is expected in the data property.");n[c]=new Uint8Array(l)}else n[c]=new Uint8Array(l)}else s=t[c];else i=t[c];else n[c]=new a.URL(t[c],window.location).href;n.rangeChunkSize=n.rangeChunkSize||x,n.CMapReaderFactory=n.CMapReaderFactory||o.DOMCMapReaderFactory,n.ignoreErrors=!0!==n.stopAtErrors,n.pdfBug=!0===n.pdfBug;var d=Object.values(a.NativeImageDecoding);void 0!==n.nativeImageDecoderSupport&&d.includes(n.nativeImageDecoderSupport)||(n.nativeImageDecoderSupport=u.apiCompatibilityParams.nativeImageDecoderSupport||a.NativeImageDecoding.DECODE);Number.isInteger(n.maxImageSize)||(n.maxImageSize=-1);"boolean"!=typeof n.isEvalSupported&&(n.isEvalSupported=!0);"boolean"!=typeof n.disableFontFace&&(n.disableFontFace=u.apiCompatibilityParams.disableFontFace||!1);"boolean"!=typeof n.disableRange&&(n.disableRange=!1);"boolean"!=typeof n.disableStream&&(n.disableStream=!1);"boolean"!=typeof n.disableAutoFetch&&(n.disableAutoFetch=!1);"boolean"!=typeof n.disableCreateObjectURL&&(n.disableCreateObjectURL=u.apiCompatibilityParams.disableCreateObjectURL||!1);if((0,a.setVerbosityLevel)(n.verbosity),!s){var v={postMessageTransfers:n.postMessageTransfers,verbosity:n.verbosity,port:h.GlobalWorkerOptions.workerPort};s=v.port?D.fromPort(v):new D(v),r._worker=s}var m=r.docId;return s.promise.then(function(){if(r.destroyed)throw new Error("Loading aborted");return function(e,t,r,n){return e.destroyed?Promise.reject(new Error("Worker was destroyed")):(r&&(t.length=r.length,t.initialData=r.initialData,t.progressiveDone=r.progressiveDone),e.messageHandler.sendWithPromise("GetDocRequest",{docId:n,apiVersion:"2.2.228",source:{data:t.data,url:t.url,password:t.password,disableAutoFetch:t.disableAutoFetch,rangeChunkSize:t.rangeChunkSize,length:t.length},maxImageSize:t.maxImageSize,disableFontFace:t.disableFontFace,disableCreateObjectURL:t.disableCreateObjectURL,postMessageTransfers:e.postMessageTransfers,docBaseUrl:t.docBaseUrl,nativeImageDecoderSupport:t.nativeImageDecoderSupport,ignoreErrors:t.ignoreErrors,isEvalSupported:t.isEvalSupported}).then(function(t){if(e.destroyed)throw new Error("Worker was destroyed");return t}))}(s,n,i,m).then(function(e){if(r.destroyed)throw new Error("Loading aborted");var t;i?t=new p.PDFDataTransportStream({length:n.length,initialData:n.initialData,progressiveDone:n.progressiveDone,disableRange:n.disableRange,disableStream:n.disableStream},i):n.data||(t=T({url:n.url,length:n.length,httpHeaders:n.httpHeaders,withCredentials:n.withCredentials,rangeChunkSize:n.rangeChunkSize,disableRange:n.disableRange,disableStream:n.disableStream}));var a=new f.MessageHandler(m,e,s.port);a.postMessageTransfers=s.postMessageTransfers;var o=new U(a,r,t,n);r._transport=o,a.send("Ready",null)})}).catch(r._capability.reject),r},t.setPDFNetworkStreamFactory=function(e){T=e},t.build=t.version=t.PDFPageProxy=t.PDFDocumentProxy=t.PDFWorker=t.PDFDataRangeTransport=t.LoopbackPort=void 0;var i=m(n(148)),a=n(1),o=n(151),s=n(152),u=n(153),c=n(154),l=m(n(3)),h=n(156),f=n(157),d=n(158),p=n(160),v=n(161);function m(e){return e&&e.__esModule?e:{default:e}}function g(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function _(e){return function(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function S(e,t,r){return t&&A(e.prototype,t),r&&A(e,r),e}function w(e){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var k,x=65536,C=!1,P=null,R=!1;"undefined"==typeof window?(C=!0,R=!0):R=!0,"undefined"!=typeof requirejs&&requirejs.toUrl&&(k=requirejs.toUrl("pdfjs-dist/build/pdf.worker.js"));var T,E="undefined"!=typeof requirejs&&requirejs.load;if(P=R?function(){return new Promise(function(e,t){r.e(22).then(function(){try{var n;n=r("wk5V"),e(n.WorkerMessageHandler)}catch(e){t(e)}}.bind(null,r)).catch(t)})}:E?function(){return new Promise(function(e,t){requirejs(["pdfjs-dist/build/pdf.worker"],function(r){try{e(r.WorkerMessageHandler)}catch(e){t(e)}},t)})}:null,!k&&"object"===("undefined"==typeof document?"undefined":w(document))&&"currentScript"in document){var I=document.currentScript&&document.currentScript.src;I&&(k=I.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}var O,L=(O=0,function(){function e(){b(this,e),this._capability=(0,a.createPromiseCapability)(),this._transport=null,this._worker=null,this.docId="d"+O++,this.destroyed=!1,this.onPassword=null,this.onProgress=null,this.onUnsupportedFeature=null}return S(e,[{key:"destroy",value:function(){var e=this;return this.destroyed=!0,(this._transport?this._transport.destroy():Promise.resolve()).then(function(){e._transport=null,e._worker&&(e._worker.destroy(),e._worker=null)})}},{key:"then",value:function(e,t){return(0,o.deprecated)("PDFDocumentLoadingTask.then method, use the `promise` getter instead."),this.promise.then.apply(this.promise,arguments)}},{key:"promise",get:function(){return this._capability.promise}}]),e}()),F=function(){function e(t,r){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];b(this,e),this.length=t,this.initialData=r,this.progressiveDone=n,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=(0,a.createPromiseCapability)()}return S(e,[{key:"addRangeListener",value:function(e){this._rangeListeners.push(e)}},{key:"addProgressListener",value:function(e){this._progressListeners.push(e)}},{key:"addProgressiveReadListener",value:function(e){this._progressiveReadListeners.push(e)}},{key:"addProgressiveDoneListener",value:function(e){this._progressiveDoneListeners.push(e)}},{key:"onDataRange",value:function(e,t){var r=!0,n=!1,i=void 0;try{for(var a,o=this._rangeListeners[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){(0,a.value)(e,t)}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}}},{key:"onDataProgress",value:function(e,t){var r=this;this._readyCapability.promise.then(function(){var n=!0,i=!1,a=void 0;try{for(var o,s=r._progressListeners[Symbol.iterator]();!(n=(o=s.next()).done);n=!0){(0,o.value)(e,t)}}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}})}},{key:"onDataProgressiveRead",value:function(e){var t=this;this._readyCapability.promise.then(function(){var r=!0,n=!1,i=void 0;try{for(var a,o=t._progressiveReadListeners[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){(0,a.value)(e)}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}})}},{key:"onDataProgressiveDone",value:function(){var e=this;this._readyCapability.promise.then(function(){var t=!0,r=!1,n=void 0;try{for(var i,a=e._progressiveDoneListeners[Symbol.iterator]();!(t=(i=a.next()).done);t=!0){(0,i.value)()}}catch(e){r=!0,n=e}finally{try{t||null==a.return||a.return()}finally{if(r)throw n}}})}},{key:"transportReady",value:function(){this._readyCapability.resolve()}},{key:"requestDataRange",value:function(e,t){(0,a.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}},{key:"abort",value:function(){}}]),e}();t.PDFDataRangeTransport=F;var j=function(){function e(t,r){b(this,e),this._pdfInfo=t,this._transport=r}return S(e,[{key:"getPage",value:function(e){return this._transport.getPage(e)}},{key:"getPageIndex",value:function(e){return this._transport.getPageIndex(e)}},{key:"getDestinations",value:function(){return this._transport.getDestinations()}},{key:"getDestination",value:function(e){return this._transport.getDestination(e)}},{key:"getPageLabels",value:function(){return this._transport.getPageLabels()}},{key:"getPageLayout",value:function(){return this._transport.getPageLayout()}},{key:"getPageMode",value:function(){return this._transport.getPageMode()}},{key:"getViewerPreferences",value:function(){return this._transport.getViewerPreferences()}},{key:"getOpenActionDestination",value:function(){return this._transport.getOpenActionDestination()}},{key:"getAttachments",value:function(){return this._transport.getAttachments()}},{key:"getJavaScript",value:function(){return this._transport.getJavaScript()}},{key:"getOutline",value:function(){return this._transport.getOutline()}},{key:"getPermissions",value:function(){return this._transport.getPermissions()}},{key:"getMetadata",value:function(){return this._transport.getMetadata()}},{key:"getData",value:function(){return this._transport.getData()}},{key:"getDownloadInfo",value:function(){return this._transport.downloadInfoCapability.promise}},{key:"getStats",value:function(){return this._transport.getStats()}},{key:"cleanup",value:function(){this._transport.startCleanup()}},{key:"destroy",value:function(){return this.loadingTask.destroy()}},{key:"numPages",get:function(){return this._pdfInfo.numPages}},{key:"fingerprint",get:function(){return this._pdfInfo.fingerprint}},{key:"loadingParams",get:function(){return this._transport.loadingParams}},{key:"loadingTask",get:function(){return this._transport.loadingTask}}]),e}();t.PDFDocumentProxy=j;var N=function(){function e(t,r,n){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];b(this,e),this.pageIndex=t,this._pageInfo=r,this._transport=n,this._stats=i?new o.StatTimer:o.DummyStatTimer,this._pdfBug=i,this.commonObjs=n.commonObjs,this.objs=new W,this.cleanupAfterRender=!1,this.pendingCleanup=!1,this.intentStates=Object.create(null),this.destroyed=!1}return S(e,[{key:"getViewport",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.scale,r=e.rotation,n=void 0===r?this.rotate:r,i=e.dontFlip,a=void 0!==i&&i;return(arguments.length>1||"number"==typeof arguments[0])&&((0,o.deprecated)("getViewport is called with obsolete arguments."),t=arguments[0],n="number"==typeof arguments[1]?arguments[1]:this.rotate,a="boolean"==typeof arguments[2]&&arguments[2]),new o.PageViewport({viewBox:this.view,scale:t,rotation:n,dontFlip:a})}},{key:"getAnnotations",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).intent,t=void 0===e?null:e;return this.annotationsPromise&&this.annotationsIntent===t||(this.annotationsPromise=this._transport.getAnnotations(this.pageIndex,t),this.annotationsIntent=t),this.annotationsPromise}},{key:"render",value:function(e){var t=this,r=e.canvasContext,n=e.viewport,i=e.intent,s=void 0===i?"display":i,u=e.enableWebGL,c=void 0!==u&&u,l=e.renderInteractiveForms,h=void 0!==l&&l,f=e.transform,d=void 0===f?null:f,p=e.imageLayer,m=void 0===p?null:p,g=e.canvasFactory,y=void 0===g?null:g,_=e.background,b=void 0===_?null:_,A=this._stats;A.time("Overall"),this.pendingCleanup=!1;var S="print"===s?"print":"display",w=y||new o.DOMCanvasFactory,k=new v.WebGLContext({enable:c});this.intentStates[S]||(this.intentStates[S]=Object.create(null));var x=this.intentStates[S];x.displayReadyCapability||(x.receivingOperatorList=!0,x.displayReadyCapability=(0,a.createPromiseCapability)(),x.operatorList={fnArray:[],argsArray:[],lastChunk:!1},A.time("Page Request"),this._transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageNumber-1,intent:S,renderInteractiveForms:!0===h}));var C=function(e){var r=x.renderTasks.indexOf(P);r>=0&&x.renderTasks.splice(r,1),(t.cleanupAfterRender||"print"===S)&&(t.pendingCleanup=!0),t._tryCleanup(),e?P.capability.reject(e):P.capability.resolve(),A.timeEnd("Rendering"),A.timeEnd("Overall")},P=new z({callback:C,params:{canvasContext:r,viewport:n,transform:d,imageLayer:m,background:b},objs:this.objs,commonObjs:this.commonObjs,operatorList:x.operatorList,pageNumber:this.pageNumber,canvasFactory:w,webGLContext:k,useRequestAnimationFrame:"print"!==S,pdfBug:this._pdfBug});x.renderTasks||(x.renderTasks=[]),x.renderTasks.push(P);var R=P.task;return x.displayReadyCapability.promise.then(function(e){t.pendingCleanup?C():(A.time("Rendering"),P.initializeGraphics(e),P.operatorListChanged())}).catch(C),R}},{key:"getOperatorList",value:function(){this.intentStates.oplist||(this.intentStates.oplist=Object.create(null));var e,t=this.intentStates.oplist;return t.opListReadCapability||((e={}).operatorListChanged=function(){if(t.operatorList.lastChunk){t.opListReadCapability.resolve(t.operatorList);var r=t.renderTasks.indexOf(e);r>=0&&t.renderTasks.splice(r,1)}},t.receivingOperatorList=!0,t.opListReadCapability=(0,a.createPromiseCapability)(),t.renderTasks=[],t.renderTasks.push(e),t.operatorList={fnArray:[],argsArray:[],lastChunk:!1},this._stats.time("Page Request"),this._transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageIndex,intent:"oplist"})),t.opListReadCapability.promise}},{key:"streamTextContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.normalizeWhitespace,r=void 0!==t&&t,n=e.disableCombineTextItems,i=void 0!==n&&n;return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this.pageNumber-1,normalizeWhitespace:!0===r,combineTextItems:!0!==i},{highWaterMark:100,size:function(e){return e.items.length}})}},{key:"getTextContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.streamTextContent(e);return new Promise(function(e,r){var n=t.getReader(),i={items:[],styles:Object.create(null)};!function t(){n.read().then(function(r){var n,a=r.value;r.done?e(i):(Object.assign(i.styles,a.styles),(n=i.items).push.apply(n,_(a.items)),t())},r)}()})}},{key:"_destroy",value:function(){this.destroyed=!0,this._transport.pageCache[this.pageIndex]=null;var e=[];return Object.keys(this.intentStates).forEach(function(t){"oplist"!==t&&this.intentStates[t].renderTasks.forEach(function(t){var r=t.capability.promise.catch(function(){});e.push(r),t.cancel()})},this),this.objs.clear(),this.annotationsPromise=null,this.pendingCleanup=!1,Promise.all(e)}},{key:"cleanup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.pendingCleanup=!0,this._tryCleanup(e)}},{key:"_tryCleanup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.pendingCleanup&&!Object.keys(this.intentStates).some(function(e){var t=this.intentStates[e];return 0!==t.renderTasks.length||t.receivingOperatorList},this)&&(Object.keys(this.intentStates).forEach(function(e){delete this.intentStates[e]},this),this.objs.clear(),this.annotationsPromise=null,e&&this._stats instanceof o.StatTimer&&(this._stats=new o.StatTimer),this.pendingCleanup=!1)}},{key:"_startRenderPage",value:function(e,t){var r=this.intentStates[t];r.displayReadyCapability&&r.displayReadyCapability.resolve(e)}},{key:"_renderPageChunk",value:function(e,t){for(var r=this.intentStates[t],n=0,i=e.length;n<i;n++)r.operatorList.fnArray.push(e.fnArray[n]),r.operatorList.argsArray.push(e.argsArray[n]);r.operatorList.lastChunk=e.lastChunk;for(var a=0;a<r.renderTasks.length;a++)r.renderTasks[a].operatorListChanged();e.lastChunk&&(r.receivingOperatorList=!1,this._tryCleanup())}},{key:"pageNumber",get:function(){return this.pageIndex+1}},{key:"rotate",get:function(){return this._pageInfo.rotate}},{key:"ref",get:function(){return this._pageInfo.ref}},{key:"userUnit",get:function(){return this._pageInfo.userUnit}},{key:"view",get:function(){return this._pageInfo.view}},{key:"stats",get:function(){return this._stats instanceof o.StatTimer?this._stats:null}}]),e}();t.PDFPageProxy=N;var M=function(){function e(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];b(this,e),this._listeners=[],this._defer=t,this._deferred=Promise.resolve(void 0)}return S(e,[{key:"postMessage",value:function(e,t){var r=this;if(this._defer){var n=new WeakMap,i={data:function e(r){if("object"!==w(r)||null===r)return r;if(n.has(r))return n.get(r);var i,o;if((i=r.buffer)&&(0,a.isArrayBuffer)(i)){var s=t&&t.includes(i);return o=r===i?r:s?new r.constructor(i,r.byteOffset,r.byteLength):new r.constructor(r),n.set(r,o),o}for(var u in o=Array.isArray(r)?[]:{},n.set(r,o),r){for(var c=void 0,l=r;!(c=Object.getOwnPropertyDescriptor(l,u));)l=Object.getPrototypeOf(l);void 0!==c.value&&"function"!=typeof c.value&&(o[u]=e(c.value))}return o}(e)};this._deferred.then(function(){r._listeners.forEach(function(e){e.call(this,i)},r)})}else this._listeners.forEach(function(t){t.call(this,{data:e})},this)}},{key:"addEventListener",value:function(e,t){this._listeners.push(t)}},{key:"removeEventListener",value:function(e,t){var r=this._listeners.indexOf(t);this._listeners.splice(r,1)}},{key:"terminate",value:function(){this._listeners.length=0}}]),e}();t.LoopbackPort=M;var D=function(){var e,t=new WeakMap,r=0;function n(){if(h.GlobalWorkerOptions.workerSrc)return h.GlobalWorkerOptions.workerSrc;if(void 0!==k)return k;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}function i(){try{if("undefined"!=typeof window)return window.pdfjsWorker&&window.pdfjsWorker.WorkerMessageHandler}catch(e){}return null}return function(){function s(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.name,n=void 0===r?null:r,i=e.port,o=void 0===i?null:i,u=e.postMessageTransfers,c=void 0===u||u,l=e.verbosity,h=void 0===l?(0,a.getVerbosityLevel)():l;if(b(this,s),o&&t.has(o))throw new Error("Cannot use more than one PDFWorker per port");if(this.name=n,this.destroyed=!1,this.postMessageTransfers=!1!==c,this.verbosity=h,this._readyCapability=(0,a.createPromiseCapability)(),this._port=null,this._webWorker=null,this._messageHandler=null,o)return t.set(o,this),void this._initializeFromPort(o);this._initialize()}return S(s,[{key:"_initializeFromPort",value:function(e){this._port=e,this._messageHandler=new f.MessageHandler("main","worker",e),this._messageHandler.on("ready",function(){}),this._readyCapability.resolve()}},{key:"_initialize",value:function(){var e,t,r=this;if("undefined"!=typeof Worker&&!C&&!i()){var o=n();try{(0,a.isSameOrigin)(window.location.href,o)||(e=new a.URL(o,window.location).href,t="importScripts('"+e+"');",o=a.URL.createObjectURL(new Blob([t])));var s=new Worker(o),u=new f.MessageHandler("main","worker",s),c=function(){s.removeEventListener("error",l),u.destroy(),s.terminate(),r.destroyed?r._readyCapability.reject(new Error("Worker was destroyed")):r._setupFakeWorker()},l=function(){r._webWorker||c()};s.addEventListener("error",l),u.on("test",function(e){s.removeEventListener("error",l),r.destroyed?c():e&&e.supportTypedArray?(r._messageHandler=u,r._port=s,r._webWorker=s,e.supportTransfers||(r.postMessageTransfers=!1),r._readyCapability.resolve(),u.send("configure",{verbosity:r.verbosity})):(r._setupFakeWorker(),u.destroy(),s.terminate())}),u.on("ready",function(e){if(s.removeEventListener("error",l),r.destroyed)c();else try{h()}catch(e){r._setupFakeWorker()}});var h=function(){var e=new Uint8Array([r.postMessageTransfers?255:0]);try{u.send("test",e,[e.buffer])}catch(t){(0,a.info)("Cannot use postMessage transfers"),e[0]=0,u.send("test",e)}};return void h()}catch(e){(0,a.info)("The worker has been disabled.")}}this._setupFakeWorker()}},{key:"_setupFakeWorker",value:function(){var t=this;C||((0,a.warn)("Setting up fake worker."),C=!0),function(){if(e)return e.promise;e=(0,a.createPromiseCapability)();var t=i();return t?(e.resolve(t),e.promise):((P||function(){return(0,o.loadScript)(n()).then(function(){return window.pdfjsWorker.WorkerMessageHandler})})().then(e.resolve,e.reject),e.promise)}().then(function(e){if(t.destroyed)t._readyCapability.reject(new Error("Worker was destroyed"));else{var n=new M;t._port=n;var i="fake"+r++,a=new f.MessageHandler(i+"_worker",i,n);e.setup(a,n);var o=new f.MessageHandler(i,i+"_worker",n);t._messageHandler=o,t._readyCapability.resolve()}}).catch(function(e){t._readyCapability.reject(new Error('Setting up fake worker failed: "'.concat(e.message,'".')))})}},{key:"destroy",value:function(){this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),t.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}},{key:"promise",get:function(){return this._readyCapability.promise}},{key:"port",get:function(){return this._port}},{key:"messageHandler",get:function(){return this._messageHandler}}],[{key:"fromPort",value:function(e){if(!e||!e.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return t.has(e.port)?t.get(e.port):new s(e)}},{key:"getWorkerSrc",value:function(){return n()}}]),s}()}();t.PDFWorker=D;var q,U=function(){function e(t,r,n,i){b(this,e),this.messageHandler=t,this.loadingTask=r,this.commonObjs=new W,this.fontLoader=new s.FontLoader({docId:r.docId,onUnsupportedFeature:this._onUnsupportedFeature.bind(this)}),this._params=i,this.CMapReaderFactory=new i.CMapReaderFactory({baseUrl:i.cMapUrl,isCompressed:i.cMapPacked}),this.destroyed=!1,this.destroyCapability=null,this._passwordCapability=null,this._networkStream=n,this._fullReader=null,this._lastProgress=null,this.pageCache=[],this.pagePromises=[],this.downloadInfoCapability=(0,a.createPromiseCapability)(),this.setupMessageHandler()}return S(e,[{key:"destroy",value:function(){var e=this;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=(0,a.createPromiseCapability)(),this._passwordCapability&&this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"));var t=[];this.pageCache.forEach(function(e){e&&t.push(e._destroy())}),this.pageCache.length=0,this.pagePromises.length=0;var r=this.messageHandler.sendWithPromise("Terminate",null);return t.push(r),Promise.all(t).then(function(){e.fontLoader.clear(),e._networkStream&&e._networkStream.cancelAllRequests(),e.messageHandler&&(e.messageHandler.destroy(),e.messageHandler=null),e.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}},{key:"setupMessageHandler",value:function(){var e=this.messageHandler,t=this.loadingTask;e.on("GetReader",function(e,t){var r=this;(0,a.assert)(this._networkStream),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=function(e){r._lastProgress={loaded:e.loaded,total:e.total}},t.onPull=function(){r._fullReader.read().then(function(e){var r=e.value;e.done?t.close():((0,a.assert)((0,a.isArrayBuffer)(r)),t.enqueue(new Uint8Array(r),1,[r]))}).catch(function(e){t.error(e)})},t.onCancel=function(e){r._fullReader.cancel(e)}},this),e.on("ReaderHeadersReady",function(e){var r=this,n=(0,a.createPromiseCapability)(),i=this._fullReader;return i.headersReady.then(function(){i.isStreamingSupported&&i.isRangeSupported||(r._lastProgress&&t.onProgress&&t.onProgress(r._lastProgress),i.onProgress=function(e){t.onProgress&&t.onProgress({loaded:e.loaded,total:e.total})}),n.resolve({isStreamingSupported:i.isStreamingSupported,isRangeSupported:i.isRangeSupported,contentLength:i.contentLength})},n.reject),n.promise},this),e.on("GetRangeReader",function(e,t){(0,a.assert)(this._networkStream);var r=this._networkStream.getRangeReader(e.begin,e.end);r?(t.onPull=function(){r.read().then(function(e){var r=e.value;e.done?t.close():((0,a.assert)((0,a.isArrayBuffer)(r)),t.enqueue(new Uint8Array(r),1,[r]))}).catch(function(e){t.error(e)})},t.onCancel=function(e){r.cancel(e)}):t.close()},this),e.on("GetDoc",function(e){var r=e.pdfInfo;this._numPages=r.numPages,t._capability.resolve(new j(r,this))},this),e.on("PasswordRequest",function(e){var r=this;if(this._passwordCapability=(0,a.createPromiseCapability)(),t.onPassword){try{t.onPassword(function(e){r._passwordCapability.resolve({password:e})},e.code)}catch(e){this._passwordCapability.reject(e)}}else this._passwordCapability.reject(new a.PasswordException(e.message,e.code));return this._passwordCapability.promise},this),e.on("PasswordException",function(e){t._capability.reject(new a.PasswordException(e.message,e.code))},this),e.on("InvalidPDF",function(e){t._capability.reject(new a.InvalidPDFException(e.message))},this),e.on("MissingPDF",function(e){t._capability.reject(new a.MissingPDFException(e.message))},this),e.on("UnexpectedResponse",function(e){t._capability.reject(new a.UnexpectedResponseException(e.message,e.status))},this),e.on("UnknownError",function(e){t._capability.reject(new a.UnknownErrorException(e.message,e.details))},this),e.on("DataLoaded",function(e){t.onProgress&&t.onProgress({loaded:e.length,total:e.length}),this.downloadInfoCapability.resolve(e)},this),e.on("StartRenderPage",function(e){if(!this.destroyed){var t=this.pageCache[e.pageIndex];t._stats.timeEnd("Page Request"),t._startRenderPage(e.transparency,e.intent)}},this),e.on("RenderPageChunk",function(e){this.destroyed||this.pageCache[e.pageIndex]._renderPageChunk(e.operatorList,e.intent)},this),e.on("commonobj",function(t){var r=this;if(!this.destroyed){var n=y(t,3),i=n[0],o=n[1],u=n[2];if(!this.commonObjs.has(i))switch(o){case"Font":var c=this._params;if("error"in u){var h=u.error;(0,a.warn)("Error during font loading: ".concat(h)),this.commonObjs.resolve(i,h);break}var f=null;c.pdfBug&&l.default.FontInspector&&l.default.FontInspector.enabled&&(f={registerFont:function(e,t){l.default.FontInspector.fontAdded(e,t)}});var d=new s.FontFaceObject(u,{isEvalSupported:c.isEvalSupported,disableFontFace:c.disableFontFace,ignoreErrors:c.ignoreErrors,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),fontRegistry:f});this.fontLoader.bind(d).then(function(){r.commonObjs.resolve(i,d)},function(t){e.sendWithPromise("FontFallback",{id:i}).finally(function(){r.commonObjs.resolve(i,d)})});break;case"FontPath":case"FontType3Res":this.commonObjs.resolve(i,u);break;default:throw new Error("Got unknown common object type ".concat(o))}}},this),e.on("obj",function(e){if(!this.destroyed){var t=y(e,4),r=t[0],n=t[1],i=t[2],a=t[3],s=this.pageCache[n];if(!s.objs.has(r))switch(i){case"JpegStream":return new Promise(function(e,t){var r=new Image;r.onload=function(){e(r)},r.onerror=function(){t(new Error("Error during JPEG image loading")),(0,o.releaseImageResources)(r)},r.src=a}).then(function(e){s.objs.resolve(r,e)});case"Image":s.objs.resolve(r,a);a&&"data"in a&&a.data.length>8e6&&(s.cleanupAfterRender=!0);break;default:throw new Error("Got unknown object type ".concat(i))}}},this),e.on("DocProgress",function(e){this.destroyed||t.onProgress&&t.onProgress({loaded:e.loaded,total:e.total})},this),e.on("PageError",function(e){if(!this.destroyed){var t=this.pageCache[e.pageIndex].intentStates[e.intent];if(!t.displayReadyCapability)throw new Error(e.error);if(t.displayReadyCapability.reject(new Error(e.error)),t.operatorList){t.operatorList.lastChunk=!0;for(var r=0;r<t.renderTasks.length;r++)t.renderTasks[r].operatorListChanged()}}},this),e.on("UnsupportedFeature",this._onUnsupportedFeature,this),e.on("JpegDecode",function(e){if(this.destroyed)return Promise.reject(new Error("Worker was destroyed"));if("undefined"==typeof document)return Promise.reject(new Error('"document" is not defined.'));var t=y(e,2),r=t[0],n=t[1];return 3!==n&&1!==n?Promise.reject(new Error("Only 3 components or 1 component can be returned")):new Promise(function(e,t){var i=new Image;i.onload=function(){var t=i.width,r=i.height,a=t*r,s=4*a,u=new Uint8ClampedArray(a*n),c=document.createElement("canvas");c.width=t,c.height=r;var l=c.getContext("2d");l.drawImage(i,0,0);var h=l.getImageData(0,0,t,r).data;if(3===n)for(var f=0,d=0;f<s;f+=4,d+=3)u[d]=h[f],u[d+1]=h[f+1],u[d+2]=h[f+2];else if(1===n)for(var p=0,v=0;p<s;p+=4,v++)u[v]=h[p];e({data:u,width:t,height:r}),(0,o.releaseImageResources)(i),c.width=0,c.height=0,c=null,l=null},i.onerror=function(){t(new Error("JpegDecode failed to load image")),(0,o.releaseImageResources)(i)},i.src=r})},this),e.on("FetchBuiltInCMap",function(e){return this.destroyed?Promise.reject(new Error("Worker was destroyed")):this.CMapReaderFactory.fetch({name:e.name})},this)}},{key:"_onUnsupportedFeature",value:function(e){var t=e.featureId;this.destroyed||this.loadingTask.onUnsupportedFeature&&this.loadingTask.onUnsupportedFeature(t)}},{key:"getData",value:function(){return this.messageHandler.sendWithPromise("GetData",null)}},{key:"getPage",value:function(e){var t=this;if(!Number.isInteger(e)||e<=0||e>this._numPages)return Promise.reject(new Error("Invalid page request"));var r=e-1;if(r in this.pagePromises)return this.pagePromises[r];var n=this.messageHandler.sendWithPromise("GetPage",{pageIndex:r}).then(function(e){if(t.destroyed)throw new Error("Transport destroyed");var n=new N(r,e,t,t._params.pdfBug);return t.pageCache[r]=n,n});return this.pagePromises[r]=n,n}},{key:"getPageIndex",value:function(e){return this.messageHandler.sendWithPromise("GetPageIndex",{ref:e}).catch(function(e){return Promise.reject(new Error(e))})}},{key:"getAnnotations",value:function(e,t){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:e,intent:t})}},{key:"getDestinations",value:function(){return this.messageHandler.sendWithPromise("GetDestinations",null)}},{key:"getDestination",value:function(e){return"string"!=typeof e?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:e})}},{key:"getPageLabels",value:function(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}},{key:"getPageLayout",value:function(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}},{key:"getPageMode",value:function(){return this.messageHandler.sendWithPromise("GetPageMode",null)}},{key:"getViewerPreferences",value:function(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}},{key:"getOpenActionDestination",value:function(){return this.messageHandler.sendWithPromise("GetOpenActionDestination",null)}},{key:"getAttachments",value:function(){return this.messageHandler.sendWithPromise("GetAttachments",null)}},{key:"getJavaScript",value:function(){return this.messageHandler.sendWithPromise("GetJavaScript",null)}},{key:"getOutline",value:function(){return this.messageHandler.sendWithPromise("GetOutline",null)}},{key:"getPermissions",value:function(){return this.messageHandler.sendWithPromise("GetPermissions",null)}},{key:"getMetadata",value:function(){var e=this;return this.messageHandler.sendWithPromise("GetMetadata",null).then(function(t){return{info:t[0],metadata:t[1]?new d.Metadata(t[1]):null,contentDispositionFilename:e._fullReader?e._fullReader.filename:null}})}},{key:"getStats",value:function(){return this.messageHandler.sendWithPromise("GetStats",null)}},{key:"startCleanup",value:function(){var e=this;this.messageHandler.sendWithPromise("Cleanup",null).then(function(){for(var t=0,r=e.pageCache.length;t<r;t++){var n=e.pageCache[t];n&&n.cleanup()}e.commonObjs.clear(),e.fontLoader.clear()})}},{key:"loadingParams",get:function(){var e=this._params;return(0,a.shadow)(this,"loadingParams",{disableAutoFetch:e.disableAutoFetch,disableCreateObjectURL:e.disableCreateObjectURL,disableFontFace:e.disableFontFace,nativeImageDecoderSupport:e.nativeImageDecoderSupport})}}]),e}(),W=function(){function e(){b(this,e),this._objs=Object.create(null)}return S(e,[{key:"_ensureObj",value:function(e){return this._objs[e]?this._objs[e]:this._objs[e]={capability:(0,a.createPromiseCapability)(),data:null,resolved:!1}}},{key:"get",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(t)return this._ensureObj(e).capability.promise.then(t),null;var r=this._objs[e];if(!r||!r.resolved)throw new Error("Requesting object that isn't resolved yet ".concat(e,"."));return r.data}},{key:"has",value:function(e){var t=this._objs[e];return!!t&&t.resolved}},{key:"resolve",value:function(e,t){var r=this._ensureObj(e);r.resolved=!0,r.data=t,r.capability.resolve(t)}},{key:"clear",value:function(){for(var e in this._objs){var t=this._objs[e].data;"undefined"!=typeof Image&&t instanceof Image&&(0,o.releaseImageResources)(t)}this._objs=Object.create(null)}}]),e}(),B=function(){function e(t){b(this,e),this._internalRenderTask=t,this.onContinue=null}return S(e,[{key:"cancel",value:function(){this._internalRenderTask.cancel()}},{key:"then",value:function(e,t){return(0,o.deprecated)("RenderTask.then method, use the `promise` getter instead."),this.promise.then.apply(this.promise,arguments)}},{key:"promise",get:function(){return this._internalRenderTask.capability.promise}}]),e}(),z=(q=new WeakSet,function(){function e(t){var r=t.callback,n=t.params,i=t.objs,o=t.commonObjs,s=t.operatorList,u=t.pageNumber,c=t.canvasFactory,l=t.webGLContext,h=t.useRequestAnimationFrame,f=void 0!==h&&h,d=t.pdfBug,p=void 0!==d&&d;b(this,e),this.callback=r,this.params=n,this.objs=i,this.commonObjs=o,this.operatorListIdx=null,this.operatorList=s,this.pageNumber=u,this.canvasFactory=c,this.webGLContext=l,this._pdfBug=p,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===f&&"undefined"!=typeof window,this.cancelled=!1,this.capability=(0,a.createPromiseCapability)(),this.task=new B(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=n.canvasContext.canvas}return S(e,[{key:"initializeGraphics",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.cancelled){if(this._canvas){if(q.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");q.add(this._canvas)}this._pdfBug&&l.default.StepperManager&&l.default.StepperManager.enabled&&(this.stepper=l.default.StepperManager.create(this.pageNumber-1),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());var t=this.params,r=t.canvasContext,n=t.viewport,i=t.transform,a=t.imageLayer,o=t.background;this.gfx=new c.CanvasGraphics(r,this.commonObjs,this.objs,this.canvasFactory,this.webGLContext,a),this.gfx.beginDrawing({transform:i,viewport:n,transparency:e,background:o}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback&&this.graphicsReadyCallback()}}},{key:"cancel",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.running=!1,this.cancelled=!0,this.gfx&&this.gfx.endDrawing(),this._canvas&&q.delete(this._canvas),this.callback(e||new o.RenderingCancelledException("Rendering cancelled, page ".concat(this.pageNumber),"canvas"))}},{key:"operatorListChanged",value:function(){this.graphicsReady?(this.stepper&&this.stepper.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)}},{key:"_continue",value:function(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}},{key:"_scheduleNext",value:function(){var e=this;this._useRequestAnimationFrame?window.requestAnimationFrame(function(){e._nextBound().catch(e.cancel.bind(e))}):Promise.resolve().then(this._nextBound).catch(this.cancel.bind(this))}},{key:"_next",value:(t=i.default.mark(function e(){return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.cancelled){e.next=2;break}return e.abrupt("return");case 2:this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),this._canvas&&q.delete(this._canvas),this.callback()));case 4:case"end":return e.stop()}},e,this)}),r=function(){var e=this,r=arguments;return new Promise(function(n,i){var a=t.apply(e,r);function o(e){g(a,n,i,o,s,"next",e)}function s(e){g(a,n,i,o,s,"throw",e)}o(void 0)})},function(){return r.apply(this,arguments)})}]),e;var t,r}());t.version="2.2.228";t.build="d7afb74a"},function(e,t,r){"use strict";e.exports=r(149)},function(e,t,r){"use strict";(function(e){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var r=function(e){var r,n=Object.prototype,i=n.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function c(e,t,r,n){var i=t&&t.prototype instanceof m?t:m,a=Object.create(i.prototype),o=new R(n||[]);return a._invoke=function(e,t,r){var n=h;return function(i,a){if(n===d)throw new Error("Generator is already running");if(n===p){if("throw"===i)throw a;return E()}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var s=x(o,r);if(s){if(s===v)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===h)throw n=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=d;var u=l(e,t,r);if("normal"===u.type){if(n=r.done?p:f,u.arg===v)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n=p,r.method="throw",r.arg=u.arg)}}}(e,r,o),a}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var h="suspendedStart",f="suspendedYield",d="executing",p="completed",v={};function m(){}function g(){}function y(){}var _={};_[o]=function(){return this};var b=Object.getPrototypeOf,A=b&&b(b(T([])));A&&A!==n&&i.call(A,o)&&(_=A);var S=y.prototype=m.prototype=Object.create(_);function w(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function k(e){var r;this._invoke=function(n,a){function o(){return new Promise(function(r,o){!function r(n,a,o,s){var u=l(e[n],e,a);if("throw"!==u.type){var c=u.arg,h=c.value;return h&&"object"===t(h)&&i.call(h,"__await")?Promise.resolve(h.__await).then(function(e){r("next",e,o,s)},function(e){r("throw",e,o,s)}):Promise.resolve(h).then(function(e){c.value=e,o(c)},function(e){return r("throw",e,o,s)})}s(u.arg)}(n,a,r,o)})}return r=r?r.then(o,o):o()}}function x(e,t){var n=e.iterator[t.method];if(n===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=r,x(e,t),"throw"===t.method))return v;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return v}var i=l(n,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,v;var a=i.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=r),t.delegate=null,v):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,v)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function R(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function T(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function t(){for(;++n<e.length;)if(i.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=r,t.done=!0,t};return a.next=a}}return{next:E}}function E(){return{value:r,done:!0}}return g.prototype=S.constructor=y,y.constructor=g,y[u]=g.displayName="GeneratorFunction",e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,u in e||(e[u]="GeneratorFunction")),e.prototype=Object.create(S),e},e.awrap=function(e){return{__await:e}},w(k.prototype),k.prototype[s]=function(){return this},e.AsyncIterator=k,e.async=function(t,r,n,i){var a=new k(c(t,r,n,i));return e.isGeneratorFunction(r)?a:a.next().then(function(e){return e.done?e.value:a.next()})},w(S),S[u]="Generator",S[o]=function(){return this},S.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=T,R.prototype={constructor:R,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(P),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=r)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,i){return s.type="throw",s.arg=e,t.next=n,i&&(t.method="next",t.arg=r),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var u=i.call(o,"catchLoc"),c=i.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),P(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;P(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:T(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=r),v}},e}("object"===t(e)?e.exports:{});try{regeneratorRuntime=r}catch(e){Function("r","regeneratorRuntime = r")(r)}}).call(this,r(150)(e))},function(e,t,r){"use strict";e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addLinkAttributes=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.url,n=t.target,i=t.rel;if(e.href=e.title=r?(0,a.removeNullCharacters)(r):"",r){var o=Object.values(y),s=o.includes(n)?n:y.NONE;e.target=_[s],e.rel="string"==typeof i?i:h}},t.getFilenameFromUrl=function(e){var t=e.indexOf("#"),r=e.indexOf("?"),n=Math.min(t>0?t:e.length,r>0?r:e.length);return e.substring(e.lastIndexOf("/",n)+1,n)},t.isFetchSupported=w,t.isValidFetchUrl=k,t.loadScript=function(e){return new Promise(function(t,r){var n=document.createElement("script");n.src=e,n.onload=t,n.onerror=function(){r(new Error("Cannot load script at: ".concat(n.src)))},(document.head||document.documentElement).appendChild(n)})},t.deprecated=function(e){console.log("Deprecated API usage: "+e)},t.releaseImageResources=function(e){(0,a.assert)(e instanceof Image,"Invalid `img` parameter.");var t=e.src;"string"==typeof t&&t.startsWith("blob:")&&a.URL.revokeObjectURL&&a.URL.revokeObjectURL(t);e.removeAttribute("src")},t.PDFDateString=t.DummyStatTimer=t.StatTimer=t.DOMSVGFactory=t.DOMCMapReaderFactory=t.DOMCanvasFactory=t.DEFAULT_LINK_REL=t.LinkTarget=t.RenderingCancelledException=t.PageViewport=void 0;var n,i=(n=r(148))&&n.__esModule?n:{default:n},a=r(1);function o(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function s(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var a=e.apply(t,r);function s(e){o(a,n,i,s,u,"next",e)}function u(e){o(a,n,i,s,u,"throw",e)}s(void 0)})}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function l(e,t,r){return t&&c(e.prototype,t),r&&c(e,r),e}var h="noopener noreferrer nofollow";t.DEFAULT_LINK_REL=h;var f="http://www.w3.org/2000/svg",d=function(){function e(){u(this,e)}return l(e,[{key:"create",value:function(e,t){if(e<=0||t<=0)throw new Error("Invalid canvas size");var r=document.createElement("canvas"),n=r.getContext("2d");return r.width=e,r.height=t,{canvas:r,context:n}}},{key:"reset",value:function(e,t,r){if(!e.canvas)throw new Error("Canvas is not specified");if(t<=0||r<=0)throw new Error("Invalid canvas size");e.canvas.width=t,e.canvas.height=r}},{key:"destroy",value:function(e){if(!e.canvas)throw new Error("Canvas is not specified");e.canvas.width=0,e.canvas.height=0,e.canvas=null,e.context=null}}]),e}();t.DOMCanvasFactory=d;var p=function(){function e(t){var r=t.baseUrl,n=void 0===r?null:r,i=t.isCompressed,a=void 0!==i&&i;u(this,e),this.baseUrl=n,this.isCompressed=a}return l(e,[{key:"fetch",value:function(e){function t(t){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(){var e=s(i.default.mark(function e(t){var r,n,o,u=this;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.name,this.baseUrl){e.next=3;break}throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');case 3:if(r){e.next=5;break}throw new Error("CMap name must be specified.");case 5:if(n=this.baseUrl+r+(this.isCompressed?".bcmap":""),o=this.isCompressed?a.CMapCompressionType.BINARY:a.CMapCompressionType.NONE,!w()||!k(n,document.baseURI)){e.next=9;break}return e.abrupt("return",fetch(n).then(function(){var e=s(i.default.mark(function e(t){var r;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.ok){e.next=2;break}throw new Error(t.statusText);case 2:if(!u.isCompressed){e.next=10;break}return e.t0=Uint8Array,e.next=6,t.arrayBuffer();case 6:e.t1=e.sent,r=new e.t0(e.t1),e.next=15;break;case 10:return e.t2=a.stringToBytes,e.next=13,t.text();case 13:e.t3=e.sent,r=(0,e.t2)(e.t3);case 15:return e.abrupt("return",{cMapData:r,compressionType:o});case 16:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}()).catch(function(e){throw new Error("Unable to load ".concat(u.isCompressed?"binary ":"")+"CMap at: ".concat(n))}));case 9:return e.abrupt("return",new Promise(function(e,t){var r=new XMLHttpRequest;r.open("GET",n,!0),u.isCompressed&&(r.responseType="arraybuffer"),r.onreadystatechange=function(){if(r.readyState===XMLHttpRequest.DONE){var n;if(200===r.status||0===r.status)if(u.isCompressed&&r.response?n=new Uint8Array(r.response):!u.isCompressed&&r.responseText&&(n=(0,a.stringToBytes)(r.responseText)),n)return void e({cMapData:n,compressionType:o});t(new Error(r.statusText))}},r.send(null)}).catch(function(e){throw new Error("Unable to load ".concat(u.isCompressed?"binary ":"")+"CMap at: ".concat(n))}));case 10:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}())}]),e}();t.DOMCMapReaderFactory=p;var v=function(){function e(){u(this,e)}return l(e,[{key:"create",value:function(e,t){(0,a.assert)(e>0&&t>0,"Invalid SVG dimensions");var r=document.createElementNS(f,"svg:svg");return r.setAttribute("version","1.1"),r.setAttribute("width",e+"px"),r.setAttribute("height",t+"px"),r.setAttribute("preserveAspectRatio","none"),r.setAttribute("viewBox","0 0 "+e+" "+t),r}},{key:"createElement",value:function(e){return(0,a.assert)("string"==typeof e,"Invalid SVG element type"),document.createElementNS(f,e)}}]),e}();t.DOMSVGFactory=v;var m=function(){function e(t){var r=t.viewBox,n=t.scale,i=t.rotation,a=t.offsetX,o=void 0===a?0:a,s=t.offsetY,c=void 0===s?0:s,l=t.dontFlip,h=void 0!==l&&l;u(this,e),this.viewBox=r,this.scale=n,this.rotation=i,this.offsetX=o,this.offsetY=c;var f,d,p,v,m,g,y,_,b=(r[2]+r[0])/2,A=(r[3]+r[1])/2;switch(i=(i%=360)<0?i+360:i){case 180:f=-1,d=0,p=0,v=1;break;case 90:f=0,d=1,p=1,v=0;break;case 270:f=0,d=-1,p=-1,v=0;break;default:f=1,d=0,p=0,v=-1}h&&(p=-p,v=-v),0===f?(m=Math.abs(A-r[1])*n+o,g=Math.abs(b-r[0])*n+c,y=Math.abs(r[3]-r[1])*n,_=Math.abs(r[2]-r[0])*n):(m=Math.abs(b-r[0])*n+o,g=Math.abs(A-r[1])*n+c,y=Math.abs(r[2]-r[0])*n,_=Math.abs(r[3]-r[1])*n),this.transform=[f*n,d*n,p*n,v*n,m-f*n*b-p*n*A,g-d*n*b-v*n*A],this.width=y,this.height=_}return l(e,[{key:"clone",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.scale,n=void 0===r?this.scale:r,i=t.rotation,a=void 0===i?this.rotation:i,o=t.dontFlip,s=void 0!==o&&o;return new e({viewBox:this.viewBox.slice(),scale:n,rotation:a,offsetX:this.offsetX,offsetY:this.offsetY,dontFlip:s})}},{key:"convertToViewportPoint",value:function(e,t){return a.Util.applyTransform([e,t],this.transform)}},{key:"convertToViewportRectangle",value:function(e){var t=a.Util.applyTransform([e[0],e[1]],this.transform),r=a.Util.applyTransform([e[2],e[3]],this.transform);return[t[0],t[1],r[0],r[1]]}},{key:"convertToPdfPoint",value:function(e,t){return a.Util.applyInverseTransform([e,t],this.transform)}}]),e}();t.PageViewport=m;var g=function(){function e(e,t){this.message=e,this.type=t}return e.prototype=new Error,e.prototype.name="RenderingCancelledException",e.constructor=e,e}();t.RenderingCancelledException=g;var y={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};t.LinkTarget=y;var _=["","_self","_blank","_parent","_top"];var b=function(){function e(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];u(this,e),this.enabled=!!t,this.started=Object.create(null),this.times=[]}return l(e,[{key:"time",value:function(e){this.enabled&&(e in this.started&&(0,a.warn)("Timer is already running for "+e),this.started[e]=Date.now())}},{key:"timeEnd",value:function(e){this.enabled&&(e in this.started||(0,a.warn)("Timer has not been started for "+e),this.times.push({name:e,start:this.started[e],end:Date.now()}),delete this.started[e])}},{key:"toString",value:function(){var e="",t=0,r=!0,n=!1,i=void 0;try{for(var a,o=this.times[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var s=a.value.name;s.length>t&&(t=s.length)}}catch(e){n=!0,i=e}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}var u=!0,c=!1,l=void 0;try{for(var h,f=this.times[Symbol.iterator]();!(u=(h=f.next()).done);u=!0){var d=h.value,p=d.end-d.start;e+="".concat(d.name.padEnd(t)," ").concat(p,"ms\n")}}catch(e){c=!0,l=e}finally{try{u||null==f.return||f.return()}finally{if(c)throw l}}return e}}]),e}();t.StatTimer=b;var A,S=function(){function e(){u(this,e),(0,a.unreachable)("Cannot initialize DummyStatTimer.")}return l(e,null,[{key:"time",value:function(e){}},{key:"timeEnd",value:function(e){}},{key:"toString",value:function(){return""}}]),e}();function w(){return"undefined"!=typeof fetch&&"undefined"!=typeof Response&&"body"in Response.prototype&&"undefined"!=typeof ReadableStream}function k(e,t){try{var r=(t?new a.URL(e,t):new a.URL(e)).protocol;return"http:"===r||"https:"===r}catch(e){return!1}}t.DummyStatTimer=S;var x=function(){function e(){u(this,e)}return l(e,null,[{key:"toDateObject",value:function(e){if(!e||!(0,a.isString)(e))return null;A||(A=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));var t=A.exec(e);if(!t)return null;var r=parseInt(t[1],10),n=parseInt(t[2],10);n=n>=1&&n<=12?n-1:0;var i=parseInt(t[3],10);i=i>=1&&i<=31?i:1;var o=parseInt(t[4],10);o=o>=0&&o<=23?o:0;var s=parseInt(t[5],10);s=s>=0&&s<=59?s:0;var u=parseInt(t[6],10);u=u>=0&&u<=59?u:0;var c=t[7]||"Z",l=parseInt(t[8],10);l=l>=0&&l<=23?l:0;var h=parseInt(t[9],10)||0;return h=h>=0&&h<=59?h:0,"-"===c?(o+=l,s+=h):"+"===c&&(o-=l,s-=h),new Date(Date.UTC(r,n,i,o,s,u))}}]),e}();t.PDFDateString=x},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FontLoader=t.FontFaceObject=void 0;var n,i=(n=r(148))&&n.__esModule?n:{default:n},a=r(1);function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t){return!t||"object"!==o(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function u(e){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function c(e,t){return(c=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function l(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function d(e,t,r){return t&&f(e.prototype,t),r&&f(e,r),e}var p,v=function(){function e(t){var r=t.docId,n=t.onUnsupportedFeature;h(this,e),this.constructor===e&&(0,a.unreachable)("Cannot initialize BaseFontLoader."),this.docId=r,this._onUnsupportedFeature=n,this.nativeFontFaces=[],this.styleElement=null}return d(e,[{key:"addNativeFontFace",value:function(e){this.nativeFontFaces.push(e),document.fonts.add(e)}},{key:"insertRule",value:function(e){var t=this.styleElement;t||((t=this.styleElement=document.createElement("style")).id="PDFJS_FONT_STYLE_TAG_".concat(this.docId),document.documentElement.getElementsByTagName("head")[0].appendChild(t));var r=t.sheet;r.insertRule(e,r.cssRules.length)}},{key:"clear",value:function(){this.nativeFontFaces.forEach(function(e){document.fonts.delete(e)}),this.nativeFontFaces.length=0,this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}},{key:"bind",value:function(){var e,t=(e=i.default.mark(function e(t){var r,n,o=this;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.attached&&!t.missingFile){e.next=2;break}return e.abrupt("return",void 0);case 2:if(t.attached=!0,!this.isFontLoadingAPISupported){e.next=19;break}if(!(r=t.createNativeFontFace())){e.next=18;break}return this.addNativeFontFace(r),e.prev=7,e.next=10,r.loaded;case 10:e.next=18;break;case 12:throw e.prev=12,e.t0=e.catch(7),this._onUnsupportedFeature({featureId:a.UNSUPPORTED_FEATURES.font}),(0,a.warn)("Failed to load font '".concat(r.family,"': '").concat(e.t0,"'.")),t.disableFontFace=!0,e.t0;case 18:return e.abrupt("return",void 0);case 19:if(!(n=t.createFontFaceRule())){e.next=25;break}if(this.insertRule(n),!this.isSyncFontLoadingSupported){e.next=24;break}return e.abrupt("return",void 0);case 24:return e.abrupt("return",new Promise(function(e){var r=o._queueLoadingCallback(e);o._prepareFontLoadEvent([n],[t],r)}));case 25:return e.abrupt("return",void 0);case 26:case"end":return e.stop()}},e,this,[[7,12]])}),function(){var t=this,r=arguments;return new Promise(function(n,i){var a=e.apply(t,r);function o(e){l(a,n,i,o,s,"next",e)}function s(e){l(a,n,i,o,s,"throw",e)}o(void 0)})});return function(e){return t.apply(this,arguments)}}()},{key:"_queueLoadingCallback",value:function(e){(0,a.unreachable)("Abstract method `_queueLoadingCallback`.")}},{key:"_prepareFontLoadEvent",value:function(e,t,r){(0,a.unreachable)("Abstract method `_prepareFontLoadEvent`.")}},{key:"isFontLoadingAPISupported",get:function(){(0,a.unreachable)("Abstract method `isFontLoadingAPISupported`.")}},{key:"isSyncFontLoadingSupported",get:function(){(0,a.unreachable)("Abstract method `isSyncFontLoadingSupported`.")}},{key:"_loadTestFont",get:function(){(0,a.unreachable)("Abstract method `_loadTestFont`.")}}]),e}();t.FontLoader=p,t.FontLoader=p=function(e){function t(e){var r;return h(this,t),(r=s(this,u(t).call(this,e))).loadingContext={requests:[],nextRequestId:0},r.loadTestFontId=0,r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&c(e,t)}(t,v),d(t,[{key:"_queueLoadingCallback",value:function(e){var t=this.loadingContext,r={id:"pdfjs-font-loading-".concat(t.nextRequestId++),done:!1,complete:function(){for((0,a.assert)(!r.done,"completeRequest() cannot be called twice."),r.done=!0;t.requests.length>0&&t.requests[0].done;){var e=t.requests.shift();setTimeout(e.callback,0)}},callback:e};return t.requests.push(r),r}},{key:"_prepareFontLoadEvent",value:function(e,t,r){function n(e,t){return e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|255&e.charCodeAt(t+3)}function i(e,t,r,n){return e.substring(0,t)+n+e.substring(t+r)}var o,s,u=document.createElement("canvas");u.width=1,u.height=1;var c=u.getContext("2d"),l=0;var h="lt".concat(Date.now()).concat(this.loadTestFontId++),f=this._loadTestFont,d=n(f=i(f,976,h.length,h),16);for(o=0,s=h.length-3;o<s;o+=4)d=d-1482184792+n(h,o)|0;o<h.length&&(d=d-1482184792+n(h+"XXX",o)|0),f=i(f,16,4,(0,a.string32)(d));var p="url(data:font/opentype;base64,".concat(btoa(f),");"),v='@font-face {font-family:"'.concat(h,'";src:').concat(p,"}");this.insertRule(v);var m=[];for(o=0,s=t.length;o<s;o++)m.push(t[o].loadedName);m.push(h);var g=document.createElement("div");for(g.setAttribute("style","visibility: hidden;width: 10px; height: 10px;position: absolute; top: 0px; left: 0px;"),o=0,s=m.length;o<s;++o){var y=document.createElement("span");y.textContent="Hi",y.style.fontFamily=m[o],g.appendChild(y)}document.body.appendChild(g),function e(t,r){if(++l>30)return(0,a.warn)("Load test font never loaded."),void r();c.font="30px "+t,c.fillText(".",0,20),c.getImageData(0,0,1,1).data[3]>0?r():setTimeout(e.bind(null,t,r))}(h,function(){document.body.removeChild(g),r.complete()})}},{key:"isFontLoadingAPISupported",get:function(){var e="undefined"!=typeof document&&!!document.fonts;if(e&&"undefined"!=typeof navigator){var t=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);t&&t[1]<63&&(e=!1)}return(0,a.shadow)(this,"isFontLoadingAPISupported",e)}},{key:"isSyncFontLoadingSupported",get:function(){var e=!1;if("undefined"==typeof navigator)e=!0;else{var t=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);t&&t[1]>=14&&(e=!0)}return(0,a.shadow)(this,"isSyncFontLoadingSupported",e)}},{key:"_loadTestFont",get:function(){return(0,a.shadow)(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}}]),t}();var m={get value(){return(0,a.shadow)(this,"value",(0,a.isEvalSupported)())}},g=function(){function e(t,r){var n=r.isEvalSupported,i=void 0===n||n,a=r.disableFontFace,o=void 0!==a&&a,s=r.ignoreErrors,u=void 0!==s&&s,c=r.onUnsupportedFeature,l=void 0===c?null:c,f=r.fontRegistry,d=void 0===f?null:f;for(var p in h(this,e),this.compiledGlyphs=Object.create(null),t)this[p]=t[p];this.isEvalSupported=!1!==i,this.disableFontFace=!0===o,this.ignoreErrors=!0===u,this._onUnsupportedFeature=l,this.fontRegistry=d}return d(e,[{key:"createNativeFontFace",value:function(){if(!this.data||this.disableFontFace)return null;var e=new FontFace(this.loadedName,this.data,{});return this.fontRegistry&&this.fontRegistry.registerFont(this),e}},{key:"createFontFaceRule",value:function(){if(!this.data||this.disableFontFace)return null;var e=(0,a.bytesToString)(new Uint8Array(this.data)),t="url(data:".concat(this.mimetype,";base64,").concat(btoa(e),");"),r='@font-face {font-family:"'.concat(this.loadedName,'";src:').concat(t,"}");return this.fontRegistry&&this.fontRegistry.registerFont(this,t),r}},{key:"getPathGenerator",value:function(e,t){if(void 0!==this.compiledGlyphs[t])return this.compiledGlyphs[t];var r,n;try{r=e.get(this.loadedName+"_path_"+t)}catch(e){if(!this.ignoreErrors)throw e;return this._onUnsupportedFeature&&this._onUnsupportedFeature({featureId:a.UNSUPPORTED_FEATURES.font}),(0,a.warn)('getPathGenerator - ignoring character: "'.concat(e,'".')),this.compiledGlyphs[t]=function(e,t){}}if(this.isEvalSupported&&m.value){for(var i,o="",s=0,u=r.length;s<u;s++)i=void 0!==(n=r[s]).args?n.args.join(","):"",o+="c."+n.cmd+"("+i+");\n";return this.compiledGlyphs[t]=new Function("c","size",o)}return this.compiledGlyphs[t]=function(e,t){for(var i=0,a=r.length;i<a;i++)"scale"===(n=r[i]).cmd&&(n.args=[t,-t]),e[n.cmd].apply(e,n.args)}}}]),e}();t.FontFaceObject=g},function(e,t,r){"use strict";var n=Object.create(null),i=r(4),a="undefined"!=typeof navigator&&navigator.userAgent||"",o=/Trident/.test(a),s=/CriOS/.test(a);(o||s)&&(n.disableCreateObjectURL=!0),i()&&(n.disableFontFace=!0,n.nativeImageDecoderSupport="none"),t.apiCompatibilityParams=Object.freeze(n)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CanvasGraphics=void 0;var n=r(1),i=r(155),a=16,o={get value(){return(0,n.shadow)(o,"value",(0,n.isLittleEndian)())}};function s(e){e.mozCurrentTransform||(e._originalSave=e.save,e._originalRestore=e.restore,e._originalRotate=e.rotate,e._originalScale=e.scale,e._originalTranslate=e.translate,e._originalTransform=e.transform,e._originalSetTransform=e.setTransform,e._transformMatrix=e._transformMatrix||[1,0,0,1,0,0],e._transformStack=[],Object.defineProperty(e,"mozCurrentTransform",{get:function(){return this._transformMatrix}}),Object.defineProperty(e,"mozCurrentTransformInverse",{get:function(){var e=this._transformMatrix,t=e[0],r=e[1],n=e[2],i=e[3],a=e[4],o=e[5],s=t*i-r*n,u=r*n-t*i;return[i/s,r/u,n/u,t/s,(i*a-n*o)/u,(r*a-t*o)/s]}}),e.save=function(){var e=this._transformMatrix;this._transformStack.push(e),this._transformMatrix=e.slice(0,6),this._originalSave()},e.restore=function(){var e=this._transformStack.pop();e&&(this._transformMatrix=e,this._originalRestore())},e.translate=function(e,t){var r=this._transformMatrix;r[4]=r[0]*e+r[2]*t+r[4],r[5]=r[1]*e+r[3]*t+r[5],this._originalTranslate(e,t)},e.scale=function(e,t){var r=this._transformMatrix;r[0]=r[0]*e,r[1]=r[1]*e,r[2]=r[2]*t,r[3]=r[3]*t,this._originalScale(e,t)},e.transform=function(t,r,n,i,a,o){var s=this._transformMatrix;this._transformMatrix=[s[0]*t+s[2]*r,s[1]*t+s[3]*r,s[0]*n+s[2]*i,s[1]*n+s[3]*i,s[0]*a+s[2]*o+s[4],s[1]*a+s[3]*o+s[5]],e._originalTransform(t,r,n,i,a,o)},e.setTransform=function(t,r,n,i,a,o){this._transformMatrix=[t,r,n,i,a,o],e._originalSetTransform(t,r,n,i,a,o)},e.rotate=function(e){var t=Math.cos(e),r=Math.sin(e),n=this._transformMatrix;this._transformMatrix=[n[0]*t+n[2]*r,n[1]*t+n[3]*r,n[0]*-r+n[2]*t,n[1]*-r+n[3]*t,n[4],n[5]],this._originalRotate(e)})}var u=function(){function e(e){this.canvasFactory=e,this.cache=Object.create(null)}return e.prototype={getCanvas:function(e,t,r,n){var i;return void 0!==this.cache[e]?(i=this.cache[e],this.canvasFactory.reset(i,t,r),i.context.setTransform(1,0,0,1,0,0)):(i=this.canvasFactory.create(t,r),this.cache[e]=i),n&&s(i.context),i},clear:function(){for(var e in this.cache){var t=this.cache[e];this.canvasFactory.destroy(t),delete this.cache[e]}}},e}();var c=function(){function e(){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=n.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=n.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=n.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.resumeSMaskCtx=null}return e.prototype={clone:function(){return Object.create(this)},setCurrentPoint:function(e,t){this.x=e,this.y=t}},e}(),l=function(){function e(e,t,r,n,i,a){this.ctx=e,this.current=new c,this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=t,this.objs=r,this.canvasFactory=n,this.webGLContext=i,this.imageLayer=a,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.cachedCanvases=new u(this.canvasFactory),e&&s(e),this._cachedGetSinglePixelWidth=null}function t(e,t){if("undefined"!=typeof ImageData&&t instanceof ImageData)e.putImageData(t,0,0);else{var r,i,s,u,c,l=t.height,h=t.width,f=l%a,d=(l-f)/a,p=0===f?d:d+1,v=e.createImageData(h,a),m=0,g=t.data,y=v.data;if(t.kind===n.ImageKind.GRAYSCALE_1BPP){var _=g.byteLength,b=new Uint32Array(y.buffer,0,y.byteLength>>2),A=b.length,S=h+7>>3,w=4294967295,k=o.value?4278190080:255;for(i=0;i<p;i++){for(u=i<d?a:f,r=0,s=0;s<u;s++){for(var x=_-m,C=0,P=x>S?h:8*x-7,R=-8&P,T=0,E=0;C<R;C+=8)E=g[m++],b[r++]=128&E?w:k,b[r++]=64&E?w:k,b[r++]=32&E?w:k,b[r++]=16&E?w:k,b[r++]=8&E?w:k,b[r++]=4&E?w:k,b[r++]=2&E?w:k,b[r++]=1&E?w:k;for(;C<P;C++)0===T&&(E=g[m++],T=128),b[r++]=E&T?w:k,T>>=1}for(;r<A;)b[r++]=0;e.putImageData(v,0,i*a)}}else if(t.kind===n.ImageKind.RGBA_32BPP){for(s=0,c=h*a*4,i=0;i<d;i++)y.set(g.subarray(m,m+c)),m+=c,e.putImageData(v,0,s),s+=a;i<p&&(c=h*f*4,y.set(g.subarray(m,m+c)),e.putImageData(v,0,s))}else{if(t.kind!==n.ImageKind.RGB_24BPP)throw new Error("bad image kind: ".concat(t.kind));for(c=h*(u=a),i=0;i<p;i++){for(i>=d&&(c=h*(u=f)),r=0,s=c;s--;)y[r++]=g[m++],y[r++]=g[m++],y[r++]=g[m++],y[r++]=255;e.putImageData(v,0,i*a)}}}}function r(e,t){for(var r=t.height,n=t.width,i=r%a,o=(r-i)/a,s=0===i?o:o+1,u=e.createImageData(n,a),c=0,l=t.data,h=u.data,f=0;f<s;f++){for(var d=f<o?a:i,p=3,v=0;v<d;v++)for(var m=0,g=0;g<n;g++){if(!m){var y=l[c++];m=128}h[p]=y&m?0:255,p+=4,m>>=1}e.putImageData(u,0,f*a)}}function l(e,t){for(var r=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"],n=0,i=r.length;n<i;n++){var a=r[n];void 0!==e[a]&&(t[a]=e[a])}void 0!==e.setLineDash&&(t.setLineDash(e.getLineDash()),t.lineDashOffset=e.lineDashOffset)}function h(e){e.strokeStyle="#000000",e.fillStyle="#000000",e.fillRule="nonzero",e.globalAlpha=1,e.lineWidth=1,e.lineCap="butt",e.lineJoin="miter",e.miterLimit=10,e.globalCompositeOperation="source-over",e.font="10px sans-serif",void 0!==e.setLineDash&&(e.setLineDash([]),e.lineDashOffset=0)}function f(e,t,r,n){for(var i=e.length,a=3;a<i;a+=4){var o=e[a];if(0===o)e[a-3]=t,e[a-2]=r,e[a-1]=n;else if(o<255){var s=255-o;e[a-3]=e[a-3]*o+t*s>>8,e[a-2]=e[a-2]*o+r*s>>8,e[a-1]=e[a-1]*o+n*s>>8}}}function d(e,t,r){for(var n=e.length,i=3;i<n;i+=4){var a=r?r[e[i]]:e[i];t[i]=t[i]*a*(1/255)|0}}function p(e,t,r){for(var n=e.length,i=3;i<n;i+=4){var a=77*e[i-3]+152*e[i-2]+28*e[i-1];t[i]=r?t[i]*r[a>>8]>>8:t[i]*a>>16}}function v(e,t,r,n){var i=t.canvas,a=t.context;e.setTransform(t.scaleX,0,0,t.scaleY,t.offsetX,t.offsetY);var o=t.backdrop||null;if(!t.transferMap&&n.isEnabled){var s=n.composeSMask({layer:r.canvas,mask:i,properties:{subtype:t.subtype,backdrop:o}});return e.setTransform(1,0,0,1,0,0),void e.drawImage(s,t.offsetX,t.offsetY)}!function(e,t,r,n,i,a,o){var s,u=!!a,c=u?a[0]:0,l=u?a[1]:0,h=u?a[2]:0;s="Luminosity"===i?p:d;for(var v=Math.min(n,Math.ceil(1048576/r)),m=0;m<n;m+=v){var g=Math.min(v,n-m),y=e.getImageData(0,m,r,g),_=t.getImageData(0,m,r,g);u&&f(y.data,c,l,h),s(y.data,_.data,o),e.putImageData(_,0,m)}}(a,r,i.width,i.height,t.subtype,o,t.transferMap),e.drawImage(i,0,0)}var m=["butt","round","square"],g=["miter","round","bevel"],y={},_={};for(var b in e.prototype={beginDrawing:function(e){var t=e.transform,r=e.viewport,n=e.transparency,i=void 0!==n&&n,a=e.background,o=void 0===a?null:a,s=this.ctx.canvas.width,u=this.ctx.canvas.height;if(this.ctx.save(),this.ctx.fillStyle=o||"rgb(255, 255, 255)",this.ctx.fillRect(0,0,s,u),this.ctx.restore(),i){var c=this.cachedCanvases.getCanvas("transparent",s,u,!0);this.compositeCtx=this.ctx,this.transparentCanvas=c.canvas,this.ctx=c.context,this.ctx.save(),this.ctx.transform.apply(this.ctx,this.compositeCtx.mozCurrentTransform)}this.ctx.save(),h(this.ctx),t&&this.ctx.transform.apply(this.ctx,t),this.ctx.transform.apply(this.ctx,r.transform),this.baseTransform=this.ctx.mozCurrentTransform.slice(),this.imageLayer&&this.imageLayer.beginLayout()},executeOperatorList:function(e,t,r,i){var a=e.argsArray,o=e.fnArray,s=t||0,u=a.length;if(u===s)return s;for(var c,l=u-s>10&&"function"==typeof r,h=l?Date.now()+15:0,f=0,d=this.commonObjs,p=this.objs;;){if(void 0!==i&&s===i.nextBreakPoint)return i.breakIt(s,r),s;if((c=o[s])!==n.OPS.dependency)this[c].apply(this,a[s]);else{var v=!0,m=!1,g=void 0;try{for(var y,_=a[s][Symbol.iterator]();!(v=(y=_.next()).done);v=!0){var b=y.value,A=b.startsWith("g_")?d:p;if(!A.has(b))return A.get(b,r),s}}catch(e){m=!0,g=e}finally{try{v||null==_.return||_.return()}finally{if(m)throw g}}}if(++s===u)return s;if(l&&++f>10){if(Date.now()>h)return r(),s;f=0}}},endDrawing:function(){null!==this.current.activeSMask&&this.endSMaskGroup(),this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null),this.cachedCanvases.clear(),this.webGLContext.clear(),this.imageLayer&&this.imageLayer.endLayout()},setLineWidth:function(e){this.current.lineWidth=e,this.ctx.lineWidth=e},setLineCap:function(e){this.ctx.lineCap=m[e]},setLineJoin:function(e){this.ctx.lineJoin=g[e]},setMiterLimit:function(e){this.ctx.miterLimit=e},setDash:function(e,t){var r=this.ctx;void 0!==r.setLineDash&&(r.setLineDash(e),r.lineDashOffset=t)},setRenderingIntent:function(e){},setFlatness:function(e){},setGState:function(e){for(var t=0,r=e.length;t<r;t++){var n=e[t],i=n[0],a=n[1];switch(i){case"LW":this.setLineWidth(a);break;case"LC":this.setLineCap(a);break;case"LJ":this.setLineJoin(a);break;case"ML":this.setMiterLimit(a);break;case"D":this.setDash(a[0],a[1]);break;case"RI":this.setRenderingIntent(a);break;case"FL":this.setFlatness(a);break;case"Font":this.setFont(a[0],a[1]);break;case"CA":this.current.strokeAlpha=n[1];break;case"ca":this.current.fillAlpha=n[1],this.ctx.globalAlpha=n[1];break;case"BM":this.ctx.globalCompositeOperation=a;break;case"SMask":this.current.activeSMask&&(this.stateStack.length>0&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask?this.suspendSMaskGroup():this.endSMaskGroup()),this.current.activeSMask=a?this.tempSMask:null,this.current.activeSMask&&this.beginSMaskGroup(),this.tempSMask=null}}},beginSMaskGroup:function(){var e=this.current.activeSMask,t=e.canvas.width,r=e.canvas.height,n="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(n,t,r,!0),a=this.ctx,o=a.mozCurrentTransform;this.ctx.save();var s=i.context;s.scale(1/e.scaleX,1/e.scaleY),s.translate(-e.offsetX,-e.offsetY),s.transform.apply(s,o),e.startTransformInverse=s.mozCurrentTransformInverse,l(a,s),this.ctx=s,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(a),this.groupLevel++},suspendSMaskGroup:function(){var e=this.ctx;this.groupLevel--,this.ctx=this.groupStack.pop(),v(this.ctx,this.current.activeSMask,e,this.webGLContext),this.ctx.restore(),this.ctx.save(),l(e,this.ctx),this.current.resumeSMaskCtx=e;var t=n.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t),e.save(),e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,e.canvas.width,e.canvas.height),e.restore()},resumeSMaskGroup:function(){var e=this.current.resumeSMaskCtx,t=this.ctx;this.ctx=e,this.groupStack.push(t),this.groupLevel++},endSMaskGroup:function(){var e=this.ctx;this.groupLevel--,this.ctx=this.groupStack.pop(),v(this.ctx,this.current.activeSMask,e,this.webGLContext),this.ctx.restore(),l(e,this.ctx);var t=n.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t)},save:function(){this.ctx.save();var e=this.current;this.stateStack.push(e),this.current=e.clone(),this.current.resumeSMaskCtx=null},restore:function(){this.current.resumeSMaskCtx&&this.resumeSMaskGroup(),null===this.current.activeSMask||0!==this.stateStack.length&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask||this.endSMaskGroup(),0!==this.stateStack.length&&(this.current=this.stateStack.pop(),this.ctx.restore(),this.pendingClip=null,this._cachedGetSinglePixelWidth=null)},transform:function(e,t,r,n,i,a){this.ctx.transform(e,t,r,n,i,a),this._cachedGetSinglePixelWidth=null},constructPath:function(e,t){for(var r=this.ctx,i=this.current,a=i.x,o=i.y,s=0,u=0,c=e.length;s<c;s++)switch(0|e[s]){case n.OPS.rectangle:a=t[u++],o=t[u++];var l=t[u++],h=t[u++];0===l&&(l=this.getSinglePixelWidth()),0===h&&(h=this.getSinglePixelWidth());var f=a+l,d=o+h;this.ctx.moveTo(a,o),this.ctx.lineTo(f,o),this.ctx.lineTo(f,d),this.ctx.lineTo(a,d),this.ctx.lineTo(a,o),this.ctx.closePath();break;case n.OPS.moveTo:a=t[u++],o=t[u++],r.moveTo(a,o);break;case n.OPS.lineTo:a=t[u++],o=t[u++],r.lineTo(a,o);break;case n.OPS.curveTo:a=t[u+4],o=t[u+5],r.bezierCurveTo(t[u],t[u+1],t[u+2],t[u+3],a,o),u+=6;break;case n.OPS.curveTo2:r.bezierCurveTo(a,o,t[u],t[u+1],t[u+2],t[u+3]),a=t[u+2],o=t[u+3],u+=4;break;case n.OPS.curveTo3:a=t[u+2],o=t[u+3],r.bezierCurveTo(t[u],t[u+1],a,o,a,o),u+=4;break;case n.OPS.closePath:r.closePath()}i.setCurrentPoint(a,o)},closePath:function(){this.ctx.closePath()},stroke:function(e){e=void 0===e||e;var t=this.ctx,r=this.current.strokeColor;t.lineWidth=Math.max(.65*this.getSinglePixelWidth(),this.current.lineWidth),t.globalAlpha=this.current.strokeAlpha,r&&r.hasOwnProperty("type")&&"Pattern"===r.type?(t.save(),t.strokeStyle=r.getPattern(t,this),t.stroke(),t.restore()):t.stroke(),e&&this.consumePath(),t.globalAlpha=this.current.fillAlpha},closeStroke:function(){this.closePath(),this.stroke()},fill:function(e){e=void 0===e||e;var t=this.ctx,r=this.current.fillColor,n=!1;this.current.patternFill&&(t.save(),this.baseTransform&&t.setTransform.apply(t,this.baseTransform),t.fillStyle=r.getPattern(t,this),n=!0),this.pendingEOFill?(t.fill("evenodd"),this.pendingEOFill=!1):t.fill(),n&&t.restore(),e&&this.consumePath()},eoFill:function(){this.pendingEOFill=!0,this.fill()},fillStroke:function(){this.fill(!1),this.stroke(!1),this.consumePath()},eoFillStroke:function(){this.pendingEOFill=!0,this.fillStroke()},closeFillStroke:function(){this.closePath(),this.fillStroke()},closeEOFillStroke:function(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()},endPath:function(){this.consumePath()},clip:function(){this.pendingClip=y},eoClip:function(){this.pendingClip=_},beginText:function(){this.current.textMatrix=n.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0},endText:function(){var e=this.pendingTextPaths,t=this.ctx;if(void 0!==e){t.save(),t.beginPath();for(var r=0;r<e.length;r++){var n=e[r];t.setTransform.apply(t,n.transform),t.translate(n.x,n.y),n.addToPath(t,n.fontSize)}t.restore(),t.clip(),t.beginPath(),delete this.pendingTextPaths}else t.beginPath()},setCharSpacing:function(e){this.current.charSpacing=e},setWordSpacing:function(e){this.current.wordSpacing=e},setHScale:function(e){this.current.textHScale=e/100},setLeading:function(e){this.current.leading=-e},setFont:function(e,t){var r=this.commonObjs.get(e),i=this.current;if(!r)throw new Error("Can't find font for ".concat(e));if(i.fontMatrix=r.fontMatrix?r.fontMatrix:n.FONT_IDENTITY_MATRIX,0!==i.fontMatrix[0]&&0!==i.fontMatrix[3]||(0,n.warn)("Invalid font matrix for font "+e),t<0?(t=-t,i.fontDirection=-1):i.fontDirection=1,this.current.font=r,this.current.fontSize=t,!r.isType3Font){var a=r.loadedName||"sans-serif",o=r.black?"900":r.bold?"bold":"normal",s=r.italic?"italic":"normal",u='"'.concat(a,'", ').concat(r.fallbackName),c=t<16?16:t>100?100:t;this.current.fontSizeScale=t/c,this.ctx.font="".concat(s," ").concat(o," ").concat(c,"px ").concat(u)}},setTextRenderingMode:function(e){this.current.textRenderingMode=e},setTextRise:function(e){this.current.textRise=e},moveText:function(e,t){this.current.x=this.current.lineX+=e,this.current.y=this.current.lineY+=t},setLeadingMoveText:function(e,t){this.setLeading(-t),this.moveText(e,t)},setTextMatrix:function(e,t,r,n,i,a){this.current.textMatrix=[e,t,r,n,i,a],this.current.textMatrixScale=Math.sqrt(e*e+t*t),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0},nextLine:function(){this.moveText(0,this.current.leading)},paintChar:function(e,t,r,i){var a,o=this.ctx,s=this.current,u=s.font,c=s.textRenderingMode,l=s.fontSize/s.fontSizeScale,h=c&n.TextRenderingMode.FILL_STROKE_MASK,f=!!(c&n.TextRenderingMode.ADD_TO_PATH_FLAG),d=s.patternFill&&u.data;((u.disableFontFace||f||d)&&(a=u.getPathGenerator(this.commonObjs,e)),u.disableFontFace||d?(o.save(),o.translate(t,r),o.beginPath(),a(o,l),i&&o.setTransform.apply(o,i),h!==n.TextRenderingMode.FILL&&h!==n.TextRenderingMode.FILL_STROKE||o.fill(),h!==n.TextRenderingMode.STROKE&&h!==n.TextRenderingMode.FILL_STROKE||o.stroke(),o.restore()):(h!==n.TextRenderingMode.FILL&&h!==n.TextRenderingMode.FILL_STROKE||o.fillText(e,t,r),h!==n.TextRenderingMode.STROKE&&h!==n.TextRenderingMode.FILL_STROKE||o.strokeText(e,t,r)),f)&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:o.mozCurrentTransform,x:t,y:r,fontSize:l,addToPath:a})},get isFontSubpixelAAEnabled(){var e=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10).context;e.scale(1.5,1),e.fillText("I",0,10);for(var t=e.getImageData(0,0,10,10).data,r=!1,i=3;i<t.length;i+=4)if(t[i]>0&&t[i]<255){r=!0;break}return(0,n.shadow)(this,"isFontSubpixelAAEnabled",r)},showText:function(e){var t=this.current,r=t.font;if(r.isType3Font)return this.showType3Text(e);var i=t.fontSize;if(0!==i){var a,o=this.ctx,s=t.fontSizeScale,u=t.charSpacing,c=t.wordSpacing,l=t.fontDirection,h=t.textHScale*l,f=e.length,d=r.vertical,p=d?1:-1,v=r.defaultVMetrics,m=i*t.fontMatrix[0],g=t.textRenderingMode===n.TextRenderingMode.FILL&&!r.disableFontFace&&!t.patternFill;if(o.save(),t.patternFill){o.save();var y=t.fillColor.getPattern(o,this);a=o.mozCurrentTransform,o.restore(),o.fillStyle=y}o.transform.apply(o,t.textMatrix),o.translate(t.x,t.y+t.textRise),l>0?o.scale(h,-1):o.scale(h,1);var _=t.lineWidth,b=t.textMatrixScale;if(0===b||0===_){var A=t.textRenderingMode&n.TextRenderingMode.FILL_STROKE_MASK;A!==n.TextRenderingMode.STROKE&&A!==n.TextRenderingMode.FILL_STROKE||(this._cachedGetSinglePixelWidth=null,_=.65*this.getSinglePixelWidth())}else _/=b;1!==s&&(o.scale(s,s),_/=s),o.lineWidth=_;var S,w=0;for(S=0;S<f;++S){var k=e[S];if((0,n.isNum)(k))w+=p*k*i/1e3;else{var x,C,P,R,T,E,I,O=!1,L=(k.isSpace?c:0)+u,F=k.fontChar,j=k.accent,N=k.width;if(d)T=k.vmetric||v,E=-(E=k.vmetric?T[1]:.5*N)*m,I=T[2]*m,N=T?-T[0]:N,x=E/s,C=(w+I)/s;else x=w/s,C=0;if(r.remeasure&&N>0){var M=1e3*o.measureText(F).width/i*s;if(N<M&&this.isFontSubpixelAAEnabled){var D=N/M;O=!0,o.save(),o.scale(D,1),x/=D}else N!==M&&(x+=(N-M)/2e3*i/s)}(k.isInFont||r.missingFile)&&(g&&!j?o.fillText(F,x,C):(this.paintChar(F,x,C,a),j&&(P=x+j.offset.x/s,R=C-j.offset.y/s,this.paintChar(j.fontChar,P,R,a)))),w+=N*m+L*l,O&&o.restore()}}d?t.y-=w*h:t.x+=w*h,o.restore()}},showType3Text:function(e){var t,r,i,a,o=this.ctx,s=this.current,u=s.font,c=s.fontSize,l=s.fontDirection,h=u.vertical?1:-1,f=s.charSpacing,d=s.wordSpacing,p=s.textHScale*l,v=s.fontMatrix||n.FONT_IDENTITY_MATRIX,m=e.length;if(!(s.textRenderingMode===n.TextRenderingMode.INVISIBLE)&&0!==c){for(this._cachedGetSinglePixelWidth=null,o.save(),o.transform.apply(o,s.textMatrix),o.translate(s.x,s.y),o.scale(p,l),t=0;t<m;++t)if(r=e[t],(0,n.isNum)(r))a=h*r*c/1e3,this.ctx.translate(a,0),s.x+=a*p;else{var g=(r.isSpace?d:0)+f,y=u.charProcOperatorList[r.operatorListId];if(y)this.processingType3=r,this.save(),o.scale(c,c),o.transform.apply(o,v),this.executeOperatorList(y),this.restore(),i=n.Util.applyTransform([r.width,0],v)[0]*c+g,o.translate(i,0),s.x+=i*p;else(0,n.warn)('Type3 character "'.concat(r.operatorListId,'" is not available.'))}o.restore(),this.processingType3=null}},setCharWidth:function(e,t){},setCharWidthAndBounds:function(e,t,r,n,i,a){this.ctx.rect(r,n,i-r,a-n),this.clip(),this.endPath()},getColorN_Pattern:function(t){var r,n=this;if("TilingPattern"===t[0]){var a=t[1],o=this.baseTransform||this.ctx.mozCurrentTransform.slice(),s={createCanvasGraphics:function(t){return new e(t,n.commonObjs,n.objs,n.canvasFactory,n.webGLContext)}};r=new i.TilingPattern(t,a,this.ctx,s,o)}else r=(0,i.getShadingPatternFromIR)(t);return r},setStrokeColorN:function(){this.current.strokeColor=this.getColorN_Pattern(arguments)},setFillColorN:function(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0},setStrokeRGBColor:function(e,t,r){var i=n.Util.makeCssRgb(e,t,r);this.ctx.strokeStyle=i,this.current.strokeColor=i},setFillRGBColor:function(e,t,r){var i=n.Util.makeCssRgb(e,t,r);this.ctx.fillStyle=i,this.current.fillColor=i,this.current.patternFill=!1},shadingFill:function(e){var t=this.ctx;this.save();var r=(0,i.getShadingPatternFromIR)(e);t.fillStyle=r.getPattern(t,this,!0);var a=t.mozCurrentTransformInverse;if(a){var o=t.canvas,s=o.width,u=o.height,c=n.Util.applyTransform([0,0],a),l=n.Util.applyTransform([0,u],a),h=n.Util.applyTransform([s,0],a),f=n.Util.applyTransform([s,u],a),d=Math.min(c[0],l[0],h[0],f[0]),p=Math.min(c[1],l[1],h[1],f[1]),v=Math.max(c[0],l[0],h[0],f[0]),m=Math.max(c[1],l[1],h[1],f[1]);this.ctx.fillRect(d,p,v-d,m-p)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.restore()},beginInlineImage:function(){(0,n.unreachable)("Should not call beginInlineImage")},beginImageData:function(){(0,n.unreachable)("Should not call beginImageData")},paintFormXObjectBegin:function(e,t){if(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(e)&&6===e.length&&this.transform.apply(this,e),this.baseTransform=this.ctx.mozCurrentTransform,t){var r=t[2]-t[0],n=t[3]-t[1];this.ctx.rect(t[0],t[1],r,n),this.clip(),this.endPath()}},paintFormXObjectEnd:function(){this.restore(),this.baseTransform=this.baseTransformStack.pop()},beginGroup:function(e){this.save();var t=this.ctx;e.isolated||(0,n.info)("TODO: Support non-isolated groups."),e.knockout&&(0,n.warn)("Knockout groups not supported.");var r=t.mozCurrentTransform;if(e.matrix&&t.transform.apply(t,e.matrix),!e.bbox)throw new Error("Bounding box is required.");var i=n.Util.getAxialAlignedBoundingBox(e.bbox,t.mozCurrentTransform),a=[0,0,t.canvas.width,t.canvas.height];i=n.Util.intersect(i,a)||[0,0,0,0];var o=Math.floor(i[0]),s=Math.floor(i[1]),u=Math.max(Math.ceil(i[2])-o,1),c=Math.max(Math.ceil(i[3])-s,1),h=1,f=1;u>4096&&(h=u/4096,u=4096),c>4096&&(f=c/4096,c=4096);var d="groupAt"+this.groupLevel;e.smask&&(d+="_smask_"+this.smaskCounter++%2);var p=this.cachedCanvases.getCanvas(d,u,c,!0),v=p.context;v.scale(1/h,1/f),v.translate(-o,-s),v.transform.apply(v,r),e.smask?this.smaskStack.push({canvas:p.canvas,context:v,offsetX:o,offsetY:s,scaleX:h,scaleY:f,subtype:e.smask.subtype,backdrop:e.smask.backdrop,transferMap:e.smask.transferMap||null,startTransformInverse:null}):(t.setTransform(1,0,0,1,0,0),t.translate(o,s),t.scale(h,f)),l(t,v),this.ctx=v,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(t),this.groupLevel++,this.current.activeSMask=null},endGroup:function(e){this.groupLevel--;var t=this.ctx;this.ctx=this.groupStack.pop(),void 0!==this.ctx.imageSmoothingEnabled?this.ctx.imageSmoothingEnabled=!1:this.ctx.mozImageSmoothingEnabled=!1,e.smask?this.tempSMask=this.smaskStack.pop():this.ctx.drawImage(t.canvas,0,0),this.restore()},beginAnnotations:function(){this.save(),this.baseTransform&&this.ctx.setTransform.apply(this.ctx,this.baseTransform)},endAnnotations:function(){this.restore()},beginAnnotation:function(e,t,r){if(this.save(),h(this.ctx),this.current=new c,Array.isArray(e)&&4===e.length){var n=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],n,i),this.clip(),this.endPath()}this.transform.apply(this,t),this.transform.apply(this,r)},endAnnotation:function(){this.restore()},paintJpegXObject:function(e,t,r){var i=this.processingType3?this.commonObjs.get(e):this.objs.get(e);if(i){this.save();var a=this.ctx;if(a.scale(1/t,-1/r),a.drawImage(i,0,0,i.width,i.height,0,-r,t,r),this.imageLayer){var o=a.mozCurrentTransformInverse,s=this.getCanvasPosition(0,0);this.imageLayer.appendImage({objId:e,left:s[0],top:s[1],width:t/o[0],height:r/o[3]})}this.restore()}else(0,n.warn)("Dependent image isn't ready yet")},paintImageMaskXObject:function(e){var t=this.ctx,n=e.width,i=e.height,a=this.current.fillColor,o=this.current.patternFill,s=this.processingType3;if(s&&void 0===s.compiled&&(s.compiled=n<=1e3&&i<=1e3?function(e){var t,r,n,i,a=e.width,o=e.height,s=a+1,u=new Uint8Array(s*(o+1)),c=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),l=a+7&-8,h=e.data,f=new Uint8Array(l*o),d=0;for(t=0,i=h.length;t<i;t++)for(var p=128,v=h[t];p>0;)f[d++]=v&p?0:255,p>>=1;var m=0;for(0!==f[d=0]&&(u[0]=1,++m),r=1;r<a;r++)f[d]!==f[d+1]&&(u[r]=f[d]?2:1,++m),d++;for(0!==f[d]&&(u[r]=2,++m),t=1;t<o;t++){n=t*s,f[(d=t*l)-l]!==f[d]&&(u[n]=f[d]?1:8,++m);var g=(f[d]?4:0)+(f[d-l]?8:0);for(r=1;r<a;r++)c[g=(g>>2)+(f[d+1]?4:0)+(f[d-l+1]?8:0)]&&(u[n+r]=c[g],++m),d++;if(f[d-l]!==f[d]&&(u[n+r]=f[d]?2:4,++m),m>1e3)return null}for(n=t*s,0!==f[d=l*(o-1)]&&(u[n]=8,++m),r=1;r<a;r++)f[d]!==f[d+1]&&(u[n+r]=f[d]?4:8,++m),d++;if(0!==f[d]&&(u[n+r]=4,++m),m>1e3)return null;var y=new Int32Array([0,s,-1,0,-s,0,0,0,1]),_=[];for(t=0;m&&t<=o;t++){for(var b=t*s,A=b+a;b<A&&!u[b];)b++;if(b!==A){var S,w=[b%s,t],k=u[b],x=b;do{var C=y[k];do{b+=C}while(!u[b]);5!==(S=u[b])&&10!==S?(k=S,u[b]=0):(k=S&51*k>>4,u[b]&=k>>2|k<<2),w.push(b%s),w.push(b/s|0),u[b]||--m}while(x!==b);_.push(w),--t}}return function(e){e.save(),e.scale(1/a,-1/o),e.translate(0,-o),e.beginPath();for(var t=0,r=_.length;t<r;t++){var n=_[t];e.moveTo(n[0],n[1]);for(var i=2,s=n.length;i<s;i+=2)e.lineTo(n[i],n[i+1])}e.fill(),e.beginPath(),e.restore()}}({data:e.data,width:n,height:i}):null),s&&s.compiled)s.compiled(t);else{var u=this.cachedCanvases.getCanvas("maskCanvas",n,i),c=u.context;c.save(),r(c,e),c.globalCompositeOperation="source-in",c.fillStyle=o?a.getPattern(c,this):a,c.fillRect(0,0,n,i),c.restore(),this.paintInlineImageXObject(u.canvas)}},paintImageMaskXObjectRepeat:function(e,t,n,i){var a=e.width,o=e.height,s=this.current.fillColor,u=this.current.patternFill,c=this.cachedCanvases.getCanvas("maskCanvas",a,o),l=c.context;l.save(),r(l,e),l.globalCompositeOperation="source-in",l.fillStyle=u?s.getPattern(l,this):s,l.fillRect(0,0,a,o),l.restore();for(var h=this.ctx,f=0,d=i.length;f<d;f+=2)h.save(),h.transform(t,0,0,n,i[f],i[f+1]),h.scale(1,-1),h.drawImage(c.canvas,0,0,a,o,0,-1,1,1),h.restore()},paintImageMaskXObjectGroup:function(e){for(var t=this.ctx,n=this.current.fillColor,i=this.current.patternFill,a=0,o=e.length;a<o;a++){var s=e[a],u=s.width,c=s.height,l=this.cachedCanvases.getCanvas("maskCanvas",u,c),h=l.context;h.save(),r(h,s),h.globalCompositeOperation="source-in",h.fillStyle=i?n.getPattern(h,this):n,h.fillRect(0,0,u,c),h.restore(),t.save(),t.transform.apply(t,s.transform),t.scale(1,-1),t.drawImage(l.canvas,0,0,u,c,0,-1,1,1),t.restore()}},paintImageXObject:function(e){var t=this.processingType3?this.commonObjs.get(e):this.objs.get(e);t?this.paintInlineImageXObject(t):(0,n.warn)("Dependent image isn't ready yet")},paintImageXObjectRepeat:function(e,t,r,i){var a=this.processingType3?this.commonObjs.get(e):this.objs.get(e);if(a){for(var o=a.width,s=a.height,u=[],c=0,l=i.length;c<l;c+=2)u.push({transform:[t,0,0,r,i[c],i[c+1]],x:0,y:0,w:o,h:s});this.paintInlineImageXObjectGroup(a,u)}else(0,n.warn)("Dependent image isn't ready yet")},paintInlineImageXObject:function(e){var r=e.width,n=e.height,i=this.ctx;this.save(),i.scale(1/r,-1/n);var a,o,s=i.mozCurrentTransformInverse,u=s[0],c=s[1],l=Math.max(Math.sqrt(u*u+c*c),1),h=s[2],f=s[3],d=Math.max(Math.sqrt(h*h+f*f),1);if("function"==typeof HTMLElement&&e instanceof HTMLElement||!e.data)a=e;else{var p=(o=this.cachedCanvases.getCanvas("inlineImage",r,n)).context;t(p,e),a=o.canvas}for(var v=r,m=n,g="prescale1";l>2&&v>1||d>2&&m>1;){var y=v,_=m;l>2&&v>1&&(l/=v/(y=Math.ceil(v/2))),d>2&&m>1&&(d/=m/(_=Math.ceil(m/2))),(p=(o=this.cachedCanvases.getCanvas(g,y,_)).context).clearRect(0,0,y,_),p.drawImage(a,0,0,v,m,0,0,y,_),a=o.canvas,v=y,m=_,g="prescale1"===g?"prescale2":"prescale1"}if(i.drawImage(a,0,0,v,m,0,-n,r,n),this.imageLayer){var b=this.getCanvasPosition(0,-n);this.imageLayer.appendImage({imgData:e,left:b[0],top:b[1],width:r/s[0],height:n/s[3]})}this.restore()},paintInlineImageXObjectGroup:function(e,r){var n=this.ctx,i=e.width,a=e.height,o=this.cachedCanvases.getCanvas("inlineImage",i,a);t(o.context,e);for(var s=0,u=r.length;s<u;s++){var c=r[s];if(n.save(),n.transform.apply(n,c.transform),n.scale(1,-1),n.drawImage(o.canvas,c.x,c.y,c.w,c.h,0,-1,1,1),this.imageLayer){var l=this.getCanvasPosition(c.x,c.y);this.imageLayer.appendImage({imgData:e,left:l[0],top:l[1],width:i,height:a})}n.restore()}},paintSolidColorImageMask:function(){this.ctx.fillRect(0,0,1,1)},paintXObject:function(){(0,n.warn)("Unsupported 'paintXObject' command.")},markPoint:function(e){},markPointProps:function(e,t){},beginMarkedContent:function(e){},beginMarkedContentProps:function(e,t){},endMarkedContent:function(){},beginCompat:function(){},endCompat:function(){},consumePath:function(){var e=this.ctx;this.pendingClip&&(this.pendingClip===_?e.clip("evenodd"):e.clip(),this.pendingClip=null),e.beginPath()},getSinglePixelWidth:function(e){if(null===this._cachedGetSinglePixelWidth){var t=this.ctx.mozCurrentTransformInverse;this._cachedGetSinglePixelWidth=Math.sqrt(Math.max(t[0]*t[0]+t[1]*t[1],t[2]*t[2]+t[3]*t[3]))}return this._cachedGetSinglePixelWidth},getCanvasPosition:function(e,t){var r=this.ctx.mozCurrentTransform;return[r[0]*e+r[2]*t+r[4],r[1]*e+r[3]*t+r[5]]}},n.OPS)e.prototype[n.OPS[b]]=e.prototype[b];return e}();t.CanvasGraphics=l},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getShadingPatternFromIR=function(e){var t=i[e[0]];if(!t)throw new Error("Unknown IR type: ".concat(e[0]));return t.fromIR(e)},t.TilingPattern=void 0;var n=r(1),i={RadialAxial:{fromIR:function(e){var t=e[1],r=e[2],n=e[3],i=e[4],a=e[5],o=e[6];return{type:"Pattern",getPattern:function(e){var s;"axial"===t?s=e.createLinearGradient(n[0],n[1],i[0],i[1]):"radial"===t&&(s=e.createRadialGradient(n[0],n[1],a,i[0],i[1],o));for(var u=0,c=r.length;u<c;++u){var l=r[u];s.addColorStop(l[0],l[1])}return s}}}}},a=function(){function e(e,t,r,n,i,a,o,s){var u,c=t.coords,l=t.colors,h=e.data,f=4*e.width;c[r+1]>c[n+1]&&(u=r,r=n,n=u,u=a,a=o,o=u),c[n+1]>c[i+1]&&(u=n,n=i,i=u,u=o,o=s,s=u),c[r+1]>c[n+1]&&(u=r,r=n,n=u,u=a,a=o,o=u);var d=(c[r]+t.offsetX)*t.scaleX,p=(c[r+1]+t.offsetY)*t.scaleY,v=(c[n]+t.offsetX)*t.scaleX,m=(c[n+1]+t.offsetY)*t.scaleY,g=(c[i]+t.offsetX)*t.scaleX,y=(c[i+1]+t.offsetY)*t.scaleY;if(!(p>=y))for(var _,b,A,S,w,k,x,C,P,R=l[a],T=l[a+1],E=l[a+2],I=l[o],O=l[o+1],L=l[o+2],F=l[s],j=l[s+1],N=l[s+2],M=Math.round(p),D=Math.round(y),q=M;q<=D;q++){q<m?(_=d-(d-v)*(P=q<p?0:p===m?1:(p-q)/(p-m)),b=R-(R-I)*P,A=T-(T-O)*P,S=E-(E-L)*P):(_=v-(v-g)*(P=q>y?1:m===y?0:(m-q)/(m-y)),b=I-(I-F)*P,A=O-(O-j)*P,S=L-(L-N)*P),w=d-(d-g)*(P=q<p?0:q>y?1:(p-q)/(p-y)),k=R-(R-F)*P,x=T-(T-j)*P,C=E-(E-N)*P;for(var U=Math.round(Math.min(_,w)),W=Math.round(Math.max(_,w)),B=f*q+4*U,z=U;z<=W;z++)P=(P=(_-z)/(_-w))<0?0:P>1?1:P,h[B++]=b-(b-k)*P|0,h[B++]=A-(A-x)*P|0,h[B++]=S-(S-C)*P|0,h[B++]=255}}function t(t,r,n){var i,a,o=r.coords,s=r.colors;switch(r.type){case"lattice":var u=r.verticesPerRow,c=Math.floor(o.length/u)-1,l=u-1;for(i=0;i<c;i++)for(var h=i*u,f=0;f<l;f++,h++)e(t,n,o[h],o[h+1],o[h+u],s[h],s[h+1],s[h+u]),e(t,n,o[h+u+1],o[h+1],o[h+u],s[h+u+1],s[h+1],s[h+u]);break;case"triangles":for(i=0,a=o.length;i<a;i+=3)e(t,n,o[i],o[i+1],o[i+2],s[i],s[i+1],s[i+2]);break;default:throw new Error("illegal figure")}}return function(e,r,n,i,a,o,s,u){var c,l,h,f,d=Math.floor(e[0]),p=Math.floor(e[1]),v=Math.ceil(e[2])-d,m=Math.ceil(e[3])-p,g=Math.min(Math.ceil(Math.abs(v*r[0]*1.1)),3e3),y=Math.min(Math.ceil(Math.abs(m*r[1]*1.1)),3e3),_=v/g,b=m/y,A={coords:n,colors:i,offsetX:-d,offsetY:-p,scaleX:1/_,scaleY:1/b},S=g+4,w=y+4;if(u.isEnabled)c=u.drawFigures({width:g,height:y,backgroundColor:o,figures:a,context:A}),(l=s.getCanvas("mesh",S,w,!1)).context.drawImage(c,2,2),c=l.canvas;else{var k=(l=s.getCanvas("mesh",S,w,!1)).context,x=k.createImageData(g,y);if(o){var C=x.data;for(h=0,f=C.length;h<f;h+=4)C[h]=o[0],C[h+1]=o[1],C[h+2]=o[2],C[h+3]=255}for(h=0;h<a.length;h++)t(x,a[h],A);k.putImageData(x,2,2),c=l.canvas}return{canvas:c,offsetX:d-2*_,offsetY:p-2*b,scaleX:_,scaleY:b}}}();i.Mesh={fromIR:function(e){var t=e[2],r=e[3],i=e[4],o=e[5],s=e[6],u=e[8];return{type:"Pattern",getPattern:function(e,c,l){var h;if(l)h=n.Util.singularValueDecompose2dScale(e.mozCurrentTransform);else if(h=n.Util.singularValueDecompose2dScale(c.baseTransform),s){var f=n.Util.singularValueDecompose2dScale(s);h=[h[0]*f[0],h[1]*f[1]]}var d=a(o,h,t,r,i,l?null:u,c.cachedCanvases,c.webGLContext);return l||(e.setTransform.apply(e,c.baseTransform),s&&e.transform.apply(e,s)),e.translate(d.offsetX,d.offsetY),e.scale(d.scaleX,d.scaleY),e.createPattern(d.canvas,"no-repeat")}}}},i.Dummy={fromIR:function(){return{type:"Pattern",getPattern:function(){return"hotpink"}}}};var o=function(){var e=1,t=2;function r(e,t,r,n,i){this.operatorList=e[2],this.matrix=e[3]||[1,0,0,1,0,0],this.bbox=e[4],this.xstep=e[5],this.ystep=e[6],this.paintType=e[7],this.tilingType=e[8],this.color=t,this.canvasGraphicsFactory=n,this.baseTransform=i,this.type="Pattern",this.ctx=r}return r.prototype={createPatternCanvas:function(e){var t=this.operatorList,r=this.bbox,i=this.xstep,a=this.ystep,o=this.paintType,s=this.tilingType,u=this.color,c=this.canvasGraphicsFactory;(0,n.info)("TilingType: "+s);var l=r[0],h=r[1],f=r[2],d=r[3],p=n.Util.singularValueDecompose2dScale(this.matrix),v=n.Util.singularValueDecompose2dScale(this.baseTransform),m=[p[0]*v[0],p[1]*v[1]],g=this.getSizeAndScale(i,this.ctx.canvas.width,m[0]),y=this.getSizeAndScale(a,this.ctx.canvas.height,m[1]),_=e.cachedCanvases.getCanvas("pattern",g.size,y.size,!0),b=_.context,A=c.createCanvasGraphics(b);return A.groupLevel=e.groupLevel,this.setFillAndStrokeStyleToContext(A,o,u),A.transform(g.scale,0,0,y.scale,0,0),A.transform(1,0,0,1,-l,-h),this.clipBbox(A,r,l,h,f,d),A.executeOperatorList(t),this.ctx.transform(1,0,0,1,l,h),this.ctx.scale(1/g.scale,1/y.scale),_.canvas},getSizeAndScale:function(e,t,r){e=Math.abs(e);var n=Math.max(3e3,t),i=Math.ceil(e*r);return i>=n?i=n:r=i/e,{scale:r,size:i}},clipBbox:function(e,t,r,n,i,a){if(Array.isArray(t)&&4===t.length){var o=i-r,s=a-n;e.ctx.rect(r,n,o,s),e.clip(),e.endPath()}},setFillAndStrokeStyleToContext:function(r,i,a){var o=r.ctx,s=r.current;switch(i){case e:var u=this.ctx;o.fillStyle=u.fillStyle,o.strokeStyle=u.strokeStyle,s.fillColor=u.fillStyle,s.strokeColor=u.strokeStyle;break;case t:var c=n.Util.makeCssRgb(a[0],a[1],a[2]);o.fillStyle=c,o.strokeStyle=c,s.fillColor=c,s.strokeColor=c;break;default:throw new n.FormatError("Unsupported paint type: ".concat(i))}},getPattern:function(e,t){(e=this.ctx).setTransform.apply(e,this.baseTransform),e.transform.apply(e,this.matrix);var r=this.createPatternCanvas(t);return e.createPattern(r,"repeat")}},r}();t.TilingPattern=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GlobalWorkerOptions=void 0;var n=Object.create(null);t.GlobalWorkerOptions=n,n.workerPort=void 0===n.workerPort?null:n.workerPort,n.workerSrc=void 0===n.workerSrc?"":n.workerSrc},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageHandler=f;var n,i=(n=r(148))&&n.__esModule?n:{default:n},a=r(1);function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function u(e,t){return c.apply(this,arguments)}function c(){var e;return e=i.default.mark(function e(t,r){var n,a=arguments;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=a.length>2&&void 0!==a[2]?a[2]:null,t){e.next=3;break}return e.abrupt("return",void 0);case 3:return e.abrupt("return",t.apply(n,r));case 4:case"end":return e.stop()}},e)}),(c=function(){var t=this,r=arguments;return new Promise(function(n,i){var a=e.apply(t,r);function o(e){s(a,n,i,o,u,"next",e)}function u(e){s(a,n,i,o,u,"throw",e)}o(void 0)})}).apply(this,arguments)}function l(e){if("object"!==o(e))return e;switch(e.name){case"AbortException":return new a.AbortException(e.message);case"MissingPDFException":return new a.MissingPDFException(e.message);case"UnexpectedResponseException":return new a.UnexpectedResponseException(e.message,e.status);default:return new a.UnknownErrorException(e.message,e.details)}}function h(e,t,r){t?e.resolve():e.reject(r)}function f(e,t,r){var n=this;this.sourceName=e,this.targetName=t,this.comObj=r,this.callbackId=1,this.streamId=1,this.postMessageTransfers=!0,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null);var i=this.callbacksCapabilities=Object.create(null),o=this.actionHandler=Object.create(null);this._onComObjOnMessage=function(e){var t=e.data;if(t.targetName===n.sourceName)if(t.stream)n._processStreamMessage(t);else if(t.isReply){var s=t.callbackId;if(!(t.callbackId in i))throw new Error("Cannot resolve callback ".concat(s));var u=i[s];delete i[s],"error"in t?u.reject(l(t.error)):u.resolve(t.data)}else{if(!(t.action in o))throw new Error("Unknown action from worker: ".concat(t.action));var c=o[t.action];if(t.callbackId){var h=n.sourceName,f=t.sourceName;Promise.resolve().then(function(){return c[0].call(c[1],t.data)}).then(function(e){r.postMessage({sourceName:h,targetName:f,isReply:!0,callbackId:t.callbackId,data:e})},function(e){r.postMessage({sourceName:h,targetName:f,isReply:!0,callbackId:t.callbackId,error:function(e){return!(e instanceof Error)||e instanceof a.AbortException||e instanceof a.MissingPDFException||e instanceof a.UnexpectedResponseException||e instanceof a.UnknownErrorException?e:new a.UnknownErrorException(e.message,e.toString())}(e)})})}else t.streamId?n._createStreamSink(t):c[0].call(c[1],t.data)}},r.addEventListener("message",this._onComObjOnMessage)}f.prototype={on:function(e,t,r){var n=this.actionHandler;if(n[e])throw new Error('There is already an actionName called "'.concat(e,'"'));n[e]=[t,r]},send:function(e,t,r){var n={sourceName:this.sourceName,targetName:this.targetName,action:e,data:t};this.postMessage(n,r)},sendWithPromise:function(e,t,r){var n=this.callbackId++,i={sourceName:this.sourceName,targetName:this.targetName,action:e,data:t,callbackId:n},o=(0,a.createPromiseCapability)();this.callbacksCapabilities[n]=o;try{this.postMessage(i,r)}catch(e){o.reject(e)}return o.promise},sendWithStream:function(e,t,r,n){var i=this,o=this.streamId++,s=this.sourceName,u=this.targetName;return new a.ReadableStream({start:function(r){var n=(0,a.createPromiseCapability)();return i.streamControllers[o]={controller:r,startCall:n,isClosed:!1},i.postMessage({sourceName:s,targetName:u,action:e,streamId:o,data:t,desiredSize:r.desiredSize}),n.promise},pull:function(e){var t=(0,a.createPromiseCapability)();return i.streamControllers[o].pullCall=t,i.postMessage({sourceName:s,targetName:u,stream:"pull",streamId:o,desiredSize:e.desiredSize}),t.promise},cancel:function(e){var t=(0,a.createPromiseCapability)();return i.streamControllers[o].cancelCall=t,i.streamControllers[o].isClosed=!0,i.postMessage({sourceName:s,targetName:u,stream:"cancel",reason:e,streamId:o}),t.promise}},r)},_createStreamSink:function(e){var t=this,r=this,n=this.actionHandler[e.action],i=e.streamId,o=e.desiredSize,s=this.sourceName,c=e.sourceName,l=function(e){var r=e.stream,n=e.chunk,a=e.transfers,o=e.success,u=e.reason;t.postMessage({sourceName:s,targetName:c,stream:r,streamId:i,chunk:n,success:o,reason:u},a)},h={enqueue:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2?arguments[2]:void 0;if(!this.isCancelled){var n=this.desiredSize;this.desiredSize-=t,n>0&&this.desiredSize<=0&&(this.sinkCapability=(0,a.createPromiseCapability)(),this.ready=this.sinkCapability.promise),l({stream:"enqueue",chunk:e,transfers:r})}},close:function(){this.isCancelled||(this.isCancelled=!0,l({stream:"close"}),delete r.streamSinks[i])},error:function(e){this.isCancelled||(this.isCancelled=!0,l({stream:"error",reason:e}))},sinkCapability:(0,a.createPromiseCapability)(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:o,ready:null};h.sinkCapability.resolve(),h.ready=h.sinkCapability.promise,this.streamSinks[i]=h,u(n[0],[e.data,h],n[1]).then(function(){l({stream:"start_complete",success:!0})},function(e){l({stream:"start_complete",success:!1,reason:e})})},_processStreamMessage:function(e){var t=this,r=this.sourceName,n=e.sourceName,i=e.streamId,o=function(e){var a=e.stream,o=e.success,s=e.reason;t.comObj.postMessage({sourceName:r,targetName:n,stream:a,success:o,streamId:i,reason:s})},s=function(){Promise.all([t.streamControllers[e.streamId].startCall,t.streamControllers[e.streamId].pullCall,t.streamControllers[e.streamId].cancelCall].map(function(e){return e&&(t=e.promise,Promise.resolve(t).catch(function(){}));var t})).then(function(){delete t.streamControllers[e.streamId]})};switch(e.stream){case"start_complete":h(this.streamControllers[e.streamId].startCall,e.success,l(e.reason));break;case"pull_complete":h(this.streamControllers[e.streamId].pullCall,e.success,l(e.reason));break;case"pull":if(!this.streamSinks[e.streamId]){o({stream:"pull_complete",success:!0});break}this.streamSinks[e.streamId].desiredSize<=0&&e.desiredSize>0&&this.streamSinks[e.streamId].sinkCapability.resolve(),this.streamSinks[e.streamId].desiredSize=e.desiredSize,u(this.streamSinks[e.streamId].onPull).then(function(){o({stream:"pull_complete",success:!0})},function(e){o({stream:"pull_complete",success:!1,reason:e})});break;case"enqueue":(0,a.assert)(this.streamControllers[e.streamId],"enqueue should have stream controller"),this.streamControllers[e.streamId].isClosed||this.streamControllers[e.streamId].controller.enqueue(e.chunk);break;case"close":if((0,a.assert)(this.streamControllers[e.streamId],"close should have stream controller"),this.streamControllers[e.streamId].isClosed)break;this.streamControllers[e.streamId].isClosed=!0,this.streamControllers[e.streamId].controller.close(),s();break;case"error":(0,a.assert)(this.streamControllers[e.streamId],"error should have stream controller"),this.streamControllers[e.streamId].controller.error(l(e.reason)),s();break;case"cancel_complete":h(this.streamControllers[e.streamId].cancelCall,e.success,l(e.reason)),s();break;case"cancel":if(!this.streamSinks[e.streamId])break;u(this.streamSinks[e.streamId].onCancel,[l(e.reason)]).then(function(){o({stream:"cancel_complete",success:!0})},function(e){o({stream:"cancel_complete",success:!1,reason:e})}),this.streamSinks[e.streamId].sinkCapability.reject(l(e.reason)),this.streamSinks[e.streamId].isCancelled=!0,delete this.streamSinks[e.streamId];break;default:throw new Error("Unexpected stream case")}},postMessage:function(e,t){t&&this.postMessageTransfers?this.comObj.postMessage(e,t):this.comObj.postMessage(e)},destroy:function(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Metadata=void 0;var n=r(1),i=r(159);function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var o=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),(0,n.assert)("string"==typeof t,"Metadata: input is not a string"),t=this._repair(t);var r=(new i.SimpleXMLParser).parseFromString(t);this._metadata=Object.create(null),r&&this._parse(r)}var t,r,o;return t=e,(r=[{key:"_repair",value:function(e){return e.replace(/^([^<]+)/,"").replace(/>\\376\\377([^<]+)/g,function(e,t){for(var r=t.replace(/\\([0-3])([0-7])([0-7])/g,function(e,t,r,n){return String.fromCharCode(64*t+8*r+1*n)}).replace(/&(amp|apos|gt|lt|quot);/g,function(e,t){switch(t){case"amp":return"&";case"apos":return"'";case"gt":return">";case"lt":return"<";case"quot":return'"'}throw new Error("_repair: ".concat(t," isn't defined."))}),n="",i=0,a=r.length;i<a;i+=2){var o=256*r.charCodeAt(i)+r.charCodeAt(i+1);n+=o>=32&&o<127&&60!==o&&62!==o&&38!==o?String.fromCharCode(o):"&#x"+(65536+o).toString(16).substring(1)+";"}return">"+n})}},{key:"_parse",value:function(e){var t=e.documentElement;if("rdf:rdf"!==t.nodeName.toLowerCase())for(t=t.firstChild;t&&"rdf:rdf"!==t.nodeName.toLowerCase();)t=t.nextSibling;var r=t?t.nodeName.toLowerCase():null;if(t&&"rdf:rdf"===r&&t.hasChildNodes())for(var n=t.childNodes,i=0,a=n.length;i<a;i++){var o=n[i];if("rdf:description"===o.nodeName.toLowerCase())for(var s=0,u=o.childNodes.length;s<u;s++)if("#text"!==o.childNodes[s].nodeName.toLowerCase()){var c=o.childNodes[s],l=c.nodeName.toLowerCase();this._metadata[l]=c.textContent.trim()}}}},{key:"get",value:function(e){var t=this._metadata[e];return void 0!==t?t:null}},{key:"getAll",value:function(){return this._metadata}},{key:"has",value:function(e){return void 0!==this._metadata[e]}}])&&a(t.prototype,r),o&&a(t,o),e}();t.Metadata=o},function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function a(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function o(e,t,r){return(o="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=s(e)););return e}(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}})(e,t,r||e)}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function u(e,t){return(u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function h(e,t,r){return t&&l(e.prototype,t),r&&l(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleXMLParser=void 0;var f={NoError:0,EndOfDocument:-1,UnterminatedCdat:-2,UnterminatedXmlDeclaration:-3,UnterminatedDoctypeDeclaration:-4,UnterminatedComment:-5,MalformedElement:-6,OutOfMemory:-7,UnterminatedAttributeValue:-8,UnterminatedElement:-9,ElementNeverBegun:-10};function d(e,t){var r=e[t];return" "===r||"\n"===r||"\r"===r||"\t"===r}var p=function(){function e(){c(this,e)}return h(e,[{key:"_resolveEntities",value:function(e){var t=this;return e.replace(/&([^;]+);/g,function(e,r){if("#x"===r.substring(0,2))return String.fromCharCode(parseInt(r.substring(2),16));if("#"===r.substring(0,1))return String.fromCharCode(parseInt(r.substring(1),10));switch(r){case"lt":return"<";case"gt":return">";case"amp":return"&";case"quot":return'"'}return t.onResolveEntity(r)})}},{key:"_parseContent",value:function(e,t){var r,n=t,i=[];function a(){for(;n<e.length&&d(e,n);)++n}for(;n<e.length&&!d(e,n)&&">"!==e[n]&&"/"!==e[n];)++n;for(r=e.substring(t,n),a();n<e.length&&">"!==e[n]&&"/"!==e[n]&&"?"!==e[n];){a();for(var o,s="";n<e.length&&!d(e,n)&&"="!==e[n];)s+=e[n],++n;if(a(),"="!==e[n])return null;++n,a();var u=e[n];if('"'!==u&&"'"!==u)return null;var c=e.indexOf(u,++n);if(c<0)return null;o=e.substring(n,c),i.push({name:s,value:this._resolveEntities(o)}),n=c+1,a()}return{name:r,attributes:i,parsed:n-t}}},{key:"_parseProcessingInstruction",value:function(e,t){var r,n=t;for(;n<e.length&&!d(e,n)&&">"!==e[n]&&"/"!==e[n];)++n;r=e.substring(t,n),function(){for(;n<e.length&&d(e,n);)++n}();for(var i=n;n<e.length&&("?"!==e[n]||">"!==e[n+1]);)++n;return{name:r,value:e.substring(i,n),parsed:n-t}}},{key:"parseXml",value:function(e){for(var t=0;t<e.length;){var r=t;if("<"===e[t]){var n=void 0;switch(e[++r]){case"/":if(++r,(n=e.indexOf(">",r))<0)return void this.onError(f.UnterminatedElement);this.onEndElement(e.substring(r,n)),r=n+1;break;case"?":++r;var i=this._parseProcessingInstruction(e,r);if("?>"!==e.substring(r+i.parsed,r+i.parsed+2))return void this.onError(f.UnterminatedXmlDeclaration);this.onPi(i.name,i.value),r+=i.parsed+2;break;case"!":if("--"===e.substring(r+1,r+3)){if((n=e.indexOf("--\x3e",r+3))<0)return void this.onError(f.UnterminatedComment);this.onComment(e.substring(r+3,n)),r=n+3}else if("[CDATA["===e.substring(r+1,r+8)){if((n=e.indexOf("]]>",r+8))<0)return void this.onError(f.UnterminatedCdat);this.onCdata(e.substring(r+8,n)),r=n+3}else{if("DOCTYPE"!==e.substring(r+1,r+8))return void this.onError(f.MalformedElement);var a=e.indexOf("[",r+8),o=!1;if((n=e.indexOf(">",r+8))<0)return void this.onError(f.UnterminatedDoctypeDeclaration);if(a>0&&n>a){if((n=e.indexOf("]>",r+8))<0)return void this.onError(f.UnterminatedDoctypeDeclaration);o=!0}var s=e.substring(r+8,n+(o?1:0));this.onDoctype(s),r=n+(o?2:1)}break;default:var u=this._parseContent(e,r);if(null===u)return void this.onError(f.MalformedElement);var c=!1;if("/>"===e.substring(r+u.parsed,r+u.parsed+2))c=!0;else if(">"!==e.substring(r+u.parsed,r+u.parsed+1))return void this.onError(f.UnterminatedElement);this.onBeginElement(u.name,u.attributes,c),r+=u.parsed+(c?2:1)}}else{for(;r<e.length&&"<"!==e[r];)r++;var l=e.substring(t,r);this.onText(this._resolveEntities(l))}t=r}}},{key:"onResolveEntity",value:function(e){return"&".concat(e,";")}},{key:"onPi",value:function(e,t){}},{key:"onComment",value:function(e){}},{key:"onCdata",value:function(e){}},{key:"onDoctype",value:function(e){}},{key:"onText",value:function(e){}},{key:"onBeginElement",value:function(e,t,r){}},{key:"onEndElement",value:function(e){}},{key:"onError",value:function(e){}}]),e}(),v=function(){function e(t,r){c(this,e),this.nodeName=t,this.nodeValue=r,Object.defineProperty(this,"parentNode",{value:null,writable:!0})}return h(e,[{key:"hasChildNodes",value:function(){return this.childNodes&&this.childNodes.length>0}},{key:"firstChild",get:function(){return this.childNodes&&this.childNodes[0]}},{key:"nextSibling",get:function(){var e=this.parentNode.childNodes;if(e){var t=e.indexOf(this);if(-1!==t)return e[t+1]}}},{key:"textContent",get:function(){return this.childNodes?this.childNodes.map(function(e){return e.textContent}).join(""):this.nodeValue||""}}]),e}(),m=function(e){function t(){var e;return c(this,t),(e=a(this,s(t).call(this)))._currentFragment=null,e._stack=null,e._errorCode=f.NoError,e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}(t,p),h(t,[{key:"parseFromString",value:function(e){if(this._currentFragment=[],this._stack=[],this._errorCode=f.NoError,this.parseXml(e),this._errorCode===f.NoError){var t=i(this._currentFragment,1)[0];if(t)return{documentElement:t}}}},{key:"onResolveEntity",value:function(e){switch(e){case"apos":return"'"}return o(s(t.prototype),"onResolveEntity",this).call(this,e)}},{key:"onText",value:function(e){if(!function(e){for(var t=0,r=e.length;t<r;t++)if(!d(e,t))return!1;return!0}(e)){var t=new v("#text",e);this._currentFragment.push(t)}}},{key:"onCdata",value:function(e){var t=new v("#text",e);this._currentFragment.push(t)}},{key:"onBeginElement",value:function(e,t,r){var n=new v(e);n.childNodes=[],this._currentFragment.push(n),r||(this._stack.push(this._currentFragment),this._currentFragment=n.childNodes)}},{key:"onEndElement",value:function(e){this._currentFragment=this._stack.pop()||[];var t=this._currentFragment[this._currentFragment.length-1];if(t)for(var r=0,n=t.childNodes.length;r<n;r++)t.childNodes[r].parentNode=t}},{key:"onError",value:function(e){this._errorCode=e}}]),t}();t.SimpleXMLParser=m},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFDataTransportStream=void 0;var n,i=(n=r(148))&&n.__esModule?n:{default:n},a=r(1);function o(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function s(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var a=e.apply(t,r);function s(e){o(a,n,i,s,u,"next",e)}function u(e){o(a,n,i,s,u,"throw",e)}s(void 0)})}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function l(e,t,r){return t&&c(e.prototype,t),r&&c(e,r),e}var h=function(){function e(t,r){var n=this;u(this,e),(0,a.assert)(r),this._queuedChunks=[],this._progressiveDone=t.progressiveDone||!1;var i=t.initialData;if(i&&i.length>0){var o=new Uint8Array(i).buffer;this._queuedChunks.push(o)}this._pdfDataRangeTransport=r,this._isStreamingSupported=!t.disableStream,this._isRangeSupported=!t.disableRange,this._contentLength=t.length,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener(function(e,t){n._onReceiveData({begin:e,chunk:t})}),this._pdfDataRangeTransport.addProgressListener(function(e,t){n._onProgress({loaded:e,total:t})}),this._pdfDataRangeTransport.addProgressiveReadListener(function(e){n._onReceiveData({chunk:e})}),this._pdfDataRangeTransport.addProgressiveDoneListener(function(){n._onProgressiveDone()}),this._pdfDataRangeTransport.transportReady()}return l(e,[{key:"_onReceiveData",value:function(e){var t=new Uint8Array(e.chunk).buffer;if(void 0===e.begin)this._fullRequestReader?this._fullRequestReader._enqueue(t):this._queuedChunks.push(t);else{var r=this._rangeReaders.some(function(r){return r._begin===e.begin&&(r._enqueue(t),!0)});(0,a.assert)(r)}}},{key:"_onProgress",value:function(e){if(void 0===e.total){var t=this._rangeReaders[0];t&&t.onProgress&&t.onProgress({loaded:e.loaded})}else{var r=this._fullRequestReader;r&&r.onProgress&&r.onProgress({loaded:e.loaded,total:e.total})}}},{key:"_onProgressiveDone",value:function(){this._fullRequestReader&&this._fullRequestReader.progressiveDone(),this._progressiveDone=!0}},{key:"_removeRangeReader",value:function(e){var t=this._rangeReaders.indexOf(e);t>=0&&this._rangeReaders.splice(t,1)}},{key:"getFullReader",value:function(){(0,a.assert)(!this._fullRequestReader);var e=this._queuedChunks;return this._queuedChunks=null,new f(this,e,this._progressiveDone)}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var r=new d(this,e,t);return this._pdfDataRangeTransport.requestDataRange(e,t),this._rangeReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeReaders.slice(0).forEach(function(t){t.cancel(e)}),this._pdfDataRangeTransport.abort()}},{key:"_progressiveDataLength",get:function(){return this._fullRequestReader?this._fullRequestReader._loaded:0}}]),e}();t.PDFDataTransportStream=h;var f=function(){function e(t,r){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];u(this,e),this._stream=t,this._done=n||!1,this._filename=null,this._queuedChunks=r||[],this._loaded=0;var i=!0,a=!1,o=void 0;try{for(var s,c=this._queuedChunks[Symbol.iterator]();!(i=(s=c.next()).done);i=!0){var l=s.value;this._loaded+=l.byteLength}}catch(e){a=!0,o=e}finally{try{i||null==c.return||c.return()}finally{if(a)throw o}}this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}return l(e,[{key:"_enqueue",value:function(e){if(!this._done){if(this._requests.length>0)this._requests.shift().resolve({value:e,done:!1});else this._queuedChunks.push(e);this._loaded+=e.byteLength}}},{key:"read",value:function(){var e=s(i.default.mark(function e(){var t,r;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(this._queuedChunks.length>0)){e.next=3;break}return t=this._queuedChunks.shift(),e.abrupt("return",{value:t,done:!1});case 3:if(!this._done){e.next=5;break}return e.abrupt("return",{value:void 0,done:!0});case 5:return r=(0,a.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 8:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._done=!0,this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[]}},{key:"progressiveDone",value:function(){this._done||(this._done=!0)}},{key:"headersReady",get:function(){return this._headersReady}},{key:"filename",get:function(){return this._filename}},{key:"isRangeSupported",get:function(){return this._stream._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._stream._isStreamingSupported}},{key:"contentLength",get:function(){return this._stream._contentLength}}]),e}(),d=function(){function e(t,r,n){u(this,e),this._stream=t,this._begin=r,this._end=n,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}return l(e,[{key:"_enqueue",value:function(e){if(!this._done){if(0===this._requests.length)this._queuedChunk=e;else this._requests.shift().resolve({value:e,done:!1}),this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[];this._done=!0,this._stream._removeRangeReader(this)}}},{key:"read",value:function(){var e=s(i.default.mark(function e(){var t,r;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._queuedChunk){e.next=4;break}return t=this._queuedChunk,this._queuedChunk=null,e.abrupt("return",{value:t,done:!1});case 4:if(!this._done){e.next=6;break}return e.abrupt("return",{value:void 0,done:!0});case 6:return r=(0,a.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 9:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._done=!0,this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[],this._stream._removeRangeReader(this)}},{key:"isStreamingSupported",get:function(){return!1}}]),e}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WebGLContext=void 0;var n=r(1);function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var a=function(){function e(t){var r=t.enable,n=void 0!==r&&r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._enabled=!0===n}var t,r,a;return t=e,(r=[{key:"composeSMask",value:function(e){var t=e.layer,r=e.mask,n=e.properties;return o.composeSMask(t,r,n)}},{key:"drawFigures",value:function(e){var t=e.width,r=e.height,n=e.backgroundColor,i=e.figures,a=e.context;return o.drawFigures(t,r,n,i,a)}},{key:"clear",value:function(){o.cleanup()}},{key:"isEnabled",get:function(){var e=this._enabled;return e&&(e=o.tryInitGL()),(0,n.shadow)(this,"isEnabled",e)}}])&&i(t.prototype,r),a&&i(t,a),e}();t.WebGLContext=a;var o=function(){function e(e,t,r){var n=e.createShader(r);if(e.shaderSource(n,t),e.compileShader(n),!e.getShaderParameter(n,e.COMPILE_STATUS)){var i=e.getShaderInfoLog(n);throw new Error("Error during shader compilation: "+i)}return n}function t(t,r){return e(t,r,t.VERTEX_SHADER)}function r(t,r){return e(t,r,t.FRAGMENT_SHADER)}function n(e,t){for(var r=e.createProgram(),n=0,i=t.length;n<i;++n)e.attachShader(r,t[n]);if(e.linkProgram(r),!e.getProgramParameter(r,e.LINK_STATUS)){var a=e.getProgramInfoLog(r);throw new Error("Error during program linking: "+a)}return r}function i(e,t,r){e.activeTexture(r);var n=e.createTexture();return e.bindTexture(e.TEXTURE_2D,n),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t),n}var a,o;function s(){a||(o=document.createElement("canvas"),a=o.getContext("webgl",{premultipliedalpha:!1}))}var u="  attribute vec2 a_position;                                      attribute vec2 a_texCoord;                                                                                                      uniform vec2 u_resolution;                                                                                                      varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec2 clipSpace = (a_position / u_resolution) * 2.0 - 1.0;       gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_texCoord = a_texCoord;                                      }                                                             ",c="  precision mediump float;                                                                                                        uniform vec4 u_backdrop;                                        uniform int u_subtype;                                          uniform sampler2D u_image;                                      uniform sampler2D u_mask;                                                                                                       varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec4 imageColor = texture2D(u_image, v_texCoord);               vec4 maskColor = texture2D(u_mask, v_texCoord);                 if (u_backdrop.a > 0.0) {                                         maskColor.rgb = maskColor.rgb * maskColor.a +                                   u_backdrop.rgb * (1.0 - maskColor.a);         }                                                               float lum;                                                      if (u_subtype == 0) {                                             lum = maskColor.a;                                            } else {                                                          lum = maskColor.r * 0.3 + maskColor.g * 0.59 +                        maskColor.b * 0.11;                                     }                                                               imageColor.a *= lum;                                            imageColor.rgb *= imageColor.a;                                 gl_FragColor = imageColor;                                    }                                                             ",l=null;var h="  attribute vec2 a_position;                                      attribute vec3 a_color;                                                                                                         uniform vec2 u_resolution;                                      uniform vec2 u_scale;                                           uniform vec2 u_offset;                                                                                                          varying vec4 v_color;                                                                                                           void main() {                                                     vec2 position = (a_position + u_offset) * u_scale;              vec2 clipSpace = (position / u_resolution) * 2.0 - 1.0;         gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_color = vec4(a_color / 255.0, 1.0);                         }                                                             ",f="  precision mediump float;                                                                                                        varying vec4 v_color;                                                                                                           void main() {                                                     gl_FragColor = v_color;                                       }                                                             ",d=null;return{tryInitGL:function(){try{return s(),!!a}catch(e){}return!1},composeSMask:function(e,h,f){var d=e.width,p=e.height;l||function(){var e,i;s(),e=o,o=null,i=a,a=null;var h=n(i,[t(i,u),r(i,c)]);i.useProgram(h);var f={};f.gl=i,f.canvas=e,f.resolutionLocation=i.getUniformLocation(h,"u_resolution"),f.positionLocation=i.getAttribLocation(h,"a_position"),f.backdropLocation=i.getUniformLocation(h,"u_backdrop"),f.subtypeLocation=i.getUniformLocation(h,"u_subtype");var d=i.getAttribLocation(h,"a_texCoord"),p=i.getUniformLocation(h,"u_image"),v=i.getUniformLocation(h,"u_mask"),m=i.createBuffer();i.bindBuffer(i.ARRAY_BUFFER,m),i.bufferData(i.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),i.STATIC_DRAW),i.enableVertexAttribArray(d),i.vertexAttribPointer(d,2,i.FLOAT,!1,0,0),i.uniform1i(p,0),i.uniform1i(v,1),l=f}();var v=l,m=v.canvas,g=v.gl;m.width=d,m.height=p,g.viewport(0,0,g.drawingBufferWidth,g.drawingBufferHeight),g.uniform2f(v.resolutionLocation,d,p),f.backdrop?g.uniform4f(v.resolutionLocation,f.backdrop[0],f.backdrop[1],f.backdrop[2],1):g.uniform4f(v.resolutionLocation,0,0,0,0),g.uniform1i(v.subtypeLocation,"Luminosity"===f.subtype?1:0);var y=i(g,e,g.TEXTURE0),_=i(g,h,g.TEXTURE1),b=g.createBuffer();return g.bindBuffer(g.ARRAY_BUFFER,b),g.bufferData(g.ARRAY_BUFFER,new Float32Array([0,0,d,0,0,p,0,p,d,0,d,p]),g.STATIC_DRAW),g.enableVertexAttribArray(v.positionLocation),g.vertexAttribPointer(v.positionLocation,2,g.FLOAT,!1,0,0),g.clearColor(0,0,0,0),g.enable(g.BLEND),g.blendFunc(g.ONE,g.ONE_MINUS_SRC_ALPHA),g.clear(g.COLOR_BUFFER_BIT),g.drawArrays(g.TRIANGLES,0,6),g.flush(),g.deleteTexture(y),g.deleteTexture(_),g.deleteBuffer(b),m},drawFigures:function(e,i,u,c,l){d||function(){var e,i;s(),e=o,o=null,i=a,a=null;var u=n(i,[t(i,h),r(i,f)]);i.useProgram(u);var c={};c.gl=i,c.canvas=e,c.resolutionLocation=i.getUniformLocation(u,"u_resolution"),c.scaleLocation=i.getUniformLocation(u,"u_scale"),c.offsetLocation=i.getUniformLocation(u,"u_offset"),c.positionLocation=i.getAttribLocation(u,"a_position"),c.colorLocation=i.getAttribLocation(u,"a_color"),d=c}();var p=d,v=p.canvas,m=p.gl;v.width=e,v.height=i,m.viewport(0,0,m.drawingBufferWidth,m.drawingBufferHeight),m.uniform2f(p.resolutionLocation,e,i);var g,y,_,b=0;for(g=0,y=c.length;g<y;g++)switch(c[g].type){case"lattice":b+=((_=c[g].coords.length/c[g].verticesPerRow|0)-1)*(c[g].verticesPerRow-1)*6;break;case"triangles":b+=c[g].coords.length}var A=new Float32Array(2*b),S=new Uint8Array(3*b),w=l.coords,k=l.colors,x=0,C=0;for(g=0,y=c.length;g<y;g++){var P=c[g],R=P.coords,T=P.colors;switch(P.type){case"lattice":var E=P.verticesPerRow;_=R.length/E|0;for(var I=1;I<_;I++)for(var O=I*E+1,L=1;L<E;L++,O++)A[x]=w[R[O-E-1]],A[x+1]=w[R[O-E-1]+1],A[x+2]=w[R[O-E]],A[x+3]=w[R[O-E]+1],A[x+4]=w[R[O-1]],A[x+5]=w[R[O-1]+1],S[C]=k[T[O-E-1]],S[C+1]=k[T[O-E-1]+1],S[C+2]=k[T[O-E-1]+2],S[C+3]=k[T[O-E]],S[C+4]=k[T[O-E]+1],S[C+5]=k[T[O-E]+2],S[C+6]=k[T[O-1]],S[C+7]=k[T[O-1]+1],S[C+8]=k[T[O-1]+2],A[x+6]=A[x+2],A[x+7]=A[x+3],A[x+8]=A[x+4],A[x+9]=A[x+5],A[x+10]=w[R[O]],A[x+11]=w[R[O]+1],S[C+9]=S[C+3],S[C+10]=S[C+4],S[C+11]=S[C+5],S[C+12]=S[C+6],S[C+13]=S[C+7],S[C+14]=S[C+8],S[C+15]=k[T[O]],S[C+16]=k[T[O]+1],S[C+17]=k[T[O]+2],x+=12,C+=18;break;case"triangles":for(var F=0,j=R.length;F<j;F++)A[x]=w[R[F]],A[x+1]=w[R[F]+1],S[C]=k[T[F]],S[C+1]=k[T[F]+1],S[C+2]=k[T[F]+2],x+=2,C+=3}}u?m.clearColor(u[0]/255,u[1]/255,u[2]/255,1):m.clearColor(0,0,0,0),m.clear(m.COLOR_BUFFER_BIT);var N=m.createBuffer();m.bindBuffer(m.ARRAY_BUFFER,N),m.bufferData(m.ARRAY_BUFFER,A,m.STATIC_DRAW),m.enableVertexAttribArray(p.positionLocation),m.vertexAttribPointer(p.positionLocation,2,m.FLOAT,!1,0,0);var M=m.createBuffer();return m.bindBuffer(m.ARRAY_BUFFER,M),m.bufferData(m.ARRAY_BUFFER,S,m.STATIC_DRAW),m.enableVertexAttribArray(p.colorLocation),m.vertexAttribPointer(p.colorLocation,3,m.UNSIGNED_BYTE,!1,0,0),m.uniform2f(p.scaleLocation,l.scaleX,l.scaleY),m.uniform2f(p.offsetLocation,l.offsetX,l.offsetY),m.drawArrays(m.TRIANGLES,0,b),m.flush(),m.deleteBuffer(N),m.deleteBuffer(M),v},cleanup:function(){l&&l.canvas&&(l.canvas.width=0,l.canvas.height=0),d&&d.canvas&&(d.canvas.width=0,d.canvas.height=0),l=null,d=null}}}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.renderTextLayer=void 0;var n,i=r(1),a=(n=r(3))&&n.__esModule?n:{default:n};var o=function(){var e=1e5,t=/\S/;var r=["left: ",0,"px; top: ",0,"px; font-size: ",0,"px; font-family: ","",";"];function n(e,n,a){var o,s=document.createElement("span"),u={style:null,angle:0,canvasWidth:0,isWhitespace:!1,originalTransform:null,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,scale:1};if(e._textDivs.push(s),o=n.str,!t.test(o))return u.isWhitespace=!0,void e._textDivProperties.set(s,u);var c=i.Util.transform(e._viewport.transform,n.transform),l=Math.atan2(c[1],c[0]),h=a[n.fontName];h.vertical&&(l+=Math.PI/2);var f,d,p=Math.sqrt(c[2]*c[2]+c[3]*c[3]),v=p;if(h.ascent?v=h.ascent*v:h.descent&&(v=(1+h.descent)*v),0===l?(f=c[4],d=c[5]-v):(f=c[4]+v*Math.sin(l),d=c[5]-v*Math.cos(l)),r[1]=f,r[3]=d,r[5]=p,r[7]=h.fontFamily,u.style=r.join(""),s.setAttribute("style",u.style),s.textContent=n.str,e._fontInspectorEnabled&&(s.dataset.fontName=n.fontName),0!==l&&(u.angle=l*(180/Math.PI)),n.str.length>1&&(h.vertical?u.canvasWidth=n.height*e._viewport.scale:u.canvasWidth=n.width*e._viewport.scale),e._textDivProperties.set(s,u),e._textContentStream&&e._layoutText(s),e._enhanceTextSelection){var m=1,g=0;0!==l&&(m=Math.cos(l),g=Math.sin(l));var y,_,b=(h.vertical?n.height:n.width)*e._viewport.scale,A=p;0!==l?(y=[m,g,-g,m,f,d],_=i.Util.getAxialAlignedBoundingBox([0,0,b,A],y)):_=[f,d,f+b,d+A],e._bounds.push({left:_[0],top:_[1],right:_[2],bottom:_[3],div:s,size:[b,A],m:y})}}function o(t){if(!t._canceled){var r=t._textDivs,n=t._capability,i=r.length;if(i>e)return t._renderingDone=!0,void n.resolve();if(!t._textContentStream)for(var a=0;a<i;a++)t._layoutText(r[a]);t._renderingDone=!0,n.resolve()}}function s(e){for(var t=e._bounds,r=e._viewport,n=function(e,t,r){var n=r.map(function(e,t){return{x1:e.left,y1:e.top,x2:e.right,y2:e.bottom,index:t,x1New:void 0,x2New:void 0}});u(e,n);var i=new Array(r.length);return n.forEach(function(e){var t=e.index;i[t]={left:e.x1New,top:0,right:e.x2New,bottom:0}}),r.map(function(t,r){var a=i[r],o=n[r];o.x1=t.top,o.y1=e-a.right,o.x2=t.bottom,o.y2=e-a.left,o.index=r,o.x1New=void 0,o.x2New=void 0}),u(t,n),n.forEach(function(e){var t=e.index;i[t].top=e.x1New,i[t].bottom=e.x2New}),i}(r.width,r.height,t),a=0;a<n.length;a++){var o=t[a].div,s=e._textDivProperties.get(o);if(0!==s.angle){var c=n[a],l=t[a],h=l.m,f=h[0],d=h[1],p=[[0,0],[0,l.size[1]],[l.size[0],0],l.size],v=new Float64Array(64);p.forEach(function(e,t){var r=i.Util.applyTransform(e,h);v[t+0]=f&&(c.left-r[0])/f,v[t+4]=d&&(c.top-r[1])/d,v[t+8]=f&&(c.right-r[0])/f,v[t+12]=d&&(c.bottom-r[1])/d,v[t+16]=d&&(c.left-r[0])/-d,v[t+20]=f&&(c.top-r[1])/f,v[t+24]=d&&(c.right-r[0])/-d,v[t+28]=f&&(c.bottom-r[1])/f,v[t+32]=f&&(c.left-r[0])/-f,v[t+36]=d&&(c.top-r[1])/-d,v[t+40]=f&&(c.right-r[0])/-f,v[t+44]=d&&(c.bottom-r[1])/-d,v[t+48]=d&&(c.left-r[0])/d,v[t+52]=f&&(c.top-r[1])/-f,v[t+56]=d&&(c.right-r[0])/d,v[t+60]=f&&(c.bottom-r[1])/-f});var m=function(e,t,r){for(var n=0,i=0;i<r;i++){var a=e[t++];a>0&&(n=n?Math.min(a,n):a)}return n},g=1+Math.min(Math.abs(f),Math.abs(d));s.paddingLeft=m(v,32,16)/g,s.paddingTop=m(v,48,16)/g,s.paddingRight=m(v,0,16)/g,s.paddingBottom=m(v,16,16)/g,e._textDivProperties.set(o,s)}else s.paddingLeft=t[a].left-n[a].left,s.paddingTop=t[a].top-n[a].top,s.paddingRight=n[a].right-t[a].right,s.paddingBottom=n[a].bottom-t[a].bottom,e._textDivProperties.set(o,s)}}function u(e,t){t.sort(function(e,t){return e.x1-t.x1||e.index-t.index});var r=[{start:-1/0,end:1/0,boundary:{x1:-1/0,y1:-1/0,x2:0,y2:1/0,index:-1,x1New:0,x2New:0}}];t.forEach(function(e){for(var t=0;t<r.length&&r[t].end<=e.y1;)t++;for(var n,i,a=r.length-1;a>=0&&r[a].start>=e.y2;)a--;var o,s,u=-1/0;for(o=t;o<=a;o++){var c;(c=(i=(n=r[o]).boundary).x2>e.x1?i.index>e.index?i.x1New:e.x1:void 0===i.x2New?(i.x2+e.x1)/2:i.x2New)>u&&(u=c)}for(e.x1New=u,o=t;o<=a;o++)void 0===(i=(n=r[o]).boundary).x2New?i.x2>e.x1?i.index>e.index&&(i.x2New=i.x2):i.x2New=u:i.x2New>u&&(i.x2New=Math.max(u,i.x2));var l=[],h=null;for(o=t;o<=a;o++){var f=(i=(n=r[o]).boundary).x2>e.x2?i:e;h===f?l[l.length-1].end=n.end:(l.push({start:n.start,end:n.end,boundary:f}),h=f)}for(r[t].start<e.y1&&(l[0].start=e.y1,l.unshift({start:r[t].start,end:e.y1,boundary:r[t].boundary})),e.y2<r[a].end&&(l[l.length-1].end=e.y2,l.push({start:e.y2,end:r[a].end,boundary:r[a].boundary})),o=t;o<=a;o++)if(void 0===(i=(n=r[o]).boundary).x2New){var d=!1;for(s=t-1;!d&&s>=0&&r[s].start>=i.y1;s--)d=r[s].boundary===i;for(s=a+1;!d&&s<r.length&&r[s].end<=i.y2;s++)d=r[s].boundary===i;for(s=0;!d&&s<l.length;s++)d=l[s].boundary===i;d||(i.x2New=u)}Array.prototype.splice.apply(r,[t,a-t+1].concat(l))}),r.forEach(function(t){var r=t.boundary;void 0===r.x2New&&(r.x2New=Math.max(e,r.x2))})}function c(e){var t=this,r=e.textContent,n=e.textContentStream,o=e.container,s=e.viewport,u=e.textDivs,c=e.textContentItemsStr,l=e.enhanceTextSelection;this._textContent=r,this._textContentStream=n,this._container=o,this._viewport=s,this._textDivs=u||[],this._textContentItemsStr=c||[],this._enhanceTextSelection=!!l,this._fontInspectorEnabled=!(!a.default.FontInspector||!a.default.FontInspector.enabled),this._reader=null,this._layoutTextLastFontSize=null,this._layoutTextLastFontFamily=null,this._layoutTextCtx=null,this._textDivProperties=new WeakMap,this._renderingDone=!1,this._canceled=!1,this._capability=(0,i.createPromiseCapability)(),this._renderTimer=null,this._bounds=[],this._capability.promise.finally(function(){t._layoutTextCtx&&(t._layoutTextCtx.canvas.width=0,t._layoutTextCtx.canvas.height=0,t._layoutTextCtx=null)})}return c.prototype={get promise(){return this._capability.promise},cancel:function(){this._canceled=!0,this._reader&&(this._reader.cancel(new i.AbortException("TextLayer task cancelled.")),this._reader=null),null!==this._renderTimer&&(clearTimeout(this._renderTimer),this._renderTimer=null),this._capability.reject(new Error("TextLayer task cancelled."))},_processItems:function(e,t){for(var r=0,i=e.length;r<i;r++)this._textContentItemsStr.push(e[r].str),n(this,e[r],t)},_layoutText:function(e){var t=this._container,r=this._textDivProperties.get(e);if(!r.isWhitespace){var n=e.style.fontSize,i=e.style.fontFamily;n===this._layoutTextLastFontSize&&i===this._layoutTextLastFontFamily||(this._layoutTextCtx.font=n+" "+i,this._layoutTextLastFontSize=n,this._layoutTextLastFontFamily=i);var a=this._layoutTextCtx.measureText(e.textContent).width,o="";0!==r.canvasWidth&&a>0&&(r.scale=r.canvasWidth/a,o="scaleX(".concat(r.scale,")")),0!==r.angle&&(o="rotate(".concat(r.angle,"deg) ").concat(o)),o.length>0&&(r.originalTransform=o,e.style.transform=o),this._textDivProperties.set(e,r),t.appendChild(e)}},_render:function(e){var t=this,r=(0,i.createPromiseCapability)(),n=Object.create(null),a=document.createElement("canvas");if(a.mozOpaque=!0,this._layoutTextCtx=a.getContext("2d",{alpha:!1}),this._textContent){var s=this._textContent.items,u=this._textContent.styles;this._processItems(s,u),r.resolve()}else{if(!this._textContentStream)throw new Error('Neither "textContent" nor "textContentStream" parameters specified.');this._reader=this._textContentStream.getReader(),function e(){t._reader.read().then(function(i){var a=i.value;i.done?r.resolve():(Object.assign(n,a.styles),t._processItems(a.items,n),e())},r.reject)}()}r.promise.then(function(){n=null,e?t._renderTimer=setTimeout(function(){o(t),t._renderTimer=null},e):o(t)},this._capability.reject)},expandTextDivs:function(e){if(this._enhanceTextSelection&&this._renderingDone){null!==this._bounds&&(s(this),this._bounds=null);for(var t=0,r=this._textDivs.length;t<r;t++){var n=this._textDivs[t],i=this._textDivProperties.get(n);if(!i.isWhitespace)if(e){var a="",o="";1!==i.scale&&(a="scaleX("+i.scale+")"),0!==i.angle&&(a="rotate("+i.angle+"deg) "+a),0!==i.paddingLeft&&(o+=" padding-left: "+i.paddingLeft/i.scale+"px;",a+=" translateX("+-i.paddingLeft/i.scale+"px)"),0!==i.paddingTop&&(o+=" padding-top: "+i.paddingTop+"px;",a+=" translateY("+-i.paddingTop+"px)"),0!==i.paddingRight&&(o+=" padding-right: "+i.paddingRight/i.scale+"px;"),0!==i.paddingBottom&&(o+=" padding-bottom: "+i.paddingBottom+"px;"),""!==o&&n.setAttribute("style",i.style+o),""!==a&&(n.style.transform=a)}else n.style.padding=0,n.style.transform=i.originalTransform||""}}}},function(e){var t=new c({textContent:e.textContent,textContentStream:e.textContentStream,container:e.container,viewport:e.viewport,textDivs:e.textDivs,textContentItemsStr:e.textContentItemsStr,enhanceTextSelection:e.enhanceTextSelection});return t._render(e.timeout),t}}();t.renderTextLayer=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationLayer=void 0;var n=r(151),i=r(1);function a(e,t,r){return(a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=c(e)););return e}(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}})(e,t,r||e)}function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t){return!t||"object"!==o(t)&&"function"!=typeof t?u(e):t}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function c(e){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&h(e,t)}function h(e,t){return(h=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function p(e,t,r){return t&&d(e.prototype,t),r&&d(e,r),e}var v=function(){function e(){f(this,e)}return p(e,null,[{key:"create",value:function(e){switch(e.data.annotationType){case i.AnnotationType.LINK:return new g(e);case i.AnnotationType.TEXT:return new y(e);case i.AnnotationType.WIDGET:switch(e.data.fieldType){case"Tx":return new b(e);case"Btn":return e.data.radioButton?new S(e):e.data.checkBox?new A(e):new w(e);case"Ch":return new k(e)}return new _(e);case i.AnnotationType.POPUP:return new x(e);case i.AnnotationType.FREETEXT:return new P(e);case i.AnnotationType.LINE:return new R(e);case i.AnnotationType.SQUARE:return new T(e);case i.AnnotationType.CIRCLE:return new E(e);case i.AnnotationType.POLYLINE:return new I(e);case i.AnnotationType.CARET:return new L(e);case i.AnnotationType.INK:return new F(e);case i.AnnotationType.POLYGON:return new O(e);case i.AnnotationType.HIGHLIGHT:return new j(e);case i.AnnotationType.UNDERLINE:return new N(e);case i.AnnotationType.SQUIGGLY:return new M(e);case i.AnnotationType.STRIKEOUT:return new D(e);case i.AnnotationType.STAMP:return new q(e);case i.AnnotationType.FILEATTACHMENT:return new U(e);default:return new m(e)}}}]),e}(),m=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];f(this,e),this.isRenderable=r,this.data=t.data,this.layer=t.layer,this.page=t.page,this.viewport=t.viewport,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderInteractiveForms=t.renderInteractiveForms,this.svgFactory=t.svgFactory,r&&(this.container=this._createContainer(n))}return p(e,[{key:"_createContainer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.data,r=this.page,n=this.viewport,a=document.createElement("section"),o=t.rect[2]-t.rect[0],s=t.rect[3]-t.rect[1];a.setAttribute("data-annotation-id",t.id);var u=i.Util.normalizeRect([t.rect[0],r.view[3]-t.rect[1]+r.view[1],t.rect[2],r.view[3]-t.rect[3]+r.view[1]]);if(a.style.transform="matrix("+n.transform.join(",")+")",a.style.transformOrigin=-u[0]+"px "+-u[1]+"px",!e&&t.borderStyle.width>0){a.style.borderWidth=t.borderStyle.width+"px",t.borderStyle.style!==i.AnnotationBorderStyleType.UNDERLINE&&(o-=2*t.borderStyle.width,s-=2*t.borderStyle.width);var c=t.borderStyle.horizontalCornerRadius,l=t.borderStyle.verticalCornerRadius;if(c>0||l>0){var h=c+"px / "+l+"px";a.style.borderRadius=h}switch(t.borderStyle.style){case i.AnnotationBorderStyleType.SOLID:a.style.borderStyle="solid";break;case i.AnnotationBorderStyleType.DASHED:a.style.borderStyle="dashed";break;case i.AnnotationBorderStyleType.BEVELED:(0,i.warn)("Unimplemented border style: beveled");break;case i.AnnotationBorderStyleType.INSET:(0,i.warn)("Unimplemented border style: inset");break;case i.AnnotationBorderStyleType.UNDERLINE:a.style.borderBottomStyle="solid"}t.color?a.style.borderColor=i.Util.makeCssRgb(0|t.color[0],0|t.color[1],0|t.color[2]):a.style.borderWidth=0}return a.style.left=u[0]+"px",a.style.top=u[1]+"px",a.style.width=o+"px",a.style.height=s+"px",a}},{key:"_createPopup",value:function(e,t,r){t||((t=document.createElement("div")).style.height=e.style.height,t.style.width=e.style.width,e.appendChild(t));var n=new C({container:e,trigger:t,color:r.color,title:r.title,modificationDate:r.modificationDate,contents:r.contents,hideWrapper:!0}).render();n.style.left=e.style.width,e.appendChild(n)}},{key:"render",value:function(){(0,i.unreachable)("Abstract method `AnnotationElement.render` called")}}]),e}(),g=function(e){function t(e){f(this,t);var r=!!(e.data.url||e.data.dest||e.data.action);return s(this,c(t).call(this,e,r))}return l(t,m),p(t,[{key:"render",value:function(){this.container.className="linkAnnotation";var e=this.data,t=this.linkService,r=document.createElement("a");return(0,n.addLinkAttributes)(r,{url:e.url,target:e.newWindow?n.LinkTarget.BLANK:t.externalLinkTarget,rel:t.externalLinkRel}),e.url||(e.action?this._bindNamedAction(r,e.action):this._bindLink(r,e.dest)),this.container.appendChild(r),this.container}},{key:"_bindLink",value:function(e,t){var r=this;e.href=this.linkService.getDestinationHash(t),e.onclick=function(){return t&&r.linkService.navigateTo(t),!1},t&&(e.className="internalLink")}},{key:"_bindNamedAction",value:function(e,t){var r=this;e.href=this.linkService.getAnchorUrl(""),e.onclick=function(){return r.linkService.executeNamedAction(t),!1},e.className="internalLink"}}]),t}(),y=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r))}return l(t,m),p(t,[{key:"render",value:function(){this.container.className="textAnnotation";var e=document.createElement("img");return e.style.height=this.container.style.height,e.style.width=this.container.style.width,e.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",e.alt="[{{type}} Annotation]",e.dataset.l10nId="text_annotation_type",e.dataset.l10nArgs=JSON.stringify({type:this.data.name}),this.data.hasPopup||this._createPopup(this.container,e,this.data),this.container.appendChild(e),this.container}}]),t}(),_=function(e){function t(){return f(this,t),s(this,c(t).apply(this,arguments))}return l(t,m),p(t,[{key:"render",value:function(){return this.container}}]),t}(),b=function(e){function t(e){f(this,t);var r=e.renderInteractiveForms||!e.data.hasAppearance&&!!e.data.fieldValue;return s(this,c(t).call(this,e,r))}return l(t,_),p(t,[{key:"render",value:function(){this.container.className="textWidgetAnnotation";var e=null;if(this.renderInteractiveForms){if(this.data.multiLine?(e=document.createElement("textarea")).textContent=this.data.fieldValue:((e=document.createElement("input")).type="text",e.setAttribute("value",this.data.fieldValue)),e.disabled=this.data.readOnly,null!==this.data.maxLen&&(e.maxLength=this.data.maxLen),this.data.comb){var t=(this.data.rect[2]-this.data.rect[0])/this.data.maxLen;e.classList.add("comb"),e.style.letterSpacing="calc("+t+"px - 1ch)"}}else{(e=document.createElement("div")).textContent=this.data.fieldValue,e.style.verticalAlign="middle",e.style.display="table-cell";var r=null;this.data.fontRefName&&this.page.commonObjs.has(this.data.fontRefName)&&(r=this.page.commonObjs.get(this.data.fontRefName)),this._setTextStyle(e,r)}return null!==this.data.textAlignment&&(e.style.textAlign=["left","center","right"][this.data.textAlignment]),this.container.appendChild(e),this.container}},{key:"_setTextStyle",value:function(e,t){var r=e.style;if(r.fontSize=this.data.fontSize+"px",r.direction=this.data.fontDirection<0?"rtl":"ltr",t){r.fontWeight=t.black?t.bold?"900":"bold":t.bold?"bold":"normal",r.fontStyle=t.italic?"italic":"normal";var n=t.loadedName?'"'+t.loadedName+'", ':"",i=t.fallbackName||"Helvetica, sans-serif";r.fontFamily=n+i}}}]),t}(),A=function(e){function t(e){return f(this,t),s(this,c(t).call(this,e,e.renderInteractiveForms))}return l(t,_),p(t,[{key:"render",value:function(){this.container.className="buttonWidgetAnnotation checkBox";var e=document.createElement("input");return e.disabled=this.data.readOnly,e.type="checkbox",this.data.fieldValue&&"Off"!==this.data.fieldValue&&e.setAttribute("checked",!0),this.container.appendChild(e),this.container}}]),t}(),S=function(e){function t(e){return f(this,t),s(this,c(t).call(this,e,e.renderInteractiveForms))}return l(t,_),p(t,[{key:"render",value:function(){this.container.className="buttonWidgetAnnotation radioButton";var e=document.createElement("input");return e.disabled=this.data.readOnly,e.type="radio",e.name=this.data.fieldName,this.data.fieldValue===this.data.buttonValue&&e.setAttribute("checked",!0),this.container.appendChild(e),this.container}}]),t}(),w=function(e){function t(){return f(this,t),s(this,c(t).apply(this,arguments))}return l(t,g),p(t,[{key:"render",value:function(){var e=a(c(t.prototype),"render",this).call(this);return e.className="buttonWidgetAnnotation pushButton",e}}]),t}(),k=function(e){function t(e){return f(this,t),s(this,c(t).call(this,e,e.renderInteractiveForms))}return l(t,_),p(t,[{key:"render",value:function(){this.container.className="choiceWidgetAnnotation";var e=document.createElement("select");e.disabled=this.data.readOnly,this.data.combo||(e.size=this.data.options.length,this.data.multiSelect&&(e.multiple=!0));for(var t=0,r=this.data.options.length;t<r;t++){var n=this.data.options[t],i=document.createElement("option");i.textContent=n.displayValue,i.value=n.exportValue,this.data.fieldValue.includes(n.displayValue)&&i.setAttribute("selected",!0),e.appendChild(i)}return this.container.appendChild(e),this.container}}]),t}(),x=function(e){function t(e){f(this,t);var r=!(!e.data.title&&!e.data.contents);return s(this,c(t).call(this,e,r))}return l(t,m),p(t,[{key:"render",value:function(){if(this.container.className="popupAnnotation",["Line","Square","Circle","PolyLine","Polygon","Ink"].includes(this.data.parentType))return this.container;var e='[data-annotation-id="'+this.data.parentId+'"]',t=this.layer.querySelector(e);if(!t)return this.container;var r=new C({container:this.container,trigger:t,color:this.data.color,title:this.data.title,modificationDate:this.data.modificationDate,contents:this.data.contents}),n=parseFloat(t.style.left),i=parseFloat(t.style.width);return this.container.style.transformOrigin=-(n+i)+"px -"+t.style.top,this.container.style.left=n+i+"px",this.container.appendChild(r.render()),this.container}}]),t}(),C=function(){function e(t){f(this,e),this.container=t.container,this.trigger=t.trigger,this.color=t.color,this.title=t.title,this.modificationDate=t.modificationDate,this.contents=t.contents,this.hideWrapper=t.hideWrapper||!1,this.pinned=!1}return p(e,[{key:"render",value:function(){var e=document.createElement("div");e.className="popupWrapper",this.hideElement=this.hideWrapper?e:this.container,this.hideElement.setAttribute("hidden",!0);var t=document.createElement("div");t.className="popup";var r=this.color;if(r){var a=.7*(255-r[0])+r[0],o=.7*(255-r[1])+r[1],s=.7*(255-r[2])+r[2];t.style.backgroundColor=i.Util.makeCssRgb(0|a,0|o,0|s)}var u=document.createElement("h1");u.textContent=this.title,t.appendChild(u);var c=n.PDFDateString.toDateObject(this.modificationDate);if(c){var l=document.createElement("span");l.textContent="{{date}}, {{time}}",l.dataset.l10nId="annotation_date_string",l.dataset.l10nArgs=JSON.stringify({date:c.toLocaleDateString(),time:c.toLocaleTimeString()}),t.appendChild(l)}var h=this._formatContents(this.contents);return t.appendChild(h),this.trigger.addEventListener("click",this._toggle.bind(this)),this.trigger.addEventListener("mouseover",this._show.bind(this,!1)),this.trigger.addEventListener("mouseout",this._hide.bind(this,!1)),t.addEventListener("click",this._hide.bind(this,!0)),e.appendChild(t),e}},{key:"_formatContents",value:function(e){for(var t=document.createElement("p"),r=e.split(/(?:\r\n?|\n)/),n=0,i=r.length;n<i;++n){var a=r[n];t.appendChild(document.createTextNode(a)),n<i-1&&t.appendChild(document.createElement("br"))}return t}},{key:"_toggle",value:function(){this.pinned?this._hide(!0):this._show(!0)}},{key:"_show",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&(this.pinned=!0),this.hideElement.hasAttribute("hidden")&&(this.hideElement.removeAttribute("hidden"),this.container.style.zIndex+=1)}},{key:"_hide",value:function(){(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&(this.pinned=!1),this.hideElement.hasAttribute("hidden")||this.pinned||(this.hideElement.setAttribute("hidden",!0),this.container.style.zIndex-=1)}}]),e}(),P=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,m),p(t,[{key:"render",value:function(){return this.container.className="freeTextAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(),R=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,m),p(t,[{key:"render",value:function(){this.container.className="lineAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=this.svgFactory.createElement("svg:line");return i.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),i.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),i.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),i.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),i.setAttribute("stroke-width",e.borderStyle.width),i.setAttribute("stroke","transparent"),n.appendChild(i),this.container.append(n),this._createPopup(this.container,i,e),this.container}}]),t}(),T=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,m),p(t,[{key:"render",value:function(){this.container.className="squareAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.borderStyle.width,a=this.svgFactory.createElement("svg:rect");return a.setAttribute("x",i/2),a.setAttribute("y",i/2),a.setAttribute("width",t-i),a.setAttribute("height",r-i),a.setAttribute("stroke-width",i),a.setAttribute("stroke","transparent"),a.setAttribute("fill","none"),n.appendChild(a),this.container.append(n),this._createPopup(this.container,a,e),this.container}}]),t}(),E=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,m),p(t,[{key:"render",value:function(){this.container.className="circleAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.borderStyle.width,a=this.svgFactory.createElement("svg:ellipse");return a.setAttribute("cx",t/2),a.setAttribute("cy",r/2),a.setAttribute("rx",t/2-i/2),a.setAttribute("ry",r/2-i/2),a.setAttribute("stroke-width",i),a.setAttribute("stroke","transparent"),a.setAttribute("fill","none"),n.appendChild(a),this.container.append(n),this._createPopup(this.container,a,e),this.container}}]),t}(),I=function(e){function t(e){var r;f(this,t);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return(r=s(this,c(t).call(this,e,n,!0))).containerClassName="polylineAnnotation",r.svgElementName="svg:polyline",r}return l(t,m),p(t,[{key:"render",value:function(){this.container.className=this.containerClassName;for(var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.vertices,a=[],o=0,s=i.length;o<s;o++){var u=i[o].x-e.rect[0],c=e.rect[3]-i[o].y;a.push(u+","+c)}a=a.join(" ");var l=e.borderStyle.width,h=this.svgFactory.createElement(this.svgElementName);return h.setAttribute("points",a),h.setAttribute("stroke-width",l),h.setAttribute("stroke","transparent"),h.setAttribute("fill","none"),n.appendChild(h),this.container.append(n),this._createPopup(this.container,h,e),this.container}}]),t}(),O=function(e){function t(e){var r;return f(this,t),(r=s(this,c(t).call(this,e))).containerClassName="polygonAnnotation",r.svgElementName="svg:polygon",r}return l(t,I),t}(),L=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,m),p(t,[{key:"render",value:function(){return this.container.className="caretAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(),F=function(e){function t(e){var r;f(this,t);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return(r=s(this,c(t).call(this,e,n,!0))).containerClassName="inkAnnotation",r.svgElementName="svg:polyline",r}return l(t,m),p(t,[{key:"render",value:function(){this.container.className=this.containerClassName;for(var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.inkLists,a=0,o=i.length;a<o;a++){for(var s=i[a],u=[],c=0,l=s.length;c<l;c++){var h=s[c].x-e.rect[0],f=e.rect[3]-s[c].y;u.push(h+","+f)}u=u.join(" ");var d=e.borderStyle.width,p=this.svgFactory.createElement(this.svgElementName);p.setAttribute("points",u),p.setAttribute("stroke-width",d),p.setAttribute("stroke","transparent"),p.setAttribute("fill","none"),this._createPopup(this.container,p,e),n.appendChild(p)}return this.container.append(n),this.container}}]),t}(),j=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,m),p(t,[{key:"render",value:function(){return this.container.className="highlightAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(),N=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,m),p(t,[{key:"render",value:function(){return this.container.className="underlineAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(),M=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,m),p(t,[{key:"render",value:function(){return this.container.className="squigglyAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(),D=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,m),p(t,[{key:"render",value:function(){return this.container.className="strikeoutAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(),q=function(e){function t(e){f(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,m),p(t,[{key:"render",value:function(){return this.container.className="stampAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(),U=function(e){function t(e){var r;f(this,t);var a=(r=s(this,c(t).call(this,e,!0))).data.file,o=a.filename,l=a.content;return r.filename=(0,n.getFilenameFromUrl)(o),r.content=l,r.linkService.eventBus&&r.linkService.eventBus.dispatch("fileattachmentannotation",{source:u(r),id:(0,i.stringToPDFString)(o),filename:o,content:l}),r}return l(t,m),p(t,[{key:"render",value:function(){this.container.className="fileAttachmentAnnotation";var e=document.createElement("div");return e.style.height=this.container.style.height,e.style.width=this.container.style.width,e.addEventListener("dblclick",this._download.bind(this)),this.data.hasPopup||!this.data.title&&!this.data.contents||this._createPopup(this.container,e,this.data),this.container.appendChild(e),this.container}},{key:"_download",value:function(){this.downloadManager?this.downloadManager.downloadData(this.content,this.filename,""):(0,i.warn)("Download cannot be started due to unavailable download manager")}}]),t}(),W=function(){function e(){f(this,e)}return p(e,null,[{key:"render",value:function(e){for(var t=0,r=e.annotations.length;t<r;t++){var i=e.annotations[t];if(i){var a=v.create({data:i,layer:e.div,page:e.page,viewport:e.viewport,linkService:e.linkService,downloadManager:e.downloadManager,imageResourcesPath:e.imageResourcesPath||"",renderInteractiveForms:e.renderInteractiveForms||!1,svgFactory:new n.DOMSVGFactory});a.isRenderable&&e.div.appendChild(a.render())}}}},{key:"update",value:function(e){for(var t=0,r=e.annotations.length;t<r;t++){var n=e.annotations[t],i=e.div.querySelector('[data-annotation-id="'+n.id+'"]');i&&(i.style.transform="matrix("+e.viewport.transform.join(",")+")")}e.div.removeAttribute("hidden")}}]),e}();t.AnnotationLayer=W},function(e,n,a){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.SVGGraphics=void 0;var o,s=a(1),u=a(151),c=(o=a(4))&&o.__esModule?o:{default:o};function l(e){return function(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function p(e,t,r){return t&&d(e.prototype,t),r&&d(e,r),e}var v=function(){throw new Error("Not implemented: SVGGraphics")};n.SVGGraphics=v;var m=function(e){if(Number.isInteger(e))return e.toString();var t=e.toFixed(10),r=t.length-1;if("0"!==t[r])return t;do{r--}while("0"===t[r]);return t.substring(0,"."===t[r]?r:r+1)},g=function(e){if(0===e[4]&&0===e[5]){if(0===e[1]&&0===e[2])return 1===e[0]&&1===e[3]?"":"scale(".concat(m(e[0])," ").concat(m(e[3]),")");if(e[0]===e[3]&&e[1]===-e[2]){var t=180*Math.acos(e[0])/Math.PI;return"rotate(".concat(m(t),")")}}else if(1===e[0]&&0===e[1]&&0===e[2]&&1===e[3])return"translate(".concat(m(e[4])," ").concat(m(e[5]),")");return"matrix(".concat(m(e[0])," ").concat(m(e[1])," ").concat(m(e[2])," ").concat(m(e[3])," ").concat(m(e[4])," ")+"".concat(m(e[5]),")")},y={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},_="http://www.w3.org/1999/xlink",b=["butt","round","square"],A=["miter","round","bevel"],S=function(){for(var e=new Uint8Array([137,80,78,71,13,10,26,10]),n=12,a=new Int32Array(256),o=0;o<256;o++){for(var u=o,l=0;l<8;l++)u=1&u?3988292384^u>>1&**********:u>>1&**********;a[o]=u}function h(e,t,r,n){var i=n,o=t.length;r[i]=o>>24&255,r[i+1]=o>>16&255,r[i+2]=o>>8&255,r[i+3]=255&o,r[i+=4]=255&e.charCodeAt(0),r[i+1]=255&e.charCodeAt(1),r[i+2]=255&e.charCodeAt(2),r[i+3]=255&e.charCodeAt(3),i+=4,r.set(t,i);var s=function(e,t,r){for(var n=-1,i=t;i<r;i++){var o=255&(n^e[i]);n=n>>>8^a[o]}return-1^n}(r,n+4,i+=t.length);r[i]=s>>24&255,r[i+1]=s>>16&255,r[i+2]=s>>8&255,r[i+3]=255&s}function f(e){var t=e.length,r=Math.ceil(t/65535),n=new Uint8Array(2+t+5*r+4),i=0;n[i++]=120,n[i++]=156;for(var a=0;t>65535;)n[i++]=0,n[i++]=255,n[i++]=255,n[i++]=0,n[i++]=0,n.set(e.subarray(a,a+65535),i),i+=65535,a+=65535,t-=65535;n[i++]=1,n[i++]=255&t,n[i++]=t>>8&255,n[i++]=255&~t,n[i++]=(65535&~t)>>8&255,n.set(e.subarray(a),i),i+=e.length-a;var o=function(e,t,r){for(var n=1,i=0,a=t;a<r;++a)i=(i+(n=(n+(255&e[a]))%65521))%65521;return i<<16|n}(e,0,e.length);return n[i++]=o>>24&255,n[i++]=o>>16&255,n[i++]=o>>8&255,n[i++]=255&o,n}function d(a,o,u,l){var d,p,v,m=a.width,g=a.height,y=a.data;switch(o){case s.ImageKind.GRAYSCALE_1BPP:p=0,d=1,v=m+7>>3;break;case s.ImageKind.RGB_24BPP:p=2,d=8,v=3*m;break;case s.ImageKind.RGBA_32BPP:p=6,d=8,v=4*m;break;default:throw new Error("invalid format")}for(var _=new Uint8Array((1+v)*g),b=0,A=0,S=0;S<g;++S)_[b++]=0,_.set(y.subarray(A,A+v),b),A+=v,b+=v;if(o===s.ImageKind.GRAYSCALE_1BPP&&l){b=0;for(var w=0;w<g;w++){b++;for(var k=0;k<v;k++)_[b++]^=255}}var x=new Uint8Array([m>>24&255,m>>16&255,m>>8&255,255&m,g>>24&255,g>>16&255,g>>8&255,255&g,d,p,0,0,0]),C=function(e){if(!(0,c.default)())return f(e);try{var n;n=parseInt(i.versions.node)>=8?e:new t(e);var a=r(2).deflateSync(n,{level:9});return a instanceof Uint8Array?a:new Uint8Array(a)}catch(e){(0,s.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+e)}return f(e)}(_),P=e.length+3*n+x.length+C.length,R=new Uint8Array(P),T=0;return R.set(e,T),h("IHDR",x,R,T+=e.length),h("IDATA",C,R,T+=n+x.length),T+=n+C.length,h("IEND",new Uint8Array(0),R,T),(0,s.createObjectURL)(R,"image/png",u)}return function(e,t,r){return d(e,void 0===e.kind?s.ImageKind.GRAYSCALE_1BPP:e.kind,t,r)}}(),w=function(){function e(){f(this,e),this.fontSizeScale=1,this.fontWeight=y.fontWeight,this.fontSize=0,this.textMatrix=s.IDENTITY_MATRIX,this.fontMatrix=s.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=s.TextRenderingMode.FILL,this.textMatrixScale=1,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=y.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}return p(e,[{key:"clone",value:function(){return Object.create(this)}},{key:"setCurrentPoint",value:function(e,t){this.x=e,this.y=t}}]),e}(),k=0,x=0,C=0;n.SVGGraphics=v=function(){function e(t,r,n){for(var i in f(this,e),this.svgFactory=new u.DOMSVGFactory,this.current=new w,this.transformMatrix=s.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=t,this.objs=r,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!n,this._operatorIdMapping=[],s.OPS)this._operatorIdMapping[s.OPS[i]]=i}return p(e,[{key:"save",value:function(){this.transformStack.push(this.transformMatrix);var e=this.current;this.extraStack.push(e),this.current=e.clone()}},{key:"restore",value:function(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null}},{key:"group",value:function(e){this.save(),this.executeOpTree(e),this.restore()}},{key:"loadDependencies",value:function(e){for(var t=this,r=e.fnArray,n=e.argsArray,i=0,a=r.length;i<a;i++)if(r[i]===s.OPS.dependency){var o=!0,u=!1,c=void 0;try{for(var l,h=function(){var e=l.value,r=e.startsWith("g_")?t.commonObjs:t.objs,n=new Promise(function(t){r.get(e,t)});t.current.dependencies.push(n)},f=n[i][Symbol.iterator]();!(o=(l=f.next()).done);o=!0)h()}catch(e){u=!0,c=e}finally{try{o||null==f.return||f.return()}finally{if(u)throw c}}}return Promise.all(this.current.dependencies)}},{key:"transform",value:function(e,t,r,n,i,a){var o=[e,t,r,n,i,a];this.transformMatrix=s.Util.transform(this.transformMatrix,o),this.tgrp=null}},{key:"getSVG",value:function(e,t){var r=this;this.viewport=t;var n=this._initialize(t);return this.loadDependencies(e).then(function(){return r.transformMatrix=s.IDENTITY_MATRIX,r.executeOpTree(r.convertOpList(e)),n})}},{key:"convertOpList",value:function(e){for(var t=this._operatorIdMapping,r=e.argsArray,n=e.fnArray,i=[],a=0,o=n.length;a<o;a++){var s=n[a];i.push({fnId:s,fn:t[s],args:r[a]})}return function(e){var t=[],r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done);n=!0){var u=o.value;"save"!==u.fn?"restore"===u.fn?t=r.pop():t.push(u):(t.push({fnId:92,fn:"group",items:[]}),r.push(t),t=t[t.length-1].items)}}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return t}(i)}},{key:"executeOpTree",value:function(e){var t=!0,r=!1,n=void 0;try{for(var i,a=e[Symbol.iterator]();!(t=(i=a.next()).done);t=!0){var o=i.value,u=o.fn,c=o.fnId,l=o.args;switch(0|c){case s.OPS.beginText:this.beginText();break;case s.OPS.dependency:break;case s.OPS.setLeading:this.setLeading(l);break;case s.OPS.setLeadingMoveText:this.setLeadingMoveText(l[0],l[1]);break;case s.OPS.setFont:this.setFont(l);break;case s.OPS.showText:case s.OPS.showSpacedText:this.showText(l[0]);break;case s.OPS.endText:this.endText();break;case s.OPS.moveText:this.moveText(l[0],l[1]);break;case s.OPS.setCharSpacing:this.setCharSpacing(l[0]);break;case s.OPS.setWordSpacing:this.setWordSpacing(l[0]);break;case s.OPS.setHScale:this.setHScale(l[0]);break;case s.OPS.setTextMatrix:this.setTextMatrix(l[0],l[1],l[2],l[3],l[4],l[5]);break;case s.OPS.setTextRise:this.setTextRise(l[0]);break;case s.OPS.setTextRenderingMode:this.setTextRenderingMode(l[0]);break;case s.OPS.setLineWidth:this.setLineWidth(l[0]);break;case s.OPS.setLineJoin:this.setLineJoin(l[0]);break;case s.OPS.setLineCap:this.setLineCap(l[0]);break;case s.OPS.setMiterLimit:this.setMiterLimit(l[0]);break;case s.OPS.setFillRGBColor:this.setFillRGBColor(l[0],l[1],l[2]);break;case s.OPS.setStrokeRGBColor:this.setStrokeRGBColor(l[0],l[1],l[2]);break;case s.OPS.setStrokeColorN:this.setStrokeColorN(l);break;case s.OPS.setFillColorN:this.setFillColorN(l);break;case s.OPS.shadingFill:this.shadingFill(l[0]);break;case s.OPS.setDash:this.setDash(l[0],l[1]);break;case s.OPS.setRenderingIntent:this.setRenderingIntent(l[0]);break;case s.OPS.setFlatness:this.setFlatness(l[0]);break;case s.OPS.setGState:this.setGState(l[0]);break;case s.OPS.fill:this.fill();break;case s.OPS.eoFill:this.eoFill();break;case s.OPS.stroke:this.stroke();break;case s.OPS.fillStroke:this.fillStroke();break;case s.OPS.eoFillStroke:this.eoFillStroke();break;case s.OPS.clip:this.clip("nonzero");break;case s.OPS.eoClip:this.clip("evenodd");break;case s.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case s.OPS.paintJpegXObject:this.paintJpegXObject(l[0],l[1],l[2]);break;case s.OPS.paintImageXObject:this.paintImageXObject(l[0]);break;case s.OPS.paintInlineImageXObject:this.paintInlineImageXObject(l[0]);break;case s.OPS.paintImageMaskXObject:this.paintImageMaskXObject(l[0]);break;case s.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(l[0],l[1]);break;case s.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case s.OPS.closePath:this.closePath();break;case s.OPS.closeStroke:this.closeStroke();break;case s.OPS.closeFillStroke:this.closeFillStroke();break;case s.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case s.OPS.nextLine:this.nextLine();break;case s.OPS.transform:this.transform(l[0],l[1],l[2],l[3],l[4],l[5]);break;case s.OPS.constructPath:this.constructPath(l[0],l[1]);break;case s.OPS.endPath:this.endPath();break;case 92:this.group(o.items);break;default:(0,s.warn)("Unimplemented operator ".concat(u))}}}catch(e){r=!0,n=e}finally{try{t||null==a.return||a.return()}finally{if(r)throw n}}}},{key:"setWordSpacing",value:function(e){this.current.wordSpacing=e}},{key:"setCharSpacing",value:function(e){this.current.charSpacing=e}},{key:"nextLine",value:function(){this.moveText(0,this.current.leading)}},{key:"setTextMatrix",value:function(e,t,r,n,i,a){var o=this.current;o.textMatrix=o.lineMatrix=[e,t,r,n,i,a],o.textMatrixScale=Math.sqrt(e*e+t*t),o.x=o.lineX=0,o.y=o.lineY=0,o.xcoords=[],o.tspan=this.svgFactory.createElement("svg:tspan"),o.tspan.setAttributeNS(null,"font-family",o.fontFamily),o.tspan.setAttributeNS(null,"font-size","".concat(m(o.fontSize),"px")),o.tspan.setAttributeNS(null,"y",m(-o.y)),o.txtElement=this.svgFactory.createElement("svg:text"),o.txtElement.appendChild(o.tspan)}},{key:"beginText",value:function(){var e=this.current;e.x=e.lineX=0,e.y=e.lineY=0,e.textMatrix=s.IDENTITY_MATRIX,e.lineMatrix=s.IDENTITY_MATRIX,e.textMatrixScale=1,e.tspan=this.svgFactory.createElement("svg:tspan"),e.txtElement=this.svgFactory.createElement("svg:text"),e.txtgrp=this.svgFactory.createElement("svg:g"),e.xcoords=[]}},{key:"moveText",value:function(e,t){var r=this.current;r.x=r.lineX+=e,r.y=r.lineY+=t,r.xcoords=[],r.tspan=this.svgFactory.createElement("svg:tspan"),r.tspan.setAttributeNS(null,"font-family",r.fontFamily),r.tspan.setAttributeNS(null,"font-size","".concat(m(r.fontSize),"px")),r.tspan.setAttributeNS(null,"y",m(-r.y))}},{key:"showText",value:function(e){var t=this.current,r=t.font,n=t.fontSize;if(0!==n){var i=t.charSpacing,a=t.wordSpacing,o=t.fontDirection,u=t.textHScale*o,c=r.vertical,l=n*t.fontMatrix[0],h=0,f=!0,d=!1,p=void 0;try{for(var v,_=e[Symbol.iterator]();!(f=(v=_.next()).done);f=!0){var b=v.value;if(null!==b)if((0,s.isNum)(b))h+=-b*n*.001;else{var A=b.width,S=b.fontChar,w=A*l+((b.isSpace?a:0)+i)*o;b.isInFont||r.missingFile?(t.xcoords.push(t.x+h*u),t.tspan.textContent+=S,h+=w):h+=w}else h+=o*a}}catch(e){d=!0,p=e}finally{try{f||null==_.return||_.return()}finally{if(d)throw p}}c?t.y-=h*u:t.x+=h*u,t.tspan.setAttributeNS(null,"x",t.xcoords.map(m).join(" ")),t.tspan.setAttributeNS(null,"y",m(-t.y)),t.tspan.setAttributeNS(null,"font-family",t.fontFamily),t.tspan.setAttributeNS(null,"font-size","".concat(m(t.fontSize),"px")),t.fontStyle!==y.fontStyle&&t.tspan.setAttributeNS(null,"font-style",t.fontStyle),t.fontWeight!==y.fontWeight&&t.tspan.setAttributeNS(null,"font-weight",t.fontWeight);var k=t.textRenderingMode&s.TextRenderingMode.FILL_STROKE_MASK;if(k===s.TextRenderingMode.FILL||k===s.TextRenderingMode.FILL_STROKE?(t.fillColor!==y.fillColor&&t.tspan.setAttributeNS(null,"fill",t.fillColor),t.fillAlpha<1&&t.tspan.setAttributeNS(null,"fill-opacity",t.fillAlpha)):t.textRenderingMode===s.TextRenderingMode.ADD_TO_PATH?t.tspan.setAttributeNS(null,"fill","transparent"):t.tspan.setAttributeNS(null,"fill","none"),k===s.TextRenderingMode.STROKE||k===s.TextRenderingMode.FILL_STROKE){var x=1/(t.textMatrixScale||1);this._setStrokeAttributes(t.tspan,x)}var C=t.textMatrix;0!==t.textRise&&((C=C.slice())[5]+=t.textRise),t.txtElement.setAttributeNS(null,"transform","".concat(g(C)," scale(1, -1)")),t.txtElement.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),t.txtElement.appendChild(t.tspan),t.txtgrp.appendChild(t.txtElement),this._ensureTransformGroup().appendChild(t.txtElement)}}},{key:"setLeadingMoveText",value:function(e,t){this.setLeading(-t),this.moveText(e,t)}},{key:"addFontStyle",value:function(e){this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.appendChild(this.cssStyle));var t=(0,s.createObjectURL)(e.data,e.mimetype,this.forceDataSchema);this.cssStyle.textContent+='@font-face { font-family: "'.concat(e.loadedName,'";')+" src: url(".concat(t,"); }\n")}},{key:"setFont",value:function(e){var t=this.current,r=this.commonObjs.get(e[0]),n=e[1];t.font=r,this.embedFonts&&r.data&&!this.embeddedFonts[r.loadedName]&&(this.addFontStyle(r),this.embeddedFonts[r.loadedName]=r),t.fontMatrix=r.fontMatrix?r.fontMatrix:s.FONT_IDENTITY_MATRIX;var i=r.black?r.bold?"bolder":"bold":r.bold?"bold":"normal",a=r.italic?"italic":"normal";n<0?(n=-n,t.fontDirection=-1):t.fontDirection=1,t.fontSize=n,t.fontFamily=r.loadedName,t.fontWeight=i,t.fontStyle=a,t.tspan=this.svgFactory.createElement("svg:tspan"),t.tspan.setAttributeNS(null,"y",m(-t.y)),t.xcoords=[]}},{key:"endText",value:function(){var e=this.current;e.textRenderingMode&s.TextRenderingMode.ADD_TO_PATH_FLAG&&e.txtElement&&e.txtElement.hasChildNodes()&&(e.element=e.txtElement,this.clip("nonzero"),this.endPath())}},{key:"setLineWidth",value:function(e){e>0&&(this.current.lineWidth=e)}},{key:"setLineCap",value:function(e){this.current.lineCap=b[e]}},{key:"setLineJoin",value:function(e){this.current.lineJoin=A[e]}},{key:"setMiterLimit",value:function(e){this.current.miterLimit=e}},{key:"setStrokeAlpha",value:function(e){this.current.strokeAlpha=e}},{key:"setStrokeRGBColor",value:function(e,t,r){this.current.strokeColor=s.Util.makeCssRgb(e,t,r)}},{key:"setFillAlpha",value:function(e){this.current.fillAlpha=e}},{key:"setFillRGBColor",value:function(e,t,r){this.current.fillColor=s.Util.makeCssRgb(e,t,r),this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[]}},{key:"setStrokeColorN",value:function(e){this.current.strokeColor=this._makeColorN_Pattern(e)}},{key:"setFillColorN",value:function(e){this.current.fillColor=this._makeColorN_Pattern(e)}},{key:"shadingFill",value:function(e){var t=this.viewport.width,r=this.viewport.height,n=s.Util.inverseTransform(this.transformMatrix),i=s.Util.applyTransform([0,0],n),a=s.Util.applyTransform([0,r],n),o=s.Util.applyTransform([t,0],n),u=s.Util.applyTransform([t,r],n),c=Math.min(i[0],a[0],o[0],u[0]),l=Math.min(i[1],a[1],o[1],u[1]),h=Math.max(i[0],a[0],o[0],u[0]),f=Math.max(i[1],a[1],o[1],u[1]),d=this.svgFactory.createElement("svg:rect");d.setAttributeNS(null,"x",c),d.setAttributeNS(null,"y",l),d.setAttributeNS(null,"width",h-c),d.setAttributeNS(null,"height",f-l),d.setAttributeNS(null,"fill",this._makeShadingPattern(e)),this._ensureTransformGroup().appendChild(d)}},{key:"_makeColorN_Pattern",value:function(e){return"TilingPattern"===e[0]?this._makeTilingPattern(e):this._makeShadingPattern(e)}},{key:"_makeTilingPattern",value:function(e){var t=e[1],r=e[2],n=e[3]||s.IDENTITY_MATRIX,i=h(e[4],4),a=i[0],o=i[1],u=i[2],c=i[3],f=e[5],d=e[6],p=e[7],v="shading".concat(C++),m=h(s.Util.applyTransform([a,o],n),2),g=m[0],y=m[1],_=h(s.Util.applyTransform([u,c],n),2),b=_[0],A=_[1],S=h(s.Util.singularValueDecompose2dScale(n),2),w=f*S[0],k=d*S[1],x=this.svgFactory.createElement("svg:pattern");x.setAttributeNS(null,"id",v),x.setAttributeNS(null,"patternUnits","userSpaceOnUse"),x.setAttributeNS(null,"width",w),x.setAttributeNS(null,"height",k),x.setAttributeNS(null,"x","".concat(g)),x.setAttributeNS(null,"y","".concat(y));var P=this.svg,R=this.transformMatrix,T=this.current.fillColor,E=this.current.strokeColor,I=this.svgFactory.create(b-g,A-y);if(this.svg=I,this.transformMatrix=n,2===p){var O=s.Util.makeCssRgb.apply(s.Util,l(t));this.current.fillColor=O,this.current.strokeColor=O}return this.executeOpTree(this.convertOpList(r)),this.svg=P,this.transformMatrix=R,this.current.fillColor=T,this.current.strokeColor=E,x.appendChild(I.childNodes[0]),this.defs.appendChild(x),"url(#".concat(v,")")}},{key:"_makeShadingPattern",value:function(e){switch(e[0]){case"RadialAxial":var t,r="shading".concat(C++),n=e[2];switch(e[1]){case"axial":var i=e[3],a=e[4];(t=this.svgFactory.createElement("svg:linearGradient")).setAttributeNS(null,"id",r),t.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),t.setAttributeNS(null,"x1",i[0]),t.setAttributeNS(null,"y1",i[1]),t.setAttributeNS(null,"x2",a[0]),t.setAttributeNS(null,"y2",a[1]);break;case"radial":var o=e[3],u=e[4],c=e[5],l=e[6];(t=this.svgFactory.createElement("svg:radialGradient")).setAttributeNS(null,"id",r),t.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),t.setAttributeNS(null,"cx",u[0]),t.setAttributeNS(null,"cy",u[1]),t.setAttributeNS(null,"r",l),t.setAttributeNS(null,"fx",o[0]),t.setAttributeNS(null,"fy",o[1]),t.setAttributeNS(null,"fr",c);break;default:throw new Error("Unknown RadialAxial type: ".concat(e[1]))}var h=!0,f=!1,d=void 0;try{for(var p,v=n[Symbol.iterator]();!(h=(p=v.next()).done);h=!0){var m=p.value,g=this.svgFactory.createElement("svg:stop");g.setAttributeNS(null,"offset",m[0]),g.setAttributeNS(null,"stop-color",m[1]),t.appendChild(g)}}catch(e){f=!0,d=e}finally{try{h||null==v.return||v.return()}finally{if(f)throw d}}return this.defs.appendChild(t),"url(#".concat(r,")");case"Mesh":return(0,s.warn)("Unimplemented pattern Mesh"),null;case"Dummy":return"hotpink";default:throw new Error("Unknown IR type: ".concat(e[0]))}}},{key:"setDash",value:function(e,t){this.current.dashArray=e,this.current.dashPhase=t}},{key:"constructPath",value:function(e,t){var r=this.current,n=r.x,i=r.y,a=[],o=0,u=!0,c=!1,l=void 0;try{for(var h,f=e[Symbol.iterator]();!(u=(h=f.next()).done);u=!0){switch(0|h.value){case s.OPS.rectangle:n=t[o++],i=t[o++];var d=n+t[o++],p=i+t[o++];a.push("M",m(n),m(i),"L",m(d),m(i),"L",m(d),m(p),"L",m(n),m(p),"Z");break;case s.OPS.moveTo:n=t[o++],i=t[o++],a.push("M",m(n),m(i));break;case s.OPS.lineTo:n=t[o++],i=t[o++],a.push("L",m(n),m(i));break;case s.OPS.curveTo:n=t[o+4],i=t[o+5],a.push("C",m(t[o]),m(t[o+1]),m(t[o+2]),m(t[o+3]),m(n),m(i)),o+=6;break;case s.OPS.curveTo2:n=t[o+2],i=t[o+3],a.push("C",m(n),m(i),m(t[o]),m(t[o+1]),m(t[o+2]),m(t[o+3])),o+=4;break;case s.OPS.curveTo3:n=t[o+2],i=t[o+3],a.push("C",m(t[o]),m(t[o+1]),m(n),m(i),m(n),m(i)),o+=4;break;case s.OPS.closePath:a.push("Z")}}}catch(e){c=!0,l=e}finally{try{u||null==f.return||f.return()}finally{if(c)throw l}}a=a.join(" "),r.path&&e.length>0&&e[0]!==s.OPS.rectangle&&e[0]!==s.OPS.moveTo?a=r.path.getAttributeNS(null,"d")+a:(r.path=this.svgFactory.createElement("svg:path"),this._ensureTransformGroup().appendChild(r.path)),r.path.setAttributeNS(null,"d",a),r.path.setAttributeNS(null,"fill","none"),r.element=r.path,r.setCurrentPoint(n,i)}},{key:"endPath",value:function(){var e=this.current;if(e.path=null,this.pendingClip)if(e.element){var t="clippath".concat(k++),r=this.svgFactory.createElement("svg:clipPath");r.setAttributeNS(null,"id",t),r.setAttributeNS(null,"transform",g(this.transformMatrix));var n=e.element.cloneNode(!0);"evenodd"===this.pendingClip?n.setAttributeNS(null,"clip-rule","evenodd"):n.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,r.appendChild(n),this.defs.appendChild(r),e.activeClipUrl&&(e.clipGroup=null,this.extraStack.forEach(function(e){e.clipGroup=null}),r.setAttributeNS(null,"clip-path",e.activeClipUrl)),e.activeClipUrl="url(#".concat(t,")"),this.tgrp=null}else this.pendingClip=null}},{key:"clip",value:function(e){this.pendingClip=e}},{key:"closePath",value:function(){var e=this.current;if(e.path){var t="".concat(e.path.getAttributeNS(null,"d"),"Z");e.path.setAttributeNS(null,"d",t)}}},{key:"setLeading",value:function(e){this.current.leading=-e}},{key:"setTextRise",value:function(e){this.current.textRise=e}},{key:"setTextRenderingMode",value:function(e){this.current.textRenderingMode=e}},{key:"setHScale",value:function(e){this.current.textHScale=e/100}},{key:"setRenderingIntent",value:function(e){}},{key:"setFlatness",value:function(e){}},{key:"setGState",value:function(e){var t=!0,r=!1,n=void 0;try{for(var i,a=e[Symbol.iterator]();!(t=(i=a.next()).done);t=!0){var o=h(i.value,2),u=o[0],c=o[1];switch(u){case"LW":this.setLineWidth(c);break;case"LC":this.setLineCap(c);break;case"LJ":this.setLineJoin(c);break;case"ML":this.setMiterLimit(c);break;case"D":this.setDash(c[0],c[1]);break;case"RI":this.setRenderingIntent(c);break;case"FL":this.setFlatness(c);break;case"Font":this.setFont(c);break;case"CA":this.setStrokeAlpha(c);break;case"ca":this.setFillAlpha(c);break;default:(0,s.warn)("Unimplemented graphic state operator ".concat(u))}}}catch(e){r=!0,n=e}finally{try{t||null==a.return||a.return()}finally{if(r)throw n}}}},{key:"fill",value:function(){var e=this.current;e.element&&(e.element.setAttributeNS(null,"fill",e.fillColor),e.element.setAttributeNS(null,"fill-opacity",e.fillAlpha),this.endPath())}},{key:"stroke",value:function(){var e=this.current;e.element&&(this._setStrokeAttributes(e.element),e.element.setAttributeNS(null,"fill","none"),this.endPath())}},{key:"_setStrokeAttributes",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=this.current,n=r.dashArray;1!==t&&n.length>0&&(n=n.map(function(e){return t*e})),e.setAttributeNS(null,"stroke",r.strokeColor),e.setAttributeNS(null,"stroke-opacity",r.strokeAlpha),e.setAttributeNS(null,"stroke-miterlimit",m(r.miterLimit)),e.setAttributeNS(null,"stroke-linecap",r.lineCap),e.setAttributeNS(null,"stroke-linejoin",r.lineJoin),e.setAttributeNS(null,"stroke-width",m(t*r.lineWidth)+"px"),e.setAttributeNS(null,"stroke-dasharray",n.map(m).join(" ")),e.setAttributeNS(null,"stroke-dashoffset",m(t*r.dashPhase)+"px")}},{key:"eoFill",value:function(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fill()}},{key:"fillStroke",value:function(){this.stroke(),this.fill()}},{key:"eoFillStroke",value:function(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()}},{key:"closeStroke",value:function(){this.closePath(),this.stroke()}},{key:"closeFillStroke",value:function(){this.closePath(),this.fillStroke()}},{key:"closeEOFillStroke",value:function(){this.closePath(),this.eoFillStroke()}},{key:"paintSolidColorImageMask",value:function(){var e=this.svgFactory.createElement("svg:rect");e.setAttributeNS(null,"x","0"),e.setAttributeNS(null,"y","0"),e.setAttributeNS(null,"width","1px"),e.setAttributeNS(null,"height","1px"),e.setAttributeNS(null,"fill",this.current.fillColor),this._ensureTransformGroup().appendChild(e)}},{key:"paintJpegXObject",value:function(e,t,r){var n=this.objs.get(e),i=this.svgFactory.createElement("svg:image");i.setAttributeNS(_,"xlink:href",n.src),i.setAttributeNS(null,"width",m(t)),i.setAttributeNS(null,"height",m(r)),i.setAttributeNS(null,"x","0"),i.setAttributeNS(null,"y",m(-r)),i.setAttributeNS(null,"transform","scale(".concat(m(1/t)," ").concat(m(-1/r),")")),this._ensureTransformGroup().appendChild(i)}},{key:"paintImageXObject",value:function(e){var t=this.objs.get(e);t?this.paintInlineImageXObject(t):(0,s.warn)("Dependent image with object ID ".concat(e," is not ready yet"))}},{key:"paintInlineImageXObject",value:function(e,t){var r=e.width,n=e.height,i=S(e,this.forceDataSchema,!!t),a=this.svgFactory.createElement("svg:rect");a.setAttributeNS(null,"x","0"),a.setAttributeNS(null,"y","0"),a.setAttributeNS(null,"width",m(r)),a.setAttributeNS(null,"height",m(n)),this.current.element=a,this.clip("nonzero");var o=this.svgFactory.createElement("svg:image");o.setAttributeNS(_,"xlink:href",i),o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y",m(-n)),o.setAttributeNS(null,"width",m(r)+"px"),o.setAttributeNS(null,"height",m(n)+"px"),o.setAttributeNS(null,"transform","scale(".concat(m(1/r)," ").concat(m(-1/n),")")),t?t.appendChild(o):this._ensureTransformGroup().appendChild(o)}},{key:"paintImageMaskXObject",value:function(e){var t=this.current,r=e.width,n=e.height,i=t.fillColor;t.maskId="mask".concat(x++);var a=this.svgFactory.createElement("svg:mask");a.setAttributeNS(null,"id",t.maskId);var o=this.svgFactory.createElement("svg:rect");o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y","0"),o.setAttributeNS(null,"width",m(r)),o.setAttributeNS(null,"height",m(n)),o.setAttributeNS(null,"fill",i),o.setAttributeNS(null,"mask","url(#".concat(t.maskId,")")),this.defs.appendChild(a),this._ensureTransformGroup().appendChild(o),this.paintInlineImageXObject(e,a)}},{key:"paintFormXObjectBegin",value:function(e,t){if(Array.isArray(e)&&6===e.length&&this.transform(e[0],e[1],e[2],e[3],e[4],e[5]),t){var r=t[2]-t[0],n=t[3]-t[1],i=this.svgFactory.createElement("svg:rect");i.setAttributeNS(null,"x",t[0]),i.setAttributeNS(null,"y",t[1]),i.setAttributeNS(null,"width",m(r)),i.setAttributeNS(null,"height",m(n)),this.current.element=i,this.clip("nonzero"),this.endPath()}}},{key:"paintFormXObjectEnd",value:function(){}},{key:"_initialize",value:function(e){var t=this.svgFactory.create(e.width,e.height),r=this.svgFactory.createElement("svg:defs");t.appendChild(r),this.defs=r;var n=this.svgFactory.createElement("svg:g");return n.setAttributeNS(null,"transform",g(e.transform)),t.appendChild(n),this.svg=n,t}},{key:"_ensureClipGroup",value:function(){if(!this.current.clipGroup){var e=this.svgFactory.createElement("svg:g");e.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.appendChild(e),this.current.clipGroup=e}return this.current.clipGroup}},{key:"_ensureTransformGroup",value:function(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",g(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().appendChild(this.tgrp):this.svg.appendChild(this.tgrp)),this.tgrp}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFNodeStream=void 0;var i,a=(i=n(148))&&i.__esModule?i:{default:i},o=n(1),s=n(166);function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function c(e,t){return!t||"object"!==u(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function l(e){return(l=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function h(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&f(e,t)}function f(e,t){return(f=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function d(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function p(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var a=e.apply(t,r);function o(e){d(a,n,i,o,s,"next",e)}function s(e){d(a,n,i,o,s,"throw",e)}o(void 0)})}}function v(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function g(e,t,r){return t&&m(e.prototype,t),r&&m(e,r),e}var y=r(3),_=r(4),b=r(5),A=r("UZ5h"),S=/^file:\/\/\/[a-zA-Z]:\//;var w=function(){function e(t){var r,n;v(this,e),this.source=t,this.url=(r=t.url,"file:"===(n=A.parse(r)).protocol||n.host?n:/^[a-z]:[/\\]/i.test(r)?A.parse("file:///".concat(r)):(n.host||(n.protocol="file:"),n)),this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol,this.isFsUrl="file:"===this.url.protocol,this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}return g(e,[{key:"getFullReader",value:function(){return(0,o.assert)(!this._fullRequestReader),this._fullRequestReader=this.isFsUrl?new T(this):new P(this),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var r=this.isFsUrl?new E(this,e,t):new R(this,e,t);return this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeRequestReaders.slice(0).forEach(function(t){t.cancel(e)})}},{key:"_progressiveDataLength",get:function(){return this._fullRequestReader?this._fullRequestReader._loaded:0}}]),e}();t.PDFNodeStream=w;var k=function(){function e(t){v(this,e),this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;var r=t.source;this._contentLength=r.length,this._loaded=0,this._filename=null,this._disableRange=r.disableRange||!1,this._rangeChunkSize=r.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!r.disableStream,this._isRangeSupported=!r.disableRange,this._readableStream=null,this._readCapability=(0,o.createPromiseCapability)(),this._headersCapability=(0,o.createPromiseCapability)()}return g(e,[{key:"read",value:function(){var e=p(a.default.mark(function e(){var t,r;return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:if(!this._done){e.next=4;break}return e.abrupt("return",{value:void 0,done:!0});case 4:if(!this._storedError){e.next=6;break}throw this._storedError;case 6:if(null!==(t=this._readableStream.read())){e.next=10;break}return this._readCapability=(0,o.createPromiseCapability)(),e.abrupt("return",this.read());case 10:return this._loaded+=t.length,this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength}),r=new Uint8Array(t).buffer,e.abrupt("return",{value:r,done:!1});case 14:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._readableStream?this._readableStream.destroy(e):this._error(e)}},{key:"_error",value:function(e){this._storedError=e,this._readCapability.resolve()}},{key:"_setReadableStream",value:function(e){var t=this;this._readableStream=e,e.on("readable",function(){t._readCapability.resolve()}),e.on("end",function(){e.destroy(),t._done=!0,t._readCapability.resolve()}),e.on("error",function(e){t._error(e)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new o.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}},{key:"headersReady",get:function(){return this._headersCapability.promise}},{key:"filename",get:function(){return this._filename}},{key:"contentLength",get:function(){return this._contentLength}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}(),x=function(){function e(t){v(this,e),this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=(0,o.createPromiseCapability)();var r=t.source;this._isStreamingSupported=!r.disableStream}return g(e,[{key:"read",value:function(){var e=p(a.default.mark(function e(){var t,r;return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:if(!this._done){e.next=4;break}return e.abrupt("return",{value:void 0,done:!0});case 4:if(!this._storedError){e.next=6;break}throw this._storedError;case 6:if(null!==(t=this._readableStream.read())){e.next=10;break}return this._readCapability=(0,o.createPromiseCapability)(),e.abrupt("return",this.read());case 10:return this._loaded+=t.length,this.onProgress&&this.onProgress({loaded:this._loaded}),r=new Uint8Array(t).buffer,e.abrupt("return",{value:r,done:!1});case 14:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._readableStream?this._readableStream.destroy(e):this._error(e)}},{key:"_error",value:function(e){this._storedError=e,this._readCapability.resolve()}},{key:"_setReadableStream",value:function(e){var t=this;this._readableStream=e,e.on("readable",function(){t._readCapability.resolve()}),e.on("end",function(){e.destroy(),t._done=!0,t._readCapability.resolve()}),e.on("error",function(e){t._error(e)}),this._storedError&&this._readableStream.destroy(this._storedError)}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}();function C(e,t){return{protocol:e.protocol,auth:e.auth,host:e.hostname,port:e.port,path:e.path,method:"GET",headers:t}}var P=function(e){function t(e){var r;v(this,t);var n=function(t){if(404===t.statusCode){var n=new o.MissingPDFException('Missing PDF "'.concat(r._url,'".'));return r._storedError=n,void r._headersCapability.reject(n)}r._headersCapability.resolve(),r._setReadableStream(t);var i=function(e){return r._readableStream.headers[e.toLowerCase()]},a=(0,s.validateRangeRequestCapabilities)({getResponseHeader:i,isHttp:e.isHttp,rangeChunkSize:r._rangeChunkSize,disableRange:r._disableRange}),u=a.allowRangeRequests,c=a.suggestedLength;r._isRangeSupported=u,r._contentLength=c||r._contentLength,r._filename=(0,s.extractFilenameFromHeader)(i)};return(r=c(this,l(t).call(this,e)))._request=null,"http:"===r._url.protocol?r._request=_.request(C(r._url,e.httpHeaders),n):r._request=b.request(C(r._url,e.httpHeaders),n),r._request.on("error",function(e){r._storedError=e,r._headersCapability.reject(e)}),r._request.end(),r}return h(t,k),t}(),R=function(e){function t(e,r,n){var i;for(var a in v(this,t),(i=c(this,l(t).call(this,e)))._httpHeaders={},e.httpHeaders){var s=e.httpHeaders[a];void 0!==s&&(i._httpHeaders[a]=s)}i._httpHeaders.Range="bytes=".concat(r,"-").concat(n-1);var u=function(e){if(404!==e.statusCode)i._setReadableStream(e);else{var t=new o.MissingPDFException('Missing PDF "'.concat(i._url,'".'));i._storedError=t}};return i._request=null,"http:"===i._url.protocol?i._request=_.request(C(i._url,i._httpHeaders),u):i._request=b.request(C(i._url,i._httpHeaders),u),i._request.on("error",function(e){i._storedError=e}),i._request.end(),i}return h(t,x),t}(),T=function(e){function t(e){var r;v(this,t),r=c(this,l(t).call(this,e));var n=decodeURIComponent(r._url.path);return S.test(r._url.href)&&(n=n.replace(/^\//,"")),y.lstat(n,function(e,t){if(e)return"ENOENT"===e.code&&(e=new o.MissingPDFException('Missing PDF "'.concat(n,'".'))),r._storedError=e,void r._headersCapability.reject(e);r._contentLength=t.size,r._setReadableStream(y.createReadStream(n)),r._headersCapability.resolve()}),r}return h(t,k),t}(),E=function(e){function t(e,r,n){var i;v(this,t),i=c(this,l(t).call(this,e));var a=decodeURIComponent(i._url.path);return S.test(i._url.href)&&(a=a.replace(/^\//,"")),i._setReadableStream(y.createReadStream(a,{start:r,end:n-1})),i}return h(t,x),t}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createResponseStatusError=function(e,t){if(404===e||0===e&&/^file:/.test(t))return new n.MissingPDFException('Missing PDF "'+t+'".');return new n.UnexpectedResponseException("Unexpected server response ("+e+') while retrieving PDF "'+t+'".',e)},t.extractFilenameFromHeader=function(e){var t=e("Content-Disposition");if(t){var r=(0,i.getFilenameFromContentDispositionHeader)(t);if(/\.pdf$/i.test(r))return r}return null},t.validateRangeRequestCapabilities=function(e){var t=e.getResponseHeader,r=e.isHttp,i=e.rangeChunkSize,a=e.disableRange;(0,n.assert)(i>0,"Range chunk size must be larger than zero");var o={allowRangeRequests:!1,suggestedLength:void 0},s=parseInt(t("Content-Length"),10);if(!Number.isInteger(s))return o;if(o.suggestedLength=s,s<=2*i)return o;if(a||!r)return o;if("bytes"!==t("Accept-Ranges"))return o;if("identity"!==(t("Content-Encoding")||"identity"))return o;return o.allowRangeRequests=!0,o},t.validateResponseStatus=function(e){return 200===e||206===e};var n=r(1),i=r(167)},function(e,t,r){"use strict";function n(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}Object.defineProperty(t,"__esModule",{value:!0}),t.getFilenameFromContentDispositionHeader=function(e){var t=!0,r=s("filename\\*","i").exec(e);if(r){var i=l(r=r[1]);return c(i=f(i=h(i=unescape(i))))}if(r=function(e){for(var t,r=[],i=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");null!==(t=i.exec(e));){var a=t,o=n(a,4),u=o[1],c=o[2],f=o[3];if((u=parseInt(u,10))in r){if(0===u)break}else r[u]=[c,f]}for(var d=[],u=0;u<r.length&&u in r;++u){var p=n(r[u],2),c=p[0],f=p[1];f=l(f),c&&(f=unescape(f),0===u&&(f=h(f))),d.push(f)}return d.join("")}(e)){var a=f(r);return c(a)}if(r=s("filename","i").exec(e)){var o=l(r=r[1]);return c(o=f(o))}function s(e,t){return new RegExp("(?:^|;)\\s*"+e+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',t)}function u(e,r){if(e){if(!/^[\x00-\xFF]+$/.test(r))return r;try{var n=new TextDecoder(e,{fatal:!0}),i=Array.from(r,function(e){return 255&e.charCodeAt(0)});r=n.decode(new Uint8Array(i)),t=!1}catch(n){if(/^utf-?8$/i.test(e))try{r=decodeURIComponent(escape(r)),t=!1}catch(e){}}}return r}function c(e){return t&&/[\x80-\xff]/.test(e)&&(e=u("utf-8",e),t&&(e=u("iso-8859-1",e))),e}function l(e){if(e.startsWith('"')){for(var t=e.slice(1).split('\\"'),r=0;r<t.length;++r){var n=t[r].indexOf('"');-1!==n&&(t[r]=t[r].slice(0,n),t.length=r+1),t[r]=t[r].replace(/\\(.)/g,"$1")}e=t.join('"')}return e}function h(e){var t=e.indexOf("'");if(-1===t)return e;var r=e.slice(0,t),n=e.slice(t+1),i=n.replace(/^[^']*'/,"");return u(r,i)}function f(e){return!e.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(e)?e:e.replace(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(e,t,r,n){if("q"===r||"Q"===r)return n=(n=n.replace(/_/g," ")).replace(/=([0-9a-fA-F]{2})/g,function(e,t){return String.fromCharCode(parseInt(t,16))}),u(t,n);try{n=atob(n)}catch(e){}return u(t,n)})}return""}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFNetworkStream=void 0;var n,i=(n=r(148))&&n.__esModule?n:{default:n},a=r(1),o=r(166);function s(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function u(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var a=e.apply(t,r);function o(e){s(a,n,i,o,u,"next",e)}function u(e){s(a,n,i,o,u,"throw",e)}o(void 0)})}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function h(e,t,r){return t&&l(e.prototype,t),r&&l(e,r),e}var f=function(){function e(t,r){c(this,e),this.url=t,r=r||{},this.isHttp=/^https?:/i.test(t),this.httpHeaders=this.isHttp&&r.httpHeaders||{},this.withCredentials=r.withCredentials||!1,this.getXhr=r.getXhr||function(){return new XMLHttpRequest},this.currXhrId=0,this.pendingRequests=Object.create(null)}return h(e,[{key:"requestRange",value:function(e,t,r){var n={begin:e,end:t};for(var i in r)n[i]=r[i];return this.request(n)}},{key:"requestFull",value:function(e){return this.request(e)}},{key:"request",value:function(e){var t=this.getXhr(),r=this.currXhrId++,n=this.pendingRequests[r]={xhr:t};for(var i in t.open("GET",this.url),t.withCredentials=this.withCredentials,this.httpHeaders){var a=this.httpHeaders[i];void 0!==a&&t.setRequestHeader(i,a)}return this.isHttp&&"begin"in e&&"end"in e?(t.setRequestHeader("Range","bytes=".concat(e.begin,"-").concat(e.end-1)),n.expectedStatus=206):n.expectedStatus=200,t.responseType="arraybuffer",e.onError&&(t.onerror=function(r){e.onError(t.status)}),t.onreadystatechange=this.onStateChange.bind(this,r),t.onprogress=this.onProgress.bind(this,r),n.onHeadersReceived=e.onHeadersReceived,n.onDone=e.onDone,n.onError=e.onError,n.onProgress=e.onProgress,t.send(null),r}},{key:"onProgress",value:function(e,t){var r=this.pendingRequests[e];r&&r.onProgress&&r.onProgress(t)}},{key:"onStateChange",value:function(e,t){var r=this.pendingRequests[e];if(r){var n=r.xhr;if(n.readyState>=2&&r.onHeadersReceived&&(r.onHeadersReceived(),delete r.onHeadersReceived),4===n.readyState&&e in this.pendingRequests)if(delete this.pendingRequests[e],0===n.status&&this.isHttp)r.onError&&r.onError(n.status);else{var i=n.status||200;if(200===i&&206===r.expectedStatus||i===r.expectedStatus){var o=function(e){var t=e.response;return"string"!=typeof t?t:(0,a.stringToBytes)(t).buffer}(n);if(206===i){var s=n.getResponseHeader("Content-Range"),u=/bytes (\d+)-(\d+)\/(\d+)/.exec(s);r.onDone({begin:parseInt(u[1],10),chunk:o})}else o?r.onDone({begin:0,chunk:o}):r.onError&&r.onError(n.status)}else r.onError&&r.onError(n.status)}}}},{key:"hasPendingRequests",value:function(){for(var e in this.pendingRequests)return!0;return!1}},{key:"getRequestXhr",value:function(e){return this.pendingRequests[e].xhr}},{key:"isPendingRequest",value:function(e){return e in this.pendingRequests}},{key:"abortAllRequests",value:function(){for(var e in this.pendingRequests)this.abortRequest(0|e)}},{key:"abortRequest",value:function(e){var t=this.pendingRequests[e].xhr;delete this.pendingRequests[e],t.abort()}}]),e}(),d=function(){function e(t){c(this,e),this._source=t,this._manager=new f(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials}),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}return h(e,[{key:"_onRangeRequestReaderClosed",value:function(e){var t=this._rangeRequestReaders.indexOf(e);t>=0&&this._rangeRequestReaders.splice(t,1)}},{key:"getFullReader",value:function(){return(0,a.assert)(!this._fullRequestReader),this._fullRequestReader=new p(this._manager,this._source),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){var r=new v(this._manager,e,t);return r.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeRequestReaders.slice(0).forEach(function(t){t.cancel(e)})}}]),e}();t.PDFNetworkStream=d;var p=function(){function e(t,r){c(this,e),this._manager=t;var n={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=r.url,this._fullRequestId=t.requestFull(n),this._headersReceivedCapability=(0,a.createPromiseCapability)(),this._disableRange=r.disableRange||!1,this._contentLength=r.length,this._rangeChunkSize=r.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}return h(e,[{key:"_onHeadersReceived",value:function(){var e=this._fullRequestId,t=this._manager.getRequestXhr(e),r=function(e){return t.getResponseHeader(e)},n=(0,o.validateRangeRequestCapabilities)({getResponseHeader:r,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange}),i=n.allowRangeRequests,a=n.suggestedLength;i&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=(0,o.extractFilenameFromHeader)(r),this._isRangeSupported&&this._manager.abortRequest(e),this._headersReceivedCapability.resolve()}},{key:"_onDone",value:function(e){e&&(this._requests.length>0?this._requests.shift().resolve({value:e.chunk,done:!1}):this._cachedChunks.push(e.chunk));this._done=!0,this._cachedChunks.length>0||(this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[])}},{key:"_onError",value:function(e){var t=this._url,r=(0,o.createResponseStatusError)(e,t);this._storedError=r,this._headersReceivedCapability.reject(r),this._requests.forEach(function(e){e.reject(r)}),this._requests=[],this._cachedChunks=[]}},{key:"_onProgress",value:function(e){this.onProgress&&this.onProgress({loaded:e.loaded,total:e.lengthComputable?e.total:this._contentLength})}},{key:"read",value:function(){var e=u(i.default.mark(function e(){var t,r;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._storedError){e.next=2;break}throw this._storedError;case 2:if(!(this._cachedChunks.length>0)){e.next=5;break}return t=this._cachedChunks.shift(),e.abrupt("return",{value:t,done:!1});case 5:if(!this._done){e.next=7;break}return e.abrupt("return",{value:void 0,done:!0});case 7:return r=(0,a.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 10:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._done=!0,this._headersReceivedCapability.reject(e),this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[],this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}},{key:"filename",get:function(){return this._filename}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}},{key:"contentLength",get:function(){return this._contentLength}},{key:"headersReady",get:function(){return this._headersReceivedCapability.promise}}]),e}(),v=function(){function e(t,r,n){c(this,e),this._manager=t;var i={onDone:this._onDone.bind(this),onProgress:this._onProgress.bind(this)};this._requestId=t.requestRange(r,n,i),this._requests=[],this._queuedChunk=null,this._done=!1,this.onProgress=null,this.onClosed=null}return h(e,[{key:"_close",value:function(){this.onClosed&&this.onClosed(this)}},{key:"_onDone",value:function(e){var t=e.chunk;this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunk=t;this._done=!0,this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[],this._close()}},{key:"_onProgress",value:function(e){!this.isStreamingSupported&&this.onProgress&&this.onProgress({loaded:e.loaded})}},{key:"read",value:function(){var e=u(i.default.mark(function e(){var t,r;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(null===this._queuedChunk){e.next=4;break}return t=this._queuedChunk,this._queuedChunk=null,e.abrupt("return",{value:t,done:!1});case 4:if(!this._done){e.next=6;break}return e.abrupt("return",{value:void 0,done:!0});case 6:return r=(0,a.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 9:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._done=!0,this._requests.forEach(function(e){e.resolve({value:void 0,done:!0})}),this._requests=[],this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}},{key:"isStreamingSupported",get:function(){return!1}}]),e}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFetchStream=void 0;var n,i=(n=r(148))&&n.__esModule?n:{default:n},a=r(1),o=r(166);function s(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function u(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var a=e.apply(t,r);function o(e){s(a,n,i,o,u,"next",e)}function u(e){s(a,n,i,o,u,"throw",e)}o(void 0)})}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function h(e,t,r){return t&&l(e.prototype,t),r&&l(e,r),e}function f(e,t,r){return{method:"GET",headers:e,signal:r&&r.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}var d=function(){function e(t){c(this,e),this.source=t,this.isHttp=/^https?:/i.test(t.url),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}return h(e,[{key:"getFullReader",value:function(){return(0,a.assert)(!this._fullRequestReader),this._fullRequestReader=new p(this),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var r=new v(this,e,t);return this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeRequestReaders.slice(0).forEach(function(t){t.cancel(e)})}},{key:"_progressiveDataLength",get:function(){return this._fullRequestReader?this._fullRequestReader._loaded:0}}]),e}();t.PDFFetchStream=d;var p=function(){function e(t){var r=this;c(this,e),this._stream=t,this._reader=null,this._loaded=0,this._filename=null;var n=t.source;for(var i in this._withCredentials=n.withCredentials||!1,this._contentLength=n.length,this._headersCapability=(0,a.createPromiseCapability)(),this._disableRange=n.disableRange||!1,this._rangeChunkSize=n.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),"undefined"!=typeof AbortController&&(this._abortController=new AbortController),this._isStreamingSupported=!n.disableStream,this._isRangeSupported=!n.disableRange,this._headers=new Headers,this._stream.httpHeaders){var s=this._stream.httpHeaders[i];void 0!==s&&this._headers.append(i,s)}var u=n.url;fetch(u,f(this._headers,this._withCredentials,this._abortController)).then(function(e){if(!(0,o.validateResponseStatus)(e.status))throw(0,o.createResponseStatusError)(e.status,u);r._reader=e.body.getReader(),r._headersCapability.resolve();var t=function(t){return e.headers.get(t)},n=(0,o.validateRangeRequestCapabilities)({getResponseHeader:t,isHttp:r._stream.isHttp,rangeChunkSize:r._rangeChunkSize,disableRange:r._disableRange}),i=n.allowRangeRequests,s=n.suggestedLength;r._isRangeSupported=i,r._contentLength=s||r._contentLength,r._filename=(0,o.extractFilenameFromHeader)(t),!r._isStreamingSupported&&r._isRangeSupported&&r.cancel(new a.AbortException("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}return h(e,[{key:"read",value:function(){var e=u(i.default.mark(function e(){var t,r,n,a;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._headersCapability.promise;case 2:return e.next=4,this._reader.read();case 4:if(t=e.sent,r=t.value,!(n=t.done)){e.next=9;break}return e.abrupt("return",{value:r,done:n});case 9:return this._loaded+=r.byteLength,this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength}),a=new Uint8Array(r).buffer,e.abrupt("return",{value:a,done:!1});case 13:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._reader&&this._reader.cancel(e),this._abortController&&this._abortController.abort()}},{key:"headersReady",get:function(){return this._headersCapability.promise}},{key:"filename",get:function(){return this._filename}},{key:"contentLength",get:function(){return this._contentLength}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}(),v=function(){function e(t,r,n){var i=this;c(this,e),this._stream=t,this._reader=null,this._loaded=0;var s=t.source;for(var u in this._withCredentials=s.withCredentials||!1,this._readCapability=(0,a.createPromiseCapability)(),this._isStreamingSupported=!s.disableStream,"undefined"!=typeof AbortController&&(this._abortController=new AbortController),this._headers=new Headers,this._stream.httpHeaders){var l=this._stream.httpHeaders[u];void 0!==l&&this._headers.append(u,l)}this._headers.append("Range","bytes=".concat(r,"-").concat(n-1));var h=s.url;fetch(h,f(this._headers,this._withCredentials,this._abortController)).then(function(e){if(!(0,o.validateResponseStatus)(e.status))throw(0,o.createResponseStatusError)(e.status,h);i._readCapability.resolve(),i._reader=e.body.getReader()}),this.onProgress=null}return h(e,[{key:"read",value:function(){var e=u(i.default.mark(function e(){var t,r,n,a;return i.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:return e.next=4,this._reader.read();case 4:if(t=e.sent,r=t.value,!(n=t.done)){e.next=9;break}return e.abrupt("return",{value:r,done:n});case 9:return this._loaded+=r.byteLength,this.onProgress&&this.onProgress({loaded:this._loaded}),a=new Uint8Array(r).buffer,e.abrupt("return",{value:a,done:!1});case 13:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._reader&&this._reader.cancel(e),this._abortController&&this._abortController.abort()}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}()}])},e.exports=a()}).call(t,r("EuP9").Buffer,r("DuR2"),r("W2nU"))},oNmr:function(e,t,r){r("9Bbf")("Set")},oeOm:function(e,t,r){var n=r("7Doy");e.exports=function(e,t){return new(n(e))(t)}},owqv:function(e,t,r){e.exports=r.p+"static/img/zuojiantou.7311397.png"},pONs:function(e,t){},pPW7:function(e,t,r){r("M6a0"),r("zQR9"),r("+tPU"),r("ttyz"),r("BDhv"),r("oNmr"),r("ioQ5"),e.exports=r("FeBl").Set},qOJP:function(e,t,r){"use strict";e.exports={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"==typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},qo66:function(e,t,r){"use strict";var n=r("7KvD"),i=r("kM2E"),a=r("06OY"),o=r("S82l"),s=r("hJx8"),u=r("xH/j"),c=r("NWt+"),l=r("2KxR"),h=r("EqjI"),f=r("e6n0"),d=r("evD5").f,p=r("ALrJ")(0),v=r("+E39");e.exports=function(e,t,r,m,g,y){var _=n[e],b=_,A=g?"set":"add",S=b&&b.prototype,w={};return v&&"function"==typeof b&&(y||S.forEach&&!o(function(){(new b).entries().next()}))?(b=t(function(t,r){l(t,b,e,"_c"),t._c=new _,void 0!=r&&c(r,g,t[A],t)}),p("add,clear,delete,forEach,get,has,set,keys,values,entries,toJSON".split(","),function(e){var t="add"==e||"set"==e;e in S&&(!y||"clear"!=e)&&s(b.prototype,e,function(r,n){if(l(this,b,e),!t&&y&&!h(r))return"get"==e&&void 0;var i=this._c[e](0===r?0:r,n);return t?this:i})}),y||d(b.prototype,"size",{get:function(){return this._c.size}})):(b=m.getConstructor(t,e,g,A),u(b.prototype,r),a.NEED=!0),f(b,e),w[e]=b,i(i.G+i.W+i.F,w),y||m.setStrong(b,e,g),b}},sOR5:function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},ttyz:function(e,t,r){"use strict";var n=r("9C8M"),i=r("LIJb");e.exports=r("qo66")("Set",function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},{add:function(e){return n.def(i(this,"Set"),e=0===e?0:e,e)}},n)},tw4v:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r("Xxa5"),i=r.n(n),a=r("exGp"),o=r.n(a),s=r("Au9i"),u=r("c2Ch"),c=r("nKpR"),l=r.n(c),h={data:function(){return{agreeUrl:"",pdfDoc:null,pages:0,tradeAgreeText:"",siteInfo:{}}},mounted:function(){this.getInfoSite()},methods:{getInfoSite:function(){var e=this;return o()(i.a.mark(function t(){var r;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,u.J();case 2:0===(r=t.sent).status?(e.siteInfo=r.data,e.logo=r.data.siteLogoSm,e.agreeUrl=r.data.tradeAgree,e.tradeAgreeText=r.data.tradeAgreeText,e._loadFile(r.data.tradeAgree)):Object(s.Toast)(r.msg);case 4:case"end":return t.stop()}},t,e)}))()},_renderPage:function(e){var t=this;this.pdfDoc.getPage(e).then(function(r){var n=document.getElementById("the-canvas"+e),i=n.getContext("2d"),a=(window.devicePixelRatio||1)/(i.webkitBackingStorePixelRatio||i.mozBackingStorePixelRatio||i.msBackingStorePixelRatio||i.oBackingStorePixelRatio||i.backingStorePixelRatio||1),o=r.getViewport(screen.availWidth/r.getViewport(1).width);n.width=o.width*a,n.height=o.height*a,n.style.width=o.width+"px",n.style.height=o.height+"px",i.setTransform(a,0,0,a,0,0);var s={canvasContext:i,viewport:o};r.render(s),t.pages>e&&t._renderPage(e+1)})},_loadFile:function(e){var t=this;l.a.getDocument(e).then(function(e){t.pdfDoc=e,t.pages=t.pdfDoc.numPages,t.$nextTick(function(){t._renderPage(1)})})}}},f={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"wrapper"},[r("mt-header",{attrs:{fixed:"",title:e.siteInfo.tradeAgreeTitle}},[r("router-link",{attrs:{slot:"left",to:"/buy"},slot:"left"},[r("mt-button",{attrs:{icon:"back"}})],1)],1),e._v(" "),r("div",{staticClass:"content",staticStyle:{margin:"20px",background:"#fff","border-radius":"5px","margin-top":"60px","line-height":"200%"}},e._l(e.tradeAgreeText.split("。"),function(t){return r("p",{key:t},[e._v(e._s(t)+"。")])}),0),e._v(" "),e._l(e.pages,function(e){return r("canvas",{key:e,attrs:{id:"the-canvas"+e}})})],2)},staticRenderFns:[]};var d=r("VU/8")(h,f,!1,function(e){r("pONs")},"data-v-e12f0594",null);t.default=d.exports},ujcs:function(e,t){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
t.read=function(e,t,r,n,i){var a,o,s=8*i-n-1,u=(1<<s)-1,c=u>>1,l=-7,h=r?i-1:0,f=r?-1:1,d=e[t+h];for(h+=f,a=d&(1<<-l)-1,d>>=-l,l+=s;l>0;a=256*a+e[t+h],h+=f,l-=8);for(o=a&(1<<-l)-1,a>>=-l,l+=n;l>0;o=256*o+e[t+h],h+=f,l-=8);if(0===a)a=1-c;else{if(a===u)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,n),a-=c}return(d?-1:1)*o*Math.pow(2,a-n)},t.write=function(e,t,r,n,i,a){var o,s,u,c=8*a-i-1,l=(1<<c)-1,h=l>>1,f=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:a-1,p=n?1:-1,v=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),(t+=o+h>=1?f/u:f*Math.pow(2,1-h))*u>=2&&(o++,u/=2),o+h>=l?(s=0,o=l):o+h>=1?(s=(t*u-1)*Math.pow(2,i),o+=h):(s=t*Math.pow(2,h-1)*Math.pow(2,i),o=0));i>=8;e[r+d]=255&s,d+=p,s/=256,i-=8);for(o=o<<i|s,c+=i;c>0;e[r+d]=255&o,d+=p,o/=256,c-=8);e[r+d-p]|=128*v}},xaZU:function(e,t,r){"use strict";var n=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,t,r,s){return t=t||"&",r=r||"=",null===e&&(e=void 0),"object"==typeof e?a(o(e),function(o){var s=encodeURIComponent(n(o))+r;return i(e[o])?a(e[o],function(e){return s+encodeURIComponent(n(e))}).join(t):s+encodeURIComponent(n(e[o]))}).join(t):s?encodeURIComponent(n(s))+r+encodeURIComponent(n(e)):""};var i=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function a(e,t){if(e.map)return e.map(t);for(var r=[],n=0;n<e.length;n++)r.push(t(e[n],n));return r}var o=Object.keys||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t}}});