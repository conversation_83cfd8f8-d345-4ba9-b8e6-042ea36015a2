import Vue from 'vue'
import Router from 'vue-router'
import i18n from '@/locales/index.js'
// import Home from '@/page/home/<USER>'
// import Buy from '@/page/home/<USER>'
// import Alertdetail from '@/page/home/<USER>/alert' // 公告详情
// import Register from '@/page/register'
// import Forget from '@/page/forget'
// import Login from '@/page/login'
// import List from '@/page/list/list'
// import TradingList from '@/page/list/trading-list'
// import Searchlist from '@/page/list/list-search'
// import VipSearchlist from '@/page/list/list-searchVip'
// import IndexSearchlist from '@/page/list/indexlist-search' // 指数查询
// import SearchMylist from '@/page/list/my-list-search'
// import ListDetail from '@/page/list/listDetail'
// import ListDetail2 from '@/page/list/detail2'
// import MyList from '@/page/list/my-list'
// import Inquiry from '@/page/home/<USER>'
// import OrderList from '@/page/user/order-list'
// import Warehouse from '@/page/user/Warehouse.vue'
// import holdOrderList from '@/page/user/search-order/hold-stockCode'
// import holdOrderList2 from '@/page/user/search-order/hold-stockSpell'
// import sellOrderList from '@/page/user/search-order/sell-stockCode'
// import sellOrderList2 from '@/page/user/search-order/sell-stockSpell'
// import Detail from '@/page/user/detail'
// import Card from '@/page/user/card'
// import Authentication from '@/page/user/authentication'
// import Aggre from '@/page/user/agreement'
// import Recharge from '@/page/user/recharge'
// import RechargeSure from '@/page/user/recharge-sure'
// import RechargeList from '@/page/user/rechargelist'
// import Cash from '@/page/user/cash'
// import Cashlist from '@/page/user/cashlist'
// import AddCard from '@/page/user/addCard'
// import Setting from '@/page/user/my'
// import Transfer from '@/page/user/transfer'
// import IndexList from '@/page/list/index-list'
// import indexBuy from '@/page/home/<USER>'
// import TwoBuy from '@/page/home/<USER>'
// import SubWarehouseBuy from '@/page/home/<USER>'
// import futuresBuy from '@/page/home/<USER>'
// import Agree from '@/page/registerAgree'
// import Trage from '@/page/tradeAgree'
// import OpenAccount from '@/page/openaccount'
// import FundsList from '@/page/funds/funds-list'
// import newLogin from '@/page/login/login.vue'
// import newRegister from '@/page/login/register.vue'
// import NewPage from '@/page/home/<USER>'
// import NewGg from '@/page/home/<USER>'
// import KLine from '@/page/kline/index.vue'
// import TradingBuy from '@/page/trading/buy.vue'
// import NewUser from '@/page/newUser/index.vue'
// import Wallet from '@/page/wallet/index.vue'
// import TransferRecord from '@/page/transferRecord/index.vue'
// import CashWithdrawalRecord from '@/page/cashWithdrawalRecord/index.vue'
// import Transfers from '@/page/transfer/index.vue'
// import Authentications from '@/page/authentication/index.vue'
// import BankCard from '@/page/bankCard/index.vue'
// import service from '@/page/service/service.vue'
// import recharge from '@/page/newUser/recharge.vue'
// import rechargePay from '@/page/newUser/rechargePay.vue'
// import withdraw from '@/page/newUser/withdraw.vue'
// import loginPassword from '@/page/newUser/loginPassword.vue'
// import setPassword from '@/page/newUser/setPassword.vue'
// import resetpass from '@/page/newUser/resetpass.vue'

// import FundingDetails from '@/page/newUser/FundingDetails.vue'
// import xieyiMianze from '@/page/newUser/xieyiMianze.vue'

// import Jijin from '@/page/home/<USER>' // 首页基金
// import College from '@/page/home/<USER>' // 首页学院
// import About from '@/page/newUser/about.vue' // 我的关于我们等详情
// import DragonTiger from '@/page/home/<USER>' // 首页龙虎榜
// import topTen from '@/page/home/<USER>' // 首页十大成交股
// import Daylimit from '@/page/home/<USER>' // 首页每日涨停
// import StopRecovery from '@/page/home/<USER>' // 每日停复牌
// import Subscription from '@/page/home/<USER>' // 新股申购
// import newshares from '@/page/home/<USER>' // 新股申购
// import newsharesDetail from '@/page/home/<USER>/newsharesDetail.vue' // 新股申购详情
// import peishouhistory from '@/page/home/<USER>' // 新股申购
// import biglist from '@/page/home/<USER>' // 大宗交易
// import dazong from '@/page/home/<USER>' // 大宗交易
// import chicang from '@/page/home/<USER>' // 大宗交易
// import chicangDetail from '@/page/home/<USER>' // 大宗交易
// import jiaoyi from '@/page/home/<USER>' // 大宗交易
// import weituoDetail from '@/page/home/<USER>' // 大宗交易

// import sharerecord from '@/page/home/<USER>/sharerecord.vue' // 申购记录
// import zhongqianrecord from '@/page/home/<USER>/zhongqianrecord.vue' // 中签记录
// import qingchouDetail from '@/page/home/<USER>/qingchouDetail.vue' // 新股抢筹详情
// import vipdetail from '@/page/home/<USER>/vipdetail.vue' // vip抢筹详情
// import Setup from '@/page/newUser/setup.vue'
// import buyStock from '@/page/kline/buyStock.vue'
// import smrz from '@/page/newUser/smrz.vue'
// import sharerecordDz from '@/page/home/<USER>/sharerecordDz.vue'

// import bankUpDate from '@/page/bankCard/bankUpDate.vue'
// // 开户合同
// import accountOpeningContract from '@/page/accountOpeningContract/index.vue'
// import silverTransfersInDescription from '@/page/silverTransfersInDescription/index.vue'
Vue.use(Router)

const routerPush = Router.prototype.push
Router.prototype.push = function push(location) {
	return routerPush.call(this, location).catch(error => error)
}
export default new Router({
	routes: [
		{
			path: '/',
			redirect: '/home'
		},
		{
			path: '/home',
			name: 'home',
			meta: {
				title: i18n.t('hj224'),
				requireAuth: true,
				index: 0
			},
			component: () => import(/* webpackChunkName: "home" */ '@/page/home/<USER>')
		},
		{
			path: '/jijin',
			name: 'jijin',
			meta: {
				title: '基金',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "jijin" */ '@/page/home/<USER>')
		},
		{
			path: '/college',
			name: 'college',
			meta: {
				title: '学院',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "college" */ '@/page/home/<USER>')
		},
		{
			path: '/DragonTiger',
			name: 'DragonTiger',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>')
		},
		{
			path: '/topTen',
			name: 'topTen',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>')
		},
		{
			path: '/daylimit',
			name: 'daylimit',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>')
		},
		{
			path: '/stopRecovery',
			name: 'stopRecovery',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>')
		},
		{
			path: '/Subscription',
			name: 'Subscription',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>')
		},
		{
			path: '/newshares',
			name: 'newshares',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>')
		},
		{
			path: '/biglist',
			name: 'biglist',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>')
		},
		{
			path: '/xieyiMianze',
			name: 'xieyiMianze',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "agreement" */ '@/page/newUser/xieyiMianze.vue')
		},
		{
			path: '/weituoDetail',
			name: 'weituoDetail',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "order" */ '@/page/home/<USER>')
		},
		{
			path: '/chicang',
			name: 'chicang',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "position" */ '@/page/home/<USER>')
		},
		{
			path: '/jiaoyi',
			name: 'jiaoyi',
			meta: {
				title: i18n.t('hj224'),
				requireAuth: true,
				index: 0
			},
			component: () => import(/* webpackChunkName: "trade" */ '@/page/home/<USER>')
		},
		{
			path: '/chicangDetail',
			name: 'chicangDetail',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "position" */ '@/page/home/<USER>')
		},
		{
			path: '/dazong',
			name: 'dazong',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "block" */ '@/page/home/<USER>')
		},
		{
			path: '/newsharesDetail',
			name: 'newsharesDetail',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>/newsharesDetail.vue')
		},
		{

			path: '/peishouhistory',
			name: 'peishouhistory',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>')
		},
		{
			path: '/sharerecord',
			name: 'sharerecord',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>/sharerecord.vue')
		},
		{
			path: '/zhongqianrecord',
			name: 'zhongqianrecord',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>/zhongqianrecord.vue')
		},
		{
			path: '/qingchouDetail',
			name: 'qingchouDetail',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>/qingchouDetail.vue')
		},
		{
			path: '/vipdetail',
			name: 'vipdetail',
			meta: {
				title: 'A股',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>/vipdetail.vue')
		},
		{
			path: '/about',
			name: 'about',
			meta: {
				title: '详情',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "about" */ '@/page/newUser/about.vue')
		},
		{
			path: '/buy',
			name: 'buy',
			meta: {
				title: i18n.t('hj237'),
				requireAuth: true,
				hasHeader: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "buy" */ '@/page/home/<USER>')
		},
		{
			path: '/newPage',
			name: 'newPage',
			meta: {
				title: i18n.t('hj238'),
				hasHeader: false,
				is_Show: true,
				show: true,
				index: 2
			},
			component: () => import(/* webpackChunkName: "page" */ '@/page/home/<USER>')
		},
		{
			path: '/newGg',
			name: 'newGg',
			meta: {
				title: i18n.t('hj239'),
				hasHeader: true,
				is_Show: true,
				index: 49
			},
			component: () => import(/* webpackChunkName: "page" */ '@/page/home/<USER>')
		},
		{
			path: '/forget',
			name: 'forget',
			meta: {
				title: i18n.t('hj240'),
				index: 4
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/forget')
		},
		{
			path: '/openaccount',
			name: 'openaccount',
			meta: {
				title: i18n.t('hj241'),
				hasHeader: false,
				show: true,
				index: 6
			},
			component: () => import(/* webpackChunkName: "account" */ '@/page/openaccount')
		},
		{
			path: '/trading-list',
			name: 'TradingList',
			meta: {
				title: i18n.t('hj242'),
				requireAuth: false,
				hasHeader: false,
				index: 7
			},
			component: () => import(/* webpackChunkName: "list" */ '@/page/list/trading-list')
		},
		{
			path: '/indexsearchlist',
			name: '指数查询',
			meta: {
				title: '指数查询',
				index: 8
			},
			component: () => import(/* webpackChunkName: "search" */ '@/page/list/indexlist-search')
		}, {
			path: '/indexlist',
			name: 'indexlist',
			meta: {
				title: '指数列表',
				requireAuth: false,
				index: 9
			},
			component: () => import(/* webpackChunkName: "list" */ '@/page/list/index-list')
		}, {
			path: '/searchlist',
			name: '个股查询',
			meta: {
				title: '个股查询',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 10
			},
			component: () => import(/* webpackChunkName: "search" */ '@/page/list/list-search')
		}, {
			path: '/searchmylist',
			name: 'searchmylist',
			meta: {
				title: '自选查询',
				requireAuth: true,
				hasHeader: false,
				index: 11
			},
			component: () => import(/* webpackChunkName: "search" */ '@/page/list/my-list-search')
		}, {
			path: '/mylist',
			name: 'mylist',
			meta: {
				title: '自选列表',
				requireAuth: true,
				hasHeader: false,
				index: 12

			},
			component: () => import(/* webpackChunkName: "list" */ '@/page/list/my-list')
		}, {
			path: '/listdetail',
			name: 'listdetail',
			meta: {
				title: i18n.t('hj238'),
				requireAuth: false,
				hasHeader: false,
				index: 13
			},
			component: () => import(/* webpackChunkName: "detail" */ '@/page/list/listDetail')
		}, {
			path: '/listdetail2',
			name: 'listdetail2',
			meta: {
				title: i18n.t('hj238'),
				requireAuth: false,
				hasHeader: true,
				index: 14
			},
			component: () => import(/* webpackChunkName: "detail" */ '@/page/list/detail2')
		},
		{
			path: '/indexBuy',
			name: 'indexBuy',
			meta: {
				title: '指数购买',
				requireAuth: false,
				hasHeader: true,
				iconRight: 'search',
				index: 15
			},
			component: () => import(/* webpackChunkName: "buy" */ '@/page/home/<USER>')
		},
		{
			path: '/twoBuy',
			name: 'TwoBuy',
			meta: {
				title: '两融交易',
				requireAuth: false,
				hasHeader: true,
				iconRight: 'search',
				index: 16
			},
			component: () => import(/* webpackChunkName: "buy" */ '@/page/home/<USER>')
		},
		{
			path: '/subWarehouseBuy',
			name: 'SubWarehouseBuy',
			meta: {
				title: '分仓交易',
				requireAuth: false,
				hasHeader: true,
				iconRight: 'search',
				index: 17
			},
			component: () => import(/* webpackChunkName: "buy" */ '@/page/home/<USER>')
		}, {
			path: '/futuresBuy',
			name: 'futuresBuy',
			meta: {
				title: '期货购买',
				requireAuth: false,
				hasHeader: true,
				index: 18
			},
			component: () => import(/* webpackChunkName: "buy" */ '@/page/home/<USER>')
		}, {
			path: '/inquiry',
			name: 'inquiry',
			meta: {
				title: '询价',
				requireAuth: true,
				index: 19
			},
			component: () => import(/* webpackChunkName: "trade" */ '@/page/home/<USER>')
		},
		{
			path: '/transfer',
			name: 'transfer',
			meta: {
				title: '资金互转',
				requireAuth: true,
				index: 21
			},
			component: () => import(/* webpackChunkName: "transfer" */ '@/page/user/transfer')
		},
		{
			path: '/warehouse',
			name: 'Warehouse',
			meta: {
				title: i18n.t('hj2'),
				requireAuth: false,
				hasHeader: false,
				index: 22
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/user/Warehouse.vue')
		},
		{
			path: '/holdorderlist',
			name: 'holdorderlist',
			meta: {
				title: '查询持仓',
				requireAuth: true,
				hasHeader: true,
				index: 23
			},
			component: () => import(/* webpackChunkName: "search" */ '@/page/user/search-order/hold-stockCode')
		}, {
			path: '/holdorderlist2',
			name: 'holdorderlist2',
			meta: {
				title: '查询持仓',
				requireAuth: true,
				hasHeader: true,
				index: 24
			},
			component: () => import(/* webpackChunkName: "search" */ '@/page/user/search-order/hold-stockSpell')
		}, {
			path: '/sellorderlist',
			name: 'sellorderlist',
			meta: {
				title: '查询平仓',
				requireAuth: true,
				hasHeader: true,
				index: 25
			},
			component: () => import(/* webpackChunkName: "search" */ '@/page/user/search-order/sell-stockCode')
		}, {
			path: '/sellorderlist2',
			name: 'sellorderlist2',
			meta: {
				title: '查询平仓',
				requireAuth: true,
				hasHeader: true,
				index: 26
			},
			component: () => import(/* webpackChunkName: "search" */ '@/page/user/search-order/sell-stockSpell')
		}, {
			path: '/detail',
			name: 'detail',
			meta: {
				title: '资金明细',
				requireAuth: true,
				hasHeader: true,
				index: 27
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/user/detail')
		}, {
			path: '/card',
			name: 'card',
			meta: {
				title: '银行卡',
				requireAuth: true,
				hasHeader: true,
				index: 28
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/user/card')
		}, {
			path: '/authentication',
			name: 'authentication',
			meta: {
				title: '认证',
				requireAuth: true,
				hasHeader: true,
				index: 29
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/user/authentication')
		}, {
			path: '/aggre',
			name: 'aggre',
			meta: {
				title: '合作协议',
				requireAuth: true,
				index: 30
			},
			component: () => import(/* webpackChunkName: "agreement" */ '@/page/user/agreement')
		},
		{
			path: '/rechargeSure',
			name: 'rechargeSure',
			meta: {
				title: '确认充值',
				requireAuth: true,
				hasHeader: true,
				index: 32

			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/user/recharge-sure')
		}, {
			path: '/rechargelist',
			name: 'rechargelist',
			meta: {
				title: '银证记录',
				requireAuth: true,
				hasHeader: true,
				index: 33
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/user/rechargelist')
		}, {
			path: '/cash',
			name: 'cash',
			meta: {
				title: '提现',
				requireAuth: true,
				hasHeader: true,
				index: 34
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/user/cash')
		}, {
			path: '/addCard',
			name: 'addCard',
			meta: {
				title: '添加银行卡',
				requireAuth: true,
				hasHeader: true,
				index: 35

			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/user/addCard')
		}, {
			path: '/cashlist',
			name: 'cashlist',
			meta: {
				title: '提现记录',
				requireAuth: true,
				hasHeader: true,
				index: 36

			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/user/cashlist')
		}, {
			path: '/setting',
			name: 'setting',
			meta: {
				title: '设置',
				requireAuth: true,
				index: 37,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/user/my')
		}, {
			path: '/agree',
			name: 'agree',
			meta: {
				title: '注册协议',
				requireAuth: true,
				index: 38
			},
			component: () => import(/* webpackChunkName: "agreement" */ '@/page/user/agreement')
		}, {
			path: '/trade',
			name: 'trade',
			meta: {
				title: '交易风险揭示书',
				requireAuth: true,
				index: 39
			},
			component: () => import(/* webpackChunkName: "trade" */ '@/page/tradeAgree')
		},
		{
			path: '/alertdetail',
			name: 'alertdetail',
			meta: {
				title: '公告详情',
				requireAuth: true,
				index: 40
			},
			component: () => import(/* webpackChunkName: "trade" */ '@/page/home/<USER>/alert')
		},
		{
			path: '/funds',
			name: 'funds',
			meta: {
				title: '配资主页',
				requireAuth: true,
				hasHeader: true,
				iconRight: 'setting',
				index: 41
			},
			component: () => import(/* webpackChunkName: "funds" */ '../page/funds/index')
		},
		{
			path: '/days',
			name: 'days',
			meta: {
				title: '按天配资',
				requireAuth: true,
				hasHeader: true,
				iconRight: 'setting',
				index: 42
			},
			component: () => import(/* webpackChunkName: "funds" */ '../page/funds/days')
		},
		{
			path: '/searchStock',
			name: 'searchStock',
			meta: {
				title: '查询股票',
				requireAuth: true,
				hasHeader: true,
				index: 44
			},
			component: () => import(/* webpackChunkName: "search" */ '../page/list/search')
		},
		{
			path: '/notify',
			name: 'notify',
			meta: {
				title: '消息记录',
				requireAuth: true,
				hasHeader: true,
				index: 45
			},
			component: () => import(/* webpackChunkName: "user" */ '../page/user/notify')
		}, {
			path: '/fundslist',
			name: 'fundslist',
			meta: {
				title: '分仓配资',
				requireAuth: false,
				hasHeader: true,
				index: 46
			},
			component: () => import(/* webpackChunkName: "funds" */ '@/page/funds/funds-list')
		},
		{
			path: '/login',
			name: 'newLogin',
			meta: {
				title: i18n.t('hj248'),
				requireAuth: false,
				hasHeader: false,
				index: 47,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/login/login.vue')
		},
		{
			path: '/register',
			name: 'newRegister',
			meta: {
				title: i18n.t('hj249'),
				requireAuth: false,
				hasHeader: false,
				index: 48,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/login/register.vue')
		},
		{
			path: '/kline',
			name: 'kline',
			meta: {
				title: i18n.t('hj238'),
				requireAuth: false,
				hasHeader: false,
				index: 49,
				show: true
			},
			component: () => import(/* webpackChunkName: "chart" */ '@/page/kline/index.vue')
		},
		{
			path: '/TradingBuy',
			name: 'TradingBuy',
			meta: {
				title: i18n.t('hj237'),
				requireAuth: false,
				hasHeader: false,
				index: 50,
				show: true
			},
			component: () => import(/* webpackChunkName: "trade" */ '@/page/trading/buy.vue')
		},
		{
			path: '/User',
			name: 'NewUser',
			meta: {
				title: i18n.t('hj243'),
				requireAuth: false,
				hasHeader: false,
				index: 51
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/newUser/index.vue')
		},
		{
			path: '/wallet',
			name: 'Wallet',
			meta: {
				title: i18n.t('hj244'),
				requireAuth: false,
				hasHeader: false,
				index: 52
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/wallet/index.vue')
		},
		{
			path: '/transferRecord',
			name: 'transferRecord',
			meta: {
				title: i18n.t('hj168'),
				requireAuth: false,
				hasHeader: false,
				index: 53,
				show: true
			},
			component: () => import(/* webpackChunkName: "transfer" */ '@/page/transferRecord/index.vue')
		},
		{
			path: '/cashWithdrawalRecord',
			name: 'cashWithdrawalRecord',
			meta: {
				title: i18n.t('hj162'),
				requireAuth: false,
				hasHeader: false,
				index: 54,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/cashWithdrawalRecord/index.vue')
		},
		{
			path: '/transfers',
			name: 'transfers',
			meta: {
				title: i18n.t('hj245'),
				requireAuth: false,
				hasHeader: false,
				index: 55,
				show: true
			},
			component: () => import(/* webpackChunkName: "transfer" */ '@/page/transfer/index.vue')
		},
		{
			path: '/authentications',
			name: 'authentications',
			meta: {
				title: i18n.t('hj246'),
				requireAuth: false,
				hasHeader: false,
				index: 56,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/authentication/index.vue')
		},
		{
			path: '/bankCard',
			name: 'bankCard',
			meta: {
				title: i18n.t('hj247'),
				requireAuth: false,
				hasHeader: false,
				index: 57,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/bankCard/index.vue')
		},
		{
			path: '/service',
			name: 'service',
			meta: {
				title: '客服',
				requireAuth: false,
				hasHeader: false,
				index: 57,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/service/service.vue')
		},

		{
			path: '/recharge',
			name: 'recharge',
			meta: {
				title: '充值',
				requireAuth: false,
				hasHeader: false,
				index: 58,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/newUser/recharge.vue')
		},

		{
			path: '/rechargePay',
			name: 'rechargePay',
			meta: {
				title: '支付',
				requireAuth: false,
				hasHeader: false,
				index: 58,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/newUser/rechargePay.vue')
		},

		{
			path: '/withdraw',
			name: 'withdraw',
			meta: {
				title: '提现',
				requireAuth: false,
				hasHeader: false,
				index: 59,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/newUser/withdraw.vue')
		},
		{
			path: '/loginPassword',
			name: 'loginPassword',
			meta: {
				title: '修改密码',
				requireAuth: false,
				hasHeader: false,
				index: 60,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/newUser/loginPassword.vue')
		},
		{
			path: '/setPassword',
			name: 'setPassword',
			meta: {
				title: '修改密码',
				requireAuth: false,
				hasHeader: false,
				index: 61,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/newUser/setPassword.vue')
		},
		{
			path: '/resetpass',
			name: 'resetpass',
			meta: {
				title: '修改密码',
				requireAuth: false,
				hasHeader: false,
				index: 61,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/newUser/resetpass.vue')
		},
		{
			path: '/FundingDetails',
			name: 'FundingDetails',
			meta: {
				title: '资金明细',
				requireAuth: false,
				hasHeader: false,
				index: 62,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/newUser/FundingDetails.vue')
		},
		{
			path: '/setup',
			name: 'setup',
			meta: {
				title: '系统设置',
				requireAuth: false,
				hasHeader: false,
				index: 63,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/newUser/setup.vue')
		},
		{
			path: '/buyStocks',
			name: 'buyStocks',
			meta: {
				title: '买入',
				requireAuth: false,
				hasHeader: false,
				index: 64,
				show: true
			},
			component: () => import(/* webpackChunkName: "buy" */ '@/page/kline/buyStock.vue')
		},
		{
			path: '/smrz',
			name: 'smrz',
			meta: {
				title: '实名认证',
				requireAuth: false,
				hasHeader: false,
				index: 65,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/newUser/smrz.vue')
		},
		{
			path: '/bankUpDate',
			name: 'bankUpDate',
			meta: {
				title: '实名认证',
				requireAuth: false,
				hasHeader: false,
				index: 66,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/bankCard/bankUpDate.vue')
		},
		{
			path: '/accountOpeningContract',
			name: 'accountOpeningContract',
			meta: {
				title: '开户合同',
				requireAuth: false,
				hasHeader: false,
				index: 66,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/accountOpeningContract/index.vue')
		},
		{
			path: '/silverTransfersInDescription',
			name: 'silverTransfersInDescription',
			meta: {
				title: '开户合同',
				requireAuth: false,
				hasHeader: false,
				index: 66,
				show: true
			},
			component: () => import(/* webpackChunkName: "user" */ '@/page/silverTransfersInDescription/index.vue')
		},
		{
			path: '/sharerecordDz',
			name: 'sharerecordDz',
			meta: {
				title: '交易记录',
				requireAuth: false,
				hasHeader: false,
				index: 66,
				show: true
			},
			component: () => import(/* webpackChunkName: "stock" */ '@/page/home/<USER>/sharerecordDz.vue')
		},
		{
			path: '/VipSearchlist',
			name: 'VipSearchlist',
			meta: {
				title: '抢筹搜索',
				requireAuth: false,
				hasHeader: false,
				index: 67,
				show: true
			},
			component: () => import(/* webpackChunkName: "search" */ '@/page/list/list-searchVip')
		},
		{
			path: '/speedtest',
			name: 'speedtest',
			meta: {
				title: '网络测速',
				requireAuth: true,
				hasHeader: false,
				show: true,
				index: 1
			},
			component: () => import(/* webpackChunkName: "speedtest" */ '@/page/speedtest/index.vue')
		},
		{
			// 会匹配所有路径
			path: '*',
			redirect: '/home'
		}
	]
})
