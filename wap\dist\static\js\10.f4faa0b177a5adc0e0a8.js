webpackJsonp([10],{"00NK":function(t,s){},"7QtP":function(t,s){},TruT:function(t,s,e){"use strict";Object.defineProperty(s,"__esModule",{value:!0});var i=e("Xxa5"),a=e.n(i),n=e("exGp"),r=e.n(n),c=e("mvHQ"),v=e.n(c),d=e("c2Ch"),l=e("Au9i"),o=e("mtWM"),_=e.n(o),u={data:function(){return{type:1,itemIndex:1,list:[],pageNum:1,finished:!1,loading:!1,source:""}},mounted:function(){this.$route.query.type&&(this.type=this.$route.query.type),this.changeItemIndex(this.type-1)},methods:{parseNumber:function(t){return parseFloat(t).toFixed(2)},chicangDetail:function(t){this.$router.push({path:"/chicangDetail?type=dazong&item="+v()(t)})},changeItemIndex:function(t){this.list=[],this.pageNum=1,this.itemIndex=t,this.finished=!1,0==this.itemIndex&&this.getOrderList(0),1==this.itemIndex&&this.getOrderList(1),2==this.itemIndex&&this.getOrderList(2)},getOrderList:function(t){var s=this;s.source&&s.source.cancel("close request"),s.source=_.a.CancelToken.source(),s.loading=!0;var e={state:t,stockCode:"",stockSpell:"",pageNum:this.pageNum,pageSize:15};d.T(e,{cancelToken:s.source.token}).then(function(t){t.data.list.length<15&&(s.finished=!0);for(var e=0;e<t.data.list.length;e++)s.list.push(t.data.list[e]);s.loading=!1,s.pageNum++})},withdrawOrder:function(t){var s=this;if(0!=t.backStatus)return!1;var e={id:t.id,userId:t.userId};l.MessageBox.confirm("确定撤单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(r()(a.a.mark(function t(){var i;return a.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,d.s(e);case 2:0===(i=t.sent).status?(Object(l.Toast)({message:i.data,type:"success"}),s.changeItemIndex(3)):Object(l.Toast)({message:i.data,type:"error"});case 4:case"end":return t.stop()}},t,s)})))}}},m={render:function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"container"},[e("div",{staticClass:"header"},[e("van-nav-bar",{attrs:{title:"我的交易","left-arrow":"",fixed:""},on:{"click-left":function(s){return t.$router.go(-1)}}})],1),t._v(" "),e("div",{staticClass:"menu"},[e("div",{class:"item "+(0==t.itemIndex?"active":""),on:{click:function(s){return t.changeItemIndex(0)}}},[t._m(0)]),t._v(" "),e("div",{class:"item "+(1==t.itemIndex?"active":""),on:{click:function(s){return t.changeItemIndex(1)}}},[t._m(1)]),t._v(" "),e("div",{class:"item "+(2==t.itemIndex?"active":""),on:{click:function(s){return t.changeItemIndex(2)}}},[t._m(2)])]),t._v(" "),0==t.itemIndex?e("div",{staticClass:"list"},[t._m(3),t._v(" "),e("div",{staticClass:"list_container"},[e("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(s){return t.getOrderList(0)}},model:{value:t.loading,callback:function(s){t.loading=s},expression:"loading"}},[t._l(t.list,function(s){return[e("div",{key:s.id,staticClass:"item",on:{click:function(e){return t.chicangDetail(s)}}},[e("div",{staticClass:"ebox",staticStyle:{"justify-content":"left"}},[e("div",{staticClass:"stock"},[e("div",{staticClass:"name"},[t._v(t._s(s.stockName))]),t._v(" "),e("div",{staticClass:"child"},[e("div",{staticClass:"tag"},[t._v("深")]),t._v(" "),e("div",[t._v(t._s(s.stockCode))])])])]),t._v(" "),e("div",{staticClass:"cbox"},[e("span",[t._v(t._s(t.parseNumber(s.buyNum)))]),t._v(" "),e("span",[t._v(t._s(t.parseNumber(s.buyPrice)))])]),t._v(" "),e("div",{staticClass:"cbox"},[e("span",[t._v(t._s(t.parseNumber(s.now_price)))]),t._v(" "),e("span",[t._v(t._s(t.parseNumber(s.buyOrderPrice)))])]),t._v(" "),e("div",{class:"cbox "+(s.profitAndLossRatio>0?"red":"green")},[e("span",[t._v(t._s(t.parseNumber(s.profitAndLose)))]),t._v(" "),e("span",[t._v(t._s(t.parseNumber(s.profitAndLossRatio))+"%")])])])]})],2)],1)]):t._e(),t._v(" "),1==t.itemIndex?e("div",{staticClass:"list"},[t._m(4),t._v(" "),e("div",{staticClass:"list_container"},[e("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(s){return t.getOrderList(1)}},model:{value:t.loading,callback:function(s){t.loading=s},expression:"loading"}},[t._l(t.list,function(s){return[e("div",{key:s.id,staticStyle:{"border-bottom":"solid 1px rgba(223, 223, 223, 1)","padding-bottom":"0.3488rem"},on:{click:function(e){return t.chicangDetail(s)}}},[e("div",{staticClass:"item",staticStyle:{border:"none"}},[e("div",{staticClass:"ebox",staticStyle:{"justify-content":"left"}},[e("div",{staticClass:"stock"},[e("div",{staticClass:"name"},[t._v(t._s(s.stockName))]),t._v(" "),e("div",{staticClass:"child"},[e("div",{staticClass:"tag"},[t._v("深")]),t._v(" "),e("div",[t._v(t._s(s.stockCode))])])])]),t._v(" "),e("div",{staticClass:"cbox"},[e("span",[t._v(t._s(t.parseNumber(s.buyNum)))]),t._v(" "),e("span",[t._v(t._s(t.parseNumber(s.buyPrice)))])]),t._v(" "),e("div",{staticClass:"cbox"},[e("span",[t._v(t._s(t.parseNumber(s.buyOrderPrice)))]),t._v(" "),e("span",[t._v(t._s(t.parseNumber(s.sellOrderPrice)))])]),t._v(" "),e("div",{class:"cbox "+(s.profitAndLossRatio>0?"red":"green")},[e("span",[t._v(t._s(t.parseNumber(s.profitAndLose)))]),t._v(" "),e("span",[t._v(t._s(t.parseNumber(s.profitAndLossRatio))+"%")])])]),t._v(" "),e("div",{staticClass:"time"},[e("div",[t._v(t._s(t.dayjs(s.buyOrderTime).format("YYYY-MM-DD HH:mm:ss")))]),t._v(" "),e("div",[t._v(t._s(t.dayjs(s.sellOrderTime).format("YYYY-MM-DD HH:mm:ss")))])]),t._v(" "),e("div",{staticClass:"cbtn"},[t._v("查看详情")])])]})],2)],1)]):t._e(),t._v(" "),2==t.itemIndex?e("div",{staticClass:"weituo_list"},[e("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("hj43")},on:{load:function(s){return t.getOrderList(2)}},model:{value:t.loading,callback:function(s){t.loading=s},expression:"loading"}},[t._l(t.list,function(s){return[e("div",{key:s.id},[e("div",{staticClass:"stock_container"},[e("div",{staticClass:"stock"},[e("div",{staticClass:"name"},[t._v(t._s(s.stockName))]),t._v(" "),e("div",{staticClass:"child"},[s.stockGid.indexOf("sz")>-1?e("div",{staticClass:"tag"},[t._v("深")]):t._e(),t._v(" "),s.stockGid.indexOf("sh")>-1?e("div",{staticClass:"tag"},[t._v("沪")]):t._e(),t._v(" "),s.stockGid.indexOf("bj")>-1?e("div",{staticClass:"tag"},[t._v("北")]):t._e(),t._v(" "),e("div",[t._v(t._s(s.stockCode))])])]),t._v(" "),e("div",[e("span",{staticStyle:{display:"block",background:"#ee0011",color:"#fff",width:"1.8604rem",height:"0.6976rem","text-align":"center","line-height":"0.6976rem","border-radius":"0.3288rem","font-size":"0.3255rem"},on:{click:function(e){return t.withdrawOrder(s)}}},[t._v("撤单")])])]),t._v(" "),e("div",{staticClass:"info"},[e("div",{staticClass:"item"},[e("div",[t._v("买卖类别")]),t._v(" "),e("div",[t._v("证券买入")])]),t._v(" "),e("div",{staticClass:"item"},[e("div",[t._v("当前状态")]),t._v(" "),e("div",[t._v("挂单")])]),t._v(" "),e("div",{staticClass:"item"},[e("div",[t._v("委托手数")]),t._v(" "),e("div",[t._v(t._s(s.orderNum/100))])]),t._v(" "),e("div",{staticClass:"item"},[e("div",[t._v("委托价格")]),t._v(" "),e("div",[t._v(t._s(s.buyOrderPrice))])])])])]})],2)],1):t._e()])},staticRenderFns:[function(){var t=this.$createElement,s=this._self._c||t;return s("div",[s("span",[this._v("我的持仓")]),this._v(" "),s("span")])},function(){var t=this.$createElement,s=this._self._c||t;return s("div",[s("span",[this._v("交易记录")]),this._v(" "),s("span")])},function(){var t=this.$createElement,s=this._self._c||t;return s("div",[s("span",[this._v("我的委托")]),this._v(" "),s("span")])},function(){var t=this.$createElement,s=this._self._c||t;return s("div",{staticClass:"list_title"},[s("div",{staticClass:"item"},[this._v("名称")]),this._v(" "),s("div",{staticClass:"item"},[this._v("持仓 | 市值")]),this._v(" "),s("div",{staticClass:"item"},[this._v("现价 | 成本")]),this._v(" "),s("div",{staticClass:"item"},[this._v("盈亏 | 涨幅")])])},function(){var t=this.$createElement,s=this._self._c||t;return s("div",{staticClass:"list_title"},[s("div",{staticClass:"item"},[this._v("股票 | 代码")]),this._v(" "),s("div",{staticClass:"item"},[this._v("本金 | 数量")]),this._v(" "),s("div",{staticClass:"item"},[this._v("买入 | 卖出价")]),this._v(" "),s("div",{staticClass:"item"},[this._v("收益 | 涨幅")])])}]};var h=e("VU/8")(u,m,!1,function(t){e("7QtP")},"data-v-a248627a",null);s.default=h.exports},UXuT:function(t,s,e){"use strict";Object.defineProperty(s,"__esModule",{value:!0});var i=e("Xxa5"),a=e.n(i),n=e("exGp"),r=e.n(n),c=e("c2Ch"),v=e("Au9i"),d={data:function(){return{currentItem:{}}},mounted:function(){this.currentItem=JSON.parse(decodeURIComponent(this.$route.query.item)),console.log(this.currentItem)},methods:{parseNumber:function(t){return parseFloat(t).toFixed(2)},getpingcang:function(t){var s=this,e=this;if(null==t.sellOrderId){v.MessageBox.confirm(this.$t("hj139")+"?",this.$t("hj165"),{confirmButtonText:this.$t("hj161"),cancelButtonText:this.$t("hj106")}).then(r()(a.a.mark(function i(){var n,r;return a.a.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return n={positionSn:t.positionSn},i.next=3,c._29(n);case 3:0===(r=i.sent).status?(Object(v.Toast)(r.msg),e.$router.go(-1)):r.msg.indexOf("不在交易时段内")>-1?Object(v.Toast)(s.$t("hj140")):Object(v.Toast)(r.msg);case 5:case"end":return i.stop()}},i,s)}))).catch(function(t){Object(v.Toast)(t.toString())})}}}},l={render:function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"container"},[e("div",{staticClass:"header"},[e("van-nav-bar",{attrs:{title:"持仓详情","left-arrow":"",fixed:""},on:{"click-left":function(s){return t.$router.go(-1)}}})],1),t._v(" "),e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("股票代码")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.currentItem.stockCode))])]),t._v(" "),e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("股票名称")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.currentItem.stockName))])]),t._v(" "),e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("持股数")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.currentItem.buyNum))])]),t._v(" "),e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("买入价格")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.currentItem.buyOrderPrice))])]),t._v(" "),t.currentItem.sellOrderId?e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("卖出价格")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.currentItem.sellOrderPrice))])]):t._e(),t._v(" "),e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("买入市值")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.currentItem.buyPrice))])]),t._v(" "),e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("手续费")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.currentItem.orderFee))])]),t._v(" "),e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("印花税")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.currentItem.orderSpread))])]),t._v(" "),e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("盈亏")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.parseNumber(t.currentItem.profitAndLose)))])]),t._v(" "),e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("盈亏比例")]),t._v(" "),e("div",{class:"enr "+(t.currentItem.profitAndLossRatio>0?"red":"green")},[t._v(t._s(t.parseNumber(t.currentItem.profitAndLossRatio))+"%")])]),t._v(" "),e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("买入时间")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.dayjs(t.currentItem.buyOrderTime).format("YYYY-MM-DD HH:mm:ss")))])]),t._v(" "),t.currentItem.sellOrderId?e("div",{staticClass:"ebox"},[e("div",{staticClass:"elabel"},[t._v("卖出时间")]),t._v(" "),e("div",{staticClass:"enr"},[t._v(t._s(t.dayjs(t.currentItem.sellOrderTime).format("YYYY-MM-DD HH:mm:ss")))])]):t._e(),t._v(" "),t.currentItem.sellOrderId?e("div",{staticClass:"ebtn",on:{click:function(s){return t.$router.go(-1)}}},[t._v("返回")]):e("div",{staticClass:"ebtn",on:{click:function(s){return t.getpingcang(t.currentItem)}}},[t._v("我要平仓")])])},staticRenderFns:[]};var o=e("VU/8")(d,l,!1,function(t){e("00NK")},"data-v-78c3babc",null);s.default=o.exports}});