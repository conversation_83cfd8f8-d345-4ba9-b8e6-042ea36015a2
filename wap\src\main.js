import Vue from 'vue'
import App from './App'
import router from './router'
import Icon from 'vue-svg-icon/Icon.vue'
import Mint from 'mint-ui'
// import Resource from 'vue-resource' // 已卸载用axios代替
import store from './store/index'
import axios from './axios/index'
import ElementUI from 'element-ui'
import VueClipboard from 'vue-clipboard2' // 复制
import state from './event'
import Vant, { Toast, Dialog, Swipe, SwipeItem, Skeleton, Switch, Notify, Tab, Tabs, Popup, DatetimePicker } from 'vant'
import 'vant/lib/index.css'

import i18n from '@/locales'
import './assets/css/style.css'

// VConsole
// import VConsole from 'vconsole';
// const vConsole = new VConsole();

import 'bootstrap/dist/css/bootstrap.min.css'
import 'mint-ui/lib/style.css'
import 'element-ui/lib/theme-chalk/index.css'
import 'lib-flexible'
// import md5 from 'js-md5'
import * as filters from '@/utils/utils'
import animated from 'animate.css' // npm install animate.css --save安装，在引入

import '../static/css/public2.css'

import echarts from 'echarts'
import dayjs from 'dayjs'
// // 设置title
// router.beforeEach((to, from, next) => {
//   if (to.meta.title) {//如果设置标题，拦截后设置标题
//     if(store.state && store.state.siteInfo && store.state.siteInfo.siteName){
//       document.title =  store.state.siteInfo.siteName + '-' + to.meta.title
//     }else{
//       document.title =  to.meta.title
//     }
//   }
//   next()
// })
/* eslint-disable no-new */

import posthog from 'posthog-js'

// import { Toast } from "mint-ui";

Vue.prototype.dayjs = dayjs
Vue.prototype.$echarts = echarts
// import VueTouch from 'vue-touch'
Vue.use(Swipe)
Vue.use(SwipeItem)
Vue.use(Skeleton)
Vue.use(animated)
Vue.use(ElementUI)
Vue.use(VueClipboard)

// Vue.use(VueTouch, { name: 'v-touch' })
// // Vue.prototype.$md5 = md5
// VueTouch.config.swipe = {
//   threshold: 100 // 手指左右滑动距离
// }

Vue.use(Vant)
Vue.use(Mint)
Vue.use(Tab)
Vue.prototype._i18n = i18n
Vue.use(Tabs, Popup, DatetimePicker, Switch, Notify)
Vue.component('icon', Icon)
Vue.config.productionTip = false
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key])
})
Vue.prototype.$state = state
Vue.prototype.$setgoindex = function () {
  if (window.history.length <= 1) {
    if (location.href.indexOf('?') === -1) {
      window.location.href = location.href + '?goindex=true'
    } else if (
      location.href.indexOf('?') !== -1 &&
      location.href.indexOf('goindex') === -1
    ) {
      window.location.href = location.href + '&goindex=true'
    }
  }
}
Vue.prototype.setCookie = function (name, value, day) {
  if (day !== 0) {
    // 当设置的时间等于0时，不设置expires属性，cookie在浏览器关闭后删除
    var curDate = new Date()
    var curTamp = curDate.getTime()
    var curWeeHours = new Date(curDate.toLocaleDateString()).getTime() - 1
    var passedTamp = curTamp - curWeeHours
    var leftTamp = 24 * 60 * 60 * 1000 - passedTamp
    var leftTime = new Date()
    leftTime.setTime(leftTamp + curTamp)
    document.cookie =
      name + '=' + escape(value) + ';expires=' + leftTime.toGMTString()
  } else {
    document.cookie = name + '=' + escape(value)
  }
}
Vue.prototype.getCookie = function (name) {
  var arr
  var reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
  arr = document.cookie.match(reg)
  if (arr) {
    return unescape(arr[2])
  } else {
    return null
  }
  // document.cookie = name + "=" + escape(value);
}
Vue.prototype.clearCookie = function () {
  this.setCookie('USER_TOKEN', '', -1)
}
Vue.prototype.checkCookie = function () {
  var user = this.getCookie('USER_TOKEN')
  if (user !== '') {
    alert('Welcome again ' + user)
  } else {
    user = prompt('Please enter your name:', '')
    if (user !== '' && user != null) {
      this.setCookie('USER_TOKEN', user, 365)
    }
  }
}
// router.beforeEach((to, from, next) => {
// console.log(to.path)
// store.state.select = to.path
// document.title = to.meta.title || '亿点通'
// // 判断是否登录
// console.log(document.cookie)
// // console.log(checkCookie(),'checkCookie()')
// if(!to.meta.requireAuth){
// next()
// return
// }
// if (document.cookie && to.meta.requireAuth) {
// if (to.path === '/login') {
// next({ path: '/' })
// } else {
// if (!to.query.url && from.query.url) {
// to.query.url = from.query.url
// }
// next()
// }
// }else{
// if (to.path === '/login') {
// next()
// } else {
// next({ path: '/login' })
// }
// }
// })
const whitelist = ['/login', '/register', '/xieyiMianze', '/service']
router.beforeEach((to, from, next) => {
  store.state.select = to.path
  document.title = to.meta.title
  const token = window.localStorage.getItem('USERTOKEN')
  if (!token && !whitelist.includes(to.path)) {
    next({ path: '/login' })
  } else {
    next()
  }
  // next()
  // if (!to.query.url && from.query.url) {
  //   to.query.url = from.query.url
  // }
})

// 初始化 PostHog
posthog.init('phc_UekjJSQYma7ND6QZrpv94BbQEbfmh4HRZKqLx6QBFEM', {
  api_host: 'https://us.i.posthog.com',
  autocapture: true, // 自动捕获用户交互
  mask_all_inputs: false // 关闭所有输入字段的默认屏蔽
})

// 将 posthog 挂载到 Vue 原型，方便全局使用
Vue.prototype.$posthog = posthog

// 引入 API 配置
import apiConfig from './config/api-config'

// 添加API相关配置到Vue原型，用于全局访问
Vue.prototype.$apiUrls = apiConfig.API_URLS
Vue.prototype.$apiConnectionPoolUrl = apiConfig.CONNECTION_POOL_URL
Vue.prototype.$apiHealthCheckPath = apiConfig.HEALTH_CHECK_PATH
Vue.prototype.$apiHealthCheckResponse = apiConfig.HEALTH_CHECK_RESPONSE

async function initializeBaseUrl() {
  let data = null
  try {
    const connectionPoolUrl = apiConfig.CONNECTION_POOL_URL
    const res = await axios.get(connectionPoolUrl, {
      withCredentials: false
    })
    data = res.data
  } catch (error) {
    console.error('Failed to dynamically set baseURL:', error.message)
  }

  // 使用配置文件中的URL列表
  const urlList = apiConfig.API_URLS

  let urls = [...urlList]
  if (data && data.url && Array.isArray(data.url)) {
    urls = [...urlList, ...data.url]

    // 将动态获取的URL也保存到Vue原型上
    Vue.prototype.$apiUrls = urls
  }

  try {
    let completedCount = 0 // 记录已完成的请求数量
    const results = []

    // 创建所有URL的请求Promise
    const promises = urls.map(async (url) => {
      const startTime = performance.now()
      try {
        const response = await axios.get(`${url}${apiConfig.HEALTH_CHECK_PATH}`, {
          timeout: 20000,
          withCredentials: false
        })

        const endTime = performance.now()
        const responseTime = endTime - startTime
        const isValid = response.data && response.data.data === apiConfig.HEALTH_CHECK_RESPONSE

        // 记录每个URL的测试结果
        results.push({
          url,
          responseTime,
          status: isValid ? '可用' : '不可用',
          isValid
        })

        // 如果有效，返回URL作为候选
        if (isValid) {
          return { url, responseTime }
        }
        throw new Error(`Invalid response data from ${url}`)
      } catch (error) {
        const endTime = performance.now()

        // 记录错误结果
        results.push({
          url,
          responseTime: endTime - startTime,
          status: '不可用',
          error: error.message,
          isValid: false
        })

        completedCount++
        console.error(`Health-check failed for ${url}:`, error.message)

        // 如果所有URL都已经完成且没有找到符合条件的URL，抛出错误
        if (completedCount === urls.length) {
          throw new Error('No valid baseURL available.')
        }

        // 返回一个延迟很长的结果，确保它不会被Promise.race选中
        return { url, responseTime: Infinity }
      }
    })

    // 获取所有结果
    const allResults = await Promise.all(promises)

    // 找出响应最快的有效URL
    const validResults = allResults.filter(result => result.responseTime !== Infinity)
    const fastestResult = validResults.reduce(
      (fastest, current) => current.responseTime < fastest.responseTime ? current : fastest,
      { responseTime: Infinity }
    )

    const fastestUrl = fastestResult.url

    // 排序并保存结果到Vue原型
    const sortedResults = results.sort((a, b) => a.responseTime - b.responseTime)
    Vue.prototype.$apiSpeedTestResults = sortedResults

    if (fastestUrl) {
      // 保存当前使用的URL
      Vue.prototype.$currentBaseUrl = fastestUrl

      // 设置axios默认URL
      axios.defaults.baseURL = fastestUrl
      console.log('Production: Fastest baseURL set to', fastestUrl)
    }
  } catch (error) {
    // 失败时使用第一个URL
    const defaultUrl = urlList[0]
    axios.defaults.baseURL = defaultUrl
    Vue.prototype.$currentBaseUrl = defaultUrl
    console.log('Production: Default baseURL set to', defaultUrl)
    console.error('Error setting baseURL:', error.message)
    Toast.fail('网络不佳!')
  }
}

const isProduction = process.env.NODE_ENV === 'production';

// Vue 初始化逻辑
(async () => {
  // if (isProduction) {
  // 确保连接池逻辑优先完成
  await initializeBaseUrl()
  // }

  new Vue({
    el: '#app',
    i18n,
    store,
    router,
    axios,
    render: (h) => h(App)
  }).$mount('#app')
})()

// new Vue({
//   el: '#app',
//   i18n,
//   store,
//   router,
//   axios,
//   render: h => h(App)
// }).$mount('#app')
