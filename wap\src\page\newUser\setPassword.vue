<template>
    <div class="container">
        <div class="header">
            <van-nav-bar :title="userInfo.hadWithdrawPwd ? '修改资金密码' : '设置资金密码'" left-arrow fixed @click-left="$router.go(-1)" />
        </div>
        <div class="layout">
            <div class="form" v-if="userInfo.hadWithdrawPwd">
                <div class="clabel">旧密码</div>
                <div class="input">
                    <input type="password" placeholder="请输入旧密码" v-model="oldWithPwd" maxlength="6" />
                </div>
            </div>
            <div class="form">
                <div class="clabel">新密码</div>
                <div class="input">
                    <input type="password" placeholder="请输入新密码" v-model="newWithPwd" maxlength="6" />
                </div>
            </div>
            <div class="form">
                <div class="clabel">确认新密码</div>
                <div class="input">
                    <input type="password" placeholder="请输入确认新密码" v-model="cirNewPassword" maxlength="6" />
                </div>
            </div>
            <div class="ebtn" @click="insertWithPwd()">提交</div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { isNull, pwdReg } from "@/utils/utils";
import { Toast } from "vant";

export default {
    data() {
        return {
            userInfo: {},
            oldWithPwd: "",
            newWithPwd: "",
            cirNewPassword: "",
        };
    },
    created() {
        this.getUserInfo();
    },
    methods: {
        async getUserInfo() {
            const toastLoading = Toast.loading({
                message: "加载中...",
                duration: 0,
                forbidClick: true,
            });
            try {
                const data = await api.getUserInfo();
                if (data.status === 0) {
                    this.userInfo = data.data;
                } else {
                    Toast(data.msg);
                    this.$router.push("/login");
                }
            } catch (error) {
                Toast("获取用户信息失败");
            } finally {
                toastLoading.clear();
            }
        },
        async insertWithPwd() {
            // 验证密码格式
            if (this.userInfo.hadWithdrawPwd && isNull(this.oldWithPwd)) {
                Toast("请输入旧密码");
                return;
            }
            if (isNull(this.newWithPwd) || isNull(this.cirNewPassword)) {
                Toast("密码不能为空");
                return;
            }
            if (!/^\d{6}$/.test(this.newWithPwd)) {
                Toast("密码必须为6位纯数字");
                return;
            }
            if (this.newWithPwd !== this.cirNewPassword) {
                Toast("两次密码不一致");
                return;
            }

            try {
                const params = {
                    oldWithPwd: this.userInfo.hadWithdrawPwd
                        ? this.oldWithPwd
                        : undefined,
                    newWithPwd: this.newWithPwd,
                };
                const res = await api.insertWithPwd(params);
                if (res.status === 0) {
                    Toast(res.msg);
                    this.$router.push("/user");
                } else {
                    Toast(res.msg || "修改失败");
                }
            } catch (error) {
                Toast("操作失败，请重试");
            }
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .layout {
        margin-top: 0.3488rem;
        min-height: calc(100vh - 1.4188rem);
        background: #fff;
        .auth-status {
            padding: 0.3488rem;
            text-align: center;

            .status-tag {
                display: inline-block;
                padding: 0.1163rem 0.2326rem;
                border-radius: 0.1163rem;
                font-size: 0.3023rem;
                font-weight: bold;
            }

            .status-pending {
                background-color: #e6e6e6;
                color: #666;
            }

            .status-processing {
                background-color: #e6f7ff;
                color: #1890ff;
                border: 1px solid #91d5ff;
            }

            .status-success {
                background-color: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
            }

            .status-failed {
                background-color: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
            }
        }
        .title {
            font-size: 0.372rem;
            padding-left: 0.3488rem;
            padding-top: 0.3488rem;
        }
        .form {
            padding: 0.3488rem;
            padding-bottom: 0;
            .clabel {
                font-size: 0.372rem;
            }
            .input {
                margin-top: 0.3488rem;
                background: rgba(245, 247, 250, 1);
                height: 1.1162rem;
                border-radius: 0.2325rem;
                padding: 0 0.3488rem;
                input {
                    height: 1.1162rem;
                    width: 100%;
                }
            }
        }
        .sfz {
            width: 9.2115rem;
            background: rgba(245, 247, 250, 1);
            margin: 0 auto;
            margin-top: 0.3488rem;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: justify;
            -ms-flex-pack: justify;
            justify-content: space-between;
            border-radius: 0.1602rem;
            .let {
                width: 4.1652rem;
                padding-left: 0.3488rem;
                // text-align: center;
                h6 {
                    font-size: 0.4005rem;
                    font-weight: 500;
                    color: #000;
                    margin-top: 0.3488rem;
                }
                p {
                    font-size: 0.267rem;
                    font-weight: 500;
                    color: #333;
                    opacity: 0.5;
                    margin-top: 0.3488rem;
                }
            }
            .rih {
                width: 4.1652rem;
                height: 2.6967rem;
                background: url(~@/assets/images/qiquan26/sfz_zheng.png)
                    no-repeat;
                background-size: cover;
                margin-right: 0.2937rem;
                margin-top: 0.3471rem;
                margin-bottom: 0.3471rem;
                position: relative;
                &.fan {
                    background-image: url(~@/assets/images/qiquan26/sfz_fan.png);
                }

                .inp {
                    opacity: 0;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    top: 0;
                }
                img {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    top: 0;
                }
            }
        }
        .ebtn {
            border-radius: 8px;
            background: rgba(215, 12, 24, 1);
            box-shadow: 0px 2px 4px rgba(224, 57, 54, 0.49);
            height: 1.1162rem;
            line-height: 1.1162rem;
            text-align: center;
            color: #fff;
            margin: 0.3488rem;
        }
    }
}
</style>
