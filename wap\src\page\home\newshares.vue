<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="新股申购" left-arrow @click-left="$router.go(-1)" fixed right-text="申购记录" @click-right="$router.push({path: '/peishouhistory?type=1'})"></van-nav-bar>
        </div>
        <div class="list">
            <van-list v-model="loading" :finished="finished" :finished-text="$t('hj43')" @load="getList" :disabled="true" direction="down">
                <div class="item" v-for="value in list" :key="value.id" @click="detail(value)">
                    <div class="flex" style="font-size: 0.3720rem; margin-bottom: 0.3488rem;">
                        <div style="display: flex; align-items: center;">
                            <div class="tag" v-if="value.stockType == '深' || value.stockType == '创'">深</div>
                            <div class="tag" v-if="value.stockType == '沪' || value.stockType == '科'">沪</div>
                            <div class="tag" v-if="value.stockType == '北'">北</div>
                            <span style="margin-left: 0.1162rem;">{{value.name}} [{{value.code}}]</span>
                        </div>
                        <span style="color: #ff7a00;">
                            <svg t="1742904664306" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5629" width="64" height="64">
                                <path d="M327.6 950.8l435.8-436.2-435.8-439-45.4 45.1 390.9 393.8-390.8 391.1z" p-id="5630"></path>
                            </svg>
                        </span>
                    </div>
                    <div class="grid">
                        <div class="grid_flex">
                            <div class="grid_flex_title">发行价格</div>
                            <div>￥{{value.price}}</div>
                        </div>
                        <div class="grid_flex">
                            <div class="grid_flex_title">发行总数</div>
                            <div>{{value.orderNumber}}万股</div>
                        </div>
                        <div class="grid_flex">
                            <div class="grid_flex_title">市盈率</div>
                            <div>{{value.pe}}</div>
                        </div>
                        <div class="grid_flex">
                            <div class="grid_flex_title">申购时间</div>
                            <div>{{dayjs(value.subscribeTime).format('YYYY-MM-DD')}}</div>
                        </div>
                    </div>
                </div>
            </van-list>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";

export default {
    data() {
        return {
            list: [],
            pageNum: 1,
            finished: false,
            loading: false,
        };
    },
    methods: {
        getList() {
            let _this = this;
            var opt = {
                pageNum: this.pageNum,
                pageSize: 9,
            };
            api.getNewGu(opt).then((res) => {
                if (res.data.list.length < 15) {
                    this.finished = true;
                }
                for (let i = 0; i < res.data.list.length; i++) {
                    _this.list.push(res.data.list[i]);
                }
                _this.loading = false;
                _this.pageNum++;
            });
        },
        detail(value) {
            this.$router.push({
                path: "/newsharesdetail?item=" + JSON.stringify(value),
            });
        },
    },
};
</script>


<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .list {
        background: #fff;
        margin-top: 0.3488rem;
        // padding: 0 0.3488rem;
        // padding-top: 0.3488rem;
        min-height: calc(100vh - 1.4188rem);
        .item {
            // box-shadow: 0 0 6px #0003;
            // margin-bottom: 0.3488rem;
            padding: 0.3488rem;
            // padding-bottom: 0;
            border-bottom: 1px solid rgba(245, 247, 250, 1);
            .flex {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-bottom: 0.3488rem;
                .tag {
                    background: rgba(255, 141, 26, 1);
                    font-size: 0.2791rem;
                    color: #fff;
                    width: 0.4651rem;
                    height: 0.4651rem;
                    line-height: 0.4651rem;
                    border-radius: 0.1162rem;
                    text-align: center;
                }
                .icon {
                    width: 0.3488rem;
                    height: 0.3488rem;
                    fill: rgba(181, 181, 181, 1);
                }
            }
            .grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 0.3488rem;
                .grid_flex {
                    display: flex;
                    .grid_flex_title {
                        color: rgba(181, 181, 181, 1);
                        margin-right: 0.3488rem;
                    }
                }
            }
        }
    }
}
</style>