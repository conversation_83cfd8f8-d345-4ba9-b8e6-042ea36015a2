<template>
    <div>
        <div class="heand">
            <div data-v-7793a11f="" class="ert">
                <h2 data-v-7793a11f="">
                    <span data-v-7793a11f="" class="hbnh"><a data-v-7793a11f="" class="fan" @click="$router.go(-1)"></a></span>
                    实名认证
                    <a data-v-7793a11f="" class="xind" style="display: none">新增</a><a data-v-7793a11f="" class="shaux" style="display: none"></a><a data-v-7793a11f="" class="xind" style="display: none">交易记录</a>
                </h2>
            </div>
        </div>
        <ul class="verul">
            <div class="auth-status" v-if="userInfo.isActive !== undefined">
                <div class="status-tag" :class="getStatusClass(userInfo.isActive)">
                    {{ getStatusText(userInfo.isActive) }}
                </div>
            </div>
            <li v-if="userInfo && userInfo.isActive == 3" style="text-align:center; background: #f44036; padding: 0 0.4005rem; min-height: 1.1748rem; line-height: 1.1748rem; color: #fff; margin: 0.4005rem; border-radius: 0.1335rem; margin-top: 0; font-size: 0.4005rem;">
                驳回理由：{{ userInfo.authMsg }}</li>
            <li class="verli">
                <input v-if="showBtn" placeholder="请输入姓名" v-model="form.name" />
                <input placeholder="请输入姓名" v-model="form.name" v-if="!showBtn" readonly />
            </li>
            <li class="verli">
                <input placeholder="请输入身份证号码" v-model="form.idCard" v-if="showBtn" />
                <input placeholder="请输入身份证号码" v-model="form.idCard" v-if="!showBtn" readonly />
            </li>
            <li class="verli">
                <input v-if="showBtn" placeholder="请输入地址" v-model="form.addr" />
                <input placeholder="请输入地址" v-model="form.addr" v-if="!showBtn" readonly />
            </li>
        </ul>
        <div class="sfz">
            <div class="let">
                <h6>身份证正面</h6>
                <p>上传您的身份证正面</p>
            </div>
            <div class="rih">
                <!-- :before-upload="beforeAvatarUpload" -->
                <el-upload :with-credentials="true" class="avatar-uploader" :action="admin + '/user/upload.do'" :http-request="setImg1key" :class="form.img1key ? 'tou' : 'butou'" accept="image/*" list-type="picture-card" name="upload_file" :show-file-list="false" :on-error="handleError" :disabled="!showBtn" :headers="headers">
                    <img v-if="form.img1key" :src="form.img1key" class="id-img avatar" style="width: 100%; height: 100%" v-show="form.img1key" />
                    <i v-else class="iconfont icon-zhaopian"></i>
                    <span v-if="!form.img1key && !imgStatus" class="btn-title">{{
                        $t("hj197")
                    }}</span>
                    <span v-if="imgStatus" class="btn-title">{{ $t("hj198") }}</span>
                </el-upload>
            </div>
        </div>
        <div class="sfz">
            <div class="let">
                <h6>身份证反面</h6>
                <p>上传您的身份证反面</p>
            </div>
            <div class="rih fanmian">
                <!-- <img  :src="imgUrl1" v-show="imgUrl1">
                <input
                     accept="image/*" type="file" class="inp"> -->
                <!-- :before-upload="beforeAvatarUpload2" -->
                <el-upload :with-credentials="true" class="avatar-uploader" :action="admin + '/user/upload.do'" :class="form.img2key ? 'tou' : 'butou'" :http-request="setImg2key" accept="image/*" list-type="picture-card" name="upload_file" :show-file-list="false" :on-error="handleError2" :disabled="!showBtn" :headers="headers">
                    <img v-if="form.img2key" :src="form.img2key" class="id-img avatar" style="width: 100%; height: 100%" />
                    <i v-else class="iconfont icon-zhaopian"></i>
                    <span v-if="!form.img2key && !imgStatus2" class="btn-title">{{
                        $t("hj199")
                    }}</span>
                    <span v-if="imgStatus2" class="btn-title">{{ $t("hj198") }}</span>
                </el-upload>
            </div>
        </div>
        <div class="enter" @click="toSure" v-if="showBtn">提交</div>
    </div>
</template>
<script>
import APIUrl from "@/axios/api.url";
import * as api from "@/axios/api";
import { Toast } from "mint-ui";
import { isNull, idCardReg, isName } from "@/utils/utils";
import { compress } from "@/utils/imgupload";
import * as qiniu from "qiniu-js";
import heic2any from "heic2any";

export default {
    data() {
        return {
            imgUrl: "",
            imgUrl1: "",
            form: {
                phone: "",
                name: "",
                idCard: "",
                img1key: "",
                img2key: "",
                img3key: "",
                addr: "",
            },
            img1Key: "",
            img2Key: "",
            img3Key: "",
            showBtn: true,
            admin: APIUrl.baseURL,
            headers: {
                USERTOKEN: localStorage.getItem("USERTOKEN"),
            },
            imgStatus: false,
            imgStatus2: false,
            userInfo: {},
            messFlag: this.$store.state.userInfo.isActive == 3,
        };
    },
    mounted() {
        this.getUserInfo();

        this.admin = "https://" + window.location.hostname;

        console.log(this.admin);

        //     if (this.$state.theme == "red") {
        //   document.body.classList.remove("black-bg");
        //   document.body.classList.add("red-bg");
        // }
        // this.admin = process.env.API_HOST;

        // //修改getElementsByName('upload_file')的透明度
        // var upload_file = document.getElementsByName('upload_file');
        // for (var i = 0; i < upload_file.length; i++) {
        //   upload_file[i].style.opacity = 0;
        // }
        // if (this.admin == undefined) {
        //   this.admin = "http://*************:8091";
        // }
    },
    methods: {
        async convertToJPEG(file) {
            if (file.type === "image/jpeg") {
                return file; // 如果已经是 JPEG 格式，直接返回
            }

            if (file.type === "image/heif" || file.type === "image/heic") {
                // 如果是 HEIF/HEIC 格式，使用 heic2any 进行转换
                const jpegBlob = await heic2any({
                    blob: file,
                    toType: "image/jpeg",
                });
                return jpegBlob;
            }

            // 使用 canvas 将其他格式（如 PNG、GIF）转换为 JPEG
            return new Promise((resolve, reject) => {
                const img = new Image();
                const url = URL.createObjectURL(file);

                img.onload = function () {
                    const canvas = document.createElement("canvas");
                    const ctx = canvas.getContext("2d");
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    canvas.toBlob(
                        (blob) => {
                            resolve(blob);
                            URL.revokeObjectURL(url);
                        },
                        "image/jpeg",
                        0.92 // 默认 JPEG 质量为 0.92
                    );
                };

                img.onerror = reject;
                img.src = url;
            });
        },

        async compressImageWithCanvas(file, maxWidth, maxHeight, quality) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                const url = URL.createObjectURL(file);

                img.onload = function () {
                    const canvas = document.createElement("canvas");
                    const ctx = canvas.getContext("2d");

                    let width = img.width;
                    let height = img.height;

                    if (width > height) {
                        if (width > maxWidth) {
                            height = Math.round((maxWidth / width) * height);
                            width = maxWidth;
                        }
                    } else {
                        if (height > maxHeight) {
                            width = Math.round((maxHeight / height) * width);
                            height = maxHeight;
                        }
                    }

                    canvas.width = width;
                    canvas.height = height;
                    ctx.drawImage(img, 0, 0, width, height);

                    canvas.toBlob(
                        (blob) => {
                            resolve(blob);
                            URL.revokeObjectURL(url);
                        },
                        "image/jpeg",
                        quality
                    );
                };

                img.onerror = reject;
                img.src = url;
            });
        },
        async upqiniu(file) {
            // 获取上传 Token
            const res = await api.getUploadToken();
            if (res.status === 0) {
                const uploadToken = res.data;

                if (!file) {
                    this.$message.error("请选择文件");
                    return;
                }

                try {
                    // 统一转换为 JPEG 格式
                    const jpegFile = await this.convertToJPEG(file);

                    // 压缩图片
                    const compressedFile = await this.compressImageWithCanvas(
                        jpegFile,
                        1024,
                        1024,
                        0.7
                    );

                    // 使用时间戳生成文件名
                    const timestamp = Date.now();
                    const fileExtension = "jpg"; // 统一为 JPEG 格式
                    const fileName = `${timestamp}.${fileExtension}`; // 生成新的文件名

                    // 上传配置
                    const putExtra = {};
                    const config = {
                        region: qiniu.region.z2, // 使用华南区域
                    };

                    // 上传压缩后的图片
                    const observable = qiniu.upload(
                        compressedFile,
                        fileName,
                        uploadToken,
                        putExtra,
                        config
                    );

                    return new Promise((resolve, reject) => {
                        observable.subscribe({
                            next(res) {
                                console.log(res.total); // 上传过程中会多次触发，可以显示上传进度
                            },
                            error(err) {
                                console.error(err);
                                this.$message.error("文件上传失败");
                                reject(err);
                            },
                            complete(res) {
                                const domain = "https://imgs.wehhdw.cn"; // 你的七牛云存储空间绑定的域名
                                const fileUrl = `${domain}/${res.key}`;
                                console.log(fileUrl);
                                resolve(fileUrl);
                            },
                        });
                    });
                } catch (error) {
                    this.$message.error("图片压缩或转换失败");
                    console.error(error);
                }
            } else {
                this.$message.error("获取上传Token失败");
            }
        },
        async setImg1key(file) {
            console.log(file);
            const url = await this.upqiniu(file.file);
            if (url) {
                this.form.img1key = url;
            }
        },
        async setImg2key(file) {
            console.log(file);
            const url = await this.upqiniu(file.file);
            if (url) {
                this.form.img2key = url;
            }
        },

        // 图片上传
        // async handleChange(e) {
        // this.imgname = e.target.files[0].name.split(".")[0];
        // const files = e.target.files;
        // const data = new FormData();
        // data.append("file", files[0]);
        // let res = await api.uploadimg(data);
        // if (res.status == 200) {
        //     this.imgUrl = APIUrl.url + res.data;
        // }
        // console.log(this.imgUrl)
        // },
        handleAvatarSuccess(res, file) {
            this.imgStatus = false;

            if (res.data.url === "" || !res.data.url.startsWith("http")) {
                this.$message.error(
                    "上传失败请重新上传 返回url:" + res.data.url
                );
                return;
            }

            this.form.img1key = res.data.url;
        },
        beforeAvatarUpload(file) {
            this.imgStatus = true;
            const isJPGOrPNG =
                file.type === "image/jpeg" || file.type === "image/png";

            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isJPGOrPNG) {
                this.$message.error("只许上传 JPG 和 PNG 图片类型!");
                return false;
            }

            if (!isLt10M) {
                this.$message.error(this.$t("hj205"));
                return false;
            } else {
                this.form.img1key = URL.createObjectURL(file);
                compress(file, function (val) {});
            }
        },
        handleError(err, file, fileList) {
            this.imgStatus = false;

            this.$message.error(`文件上传失败: ${file.name}`);

            // 如果 err 是一个 XMLHttpRequest 对象，可以进一步获取响应内容
            if (err && err.response) {
                console.log("响应状态:", err.response.status);
                console.log("响应数据:", err.response.data);
                this.$message.error(`上传失败内容: ${err.response.data}`);
            }
        },

        handleAvatarSuccess2(res, file) {
            this.imgStatus2 = false;
            if (res.data.url === "" || !res.data.url.startsWith("http")) {
                this.$message.error(
                    "上传失败请重新上传 返回url:" + res.data.url
                );
                return;
            }

            this.form.img2key = res.data.url; // URL.createObjectURL(file.raw);
        },
        beforeAvatarUpload2(file) {
            this.imgStatus2 = true;
            // const _that = this
            const isJPGOrPNG =
                file.type === "image/jpeg" || file.type === "image/png";

            if (!isJPGOrPNG) {
                this.$message.error("只许上传 JPG 和 PNG 图片类型!");
                return false;
            }

            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error(this.$t("hj205"));
                return false;
            } else {
                this.form.img2key = URL.createObjectURL(file);
                compress(file, function (val) {});
            }
        },
        handleError2() {
            this.imgStatus2 = false;
            this.$message.error(`文件上传失败: ${file.name}`);

            // 如果 err 是一个 XMLHttpRequest 对象，可以进一步获取响应内容
            if (err && err.response) {
                console.log("响应状态:", err.response.status);
                console.log("响应数据:", err.response.data);
                this.$message.error(`上传失败内容: ${err.response.data}`);
            }
        },
        handleAvatarSuccess3(res, file) {
            this.form.img3key = res.data.url; // URL.createObjectURL(file.raw);
        },
        async getUserInfo() {
            // 获取用户信息
            let data = await api.getUserInfo();
            if (data.status === 0) {
                // 判断是否登录
                this.$store.commit("dialogVisible", false);
                this.$store.state.userInfo = data.data;
                this.userInfo = data.data;
                if (
                    this.$store.state.userInfo.isActive === 1 ||
                    this.$store.state.userInfo.isActive === 2
                ) {
                    this.form.idCard = this.$store.state.userInfo.idCard;
                    this.form.name = this.$store.state.userInfo.realName;
                    this.form.img1key = this.$store.state.userInfo.img1Key;
                    this.form.img2key = this.$store.state.userInfo.img2Key;
                    this.form.addr = this.$store.state.userInfo.addr;
                    //   this.form.img3key = this.$store.state.userInfo.img3Key
                    this.showBtn = false;
                }
            } else {
                // this.$store.commit('dialogVisible',true);
                // 跳转到login
                this.$router.push({ path: "/login" });
            }
        },
        beforeAvatarUpload3(file) {},
        // 上传
        handleFile: function (e) {
            // var that = this
            let $target = e.target || e.srcElement;
            let file = $target.files[0];
            // if(file.size > 1024 * 1024 *20){
            console.log(file, "file");
            let i = false;
            if (i) {
                Toast(this.$t("hj206"));
            } else {
                // Indicator.open('Loading...')
                this.img1Key = file;
                // this.$refs.formDate.submit()
                // this.uploadIdImg({upload_file:file})
                var reader = new FileReader();
                reader.onload = (data) => {
                    let res = data.target || data.srcElement;
                    this.form.img1Key = res.result;
                    // Indicator.close()
                };
                // reader.onloadend = () => {
                //   Indicator.close()
                // }
                reader.readAsDataURL(file);
            }
        },
        toSure() {
            // 实名认证弹框
            if (isNull(this.form.name) || !isName(this.form.name)) {
                Toast(this.$t("hj207"));
            } else if (
                isNull(this.form.idCard) ||
                !idCardReg(this.form.idCard)
            ) {
                Toast(this.$t("hj208"));
            } else if (isNull(this.form.img1key) || isNull(this.form.img2key)) {
                Toast(this.$t("hj209"));
            } else if (isNull(this.form.addr)) {
                Toast("请输入地址");
            } else {
                // 显示确认弹窗
                this.toAuthentication();
            }
        },
        async toAuthentication() {
            let opts = {
                realName: this.form.name,
                idCard: this.form.idCard,
                img1key: this.form.img1key,
                img2key: this.form.img2key,
                img3key: this.form.img3key,
                addr: this.form.addr,
            };
            let data = await api.userAuth(opts);
            if (data.status === 0) {
                Toast(this.$t("hj210"));
                this.goBack();
            } else {
                Toast(data.msg);
            }
        },
        goBack() {
            this.$router.back(-1);
        },
        getStatusText(status) {
            const statusMap = {
                0: "未实名认证",
                1: "实名认证审核中",
                2: "实名认证已通过",
                3: "实名认证未通过",
            };
            return statusMap[status] || "未知状态";
        },
        getStatusClass(status) {
            const classMap = {
                0: "status-pending",
                1: "status-processing",
                2: "status-success",
                3: "status-failed",
            };
            return classMap[status] || "status-pending";
        },
    },
};
</script>
<style lang="less" scoped>
.avatar-uploader {
    overflow: hidden;
}

/deep/ .avatar-uploader .el-upload__input {
    opacity: 0 !important;
}

.heand {
    width: 100%;
    height: 6.141rem;
    background: url(~@/assets/imgRed/beij.cb205259.png) no-repeat 50%;
    background-size: 100%;
}

.ert {
    position: relative;
}

h2 {
    text-align: center;
    height: 1.2549rem;
    width: 100%;
    position: relative;
    line-height: 1.2549rem;
    font-size: 0.4806rem;
    color: #fff;
    background: transparent;
    font-weight: 500;
    z-index: 3;
}

.hbnh {
    position: absolute;
    left: 0.4005rem;
    font-size: 0.4272rem;
    font-weight: 500;
}

.hbnh .fan {
    width: 0.2403rem;
    height: 0.4272rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAAAXNSR0IArs4c6QAAAVdJREFUSEut1usqBVEYxvH/c1MuRJJDkhwTEpIkJUlyCEk++CJJckpycw49Gs3Wa+y9Z82ePd/Xr5n38KwRNR7bE0APMK1OHdtTwAWQGZcdQbZngPMc+QImK0O2Z4GzgIxLuqoE2Z4DTgMyJuk6K08yZHseOAnIqKSbRo2TINsLwHFARiTdxkaVQrYXgaOADEu6K3a7LWR7CTjMD2XdaYq0rZHtFWA/Rz6BIUn3reau6RvZXgX28kMfOfLQbnj/QbbXgN380HuOPJZtwB/I9jqwE5ABSc9lyJ8a2d4AtgPSL+klBfmFbG8CWwHpk/SaivxAtjMgg7Inq0mvpLcqSHehTOvKpzU+oSvFDlj99gesOJCDkp7Kip+yIknT3XL7C0tbum9lMbIMHKQkQEqwJWVSKZTPWTElq0dt6GYxt6uHf8DqX0cBq39BBqz+lR2w+j8RAfv9rfkGqF24CUdT9E4AAAAASUVORK5CYII=)
        no-repeat 50%;
    background-size: 100%;
    display: inline-block;
    margin-right: 0.1335rem;
    vertical-align: middle;
    margin-top: -0.0534rem;
}

.verul {
    width: 100%;
    min-height: 2.136rem;
    margin: 0 auto;
    margin-top: -1.8156rem;
    background: #fff;
    padding-top: 0.801rem;
    border-radius: 0.4005rem 0.4005rem 0 0;
}

.verul .verli {
    width: 9.345rem;
    height: 1.1748rem;
    background: #e6e6e6;
    border-radius: 0.1335rem;
    margin: 0 auto;
    margin-bottom: 0.4005rem;
}

.verli input {
    width: 7.476rem;
    height: 1.1748rem;
    background: transparent;
    font-size: 0.4005rem;
    margin-left: 0.4005rem;
    color: #000;
}

.sfz {
    width: 9.2115rem;
    background: #e6e6e6;
    margin: 0 auto;
    margin-top: 0.4539rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-radius: 0.1602rem;
}

.sfz .let {
    width: 4.1652rem;
    text-align: center;
}

.sfz .let h6 {
    font-size: 0.4005rem;
    font-weight: 500;
    color: #000;
    margin-top: 0.9612rem;
}

.let p {
    font-size: 0.267rem;
    font-weight: 500;
    color: #333;
    opacity: 0.5;
    margin-top: 0.4005rem;
}

.sfz .rih {
    width: 4.1652rem;
    height: 2.6967rem;
    background: url(~@/assets/imgRed/sfzz.0e35404d.png) no-repeat 50%;
    background-size: 100%;
    margin-right: 0.2937rem;
    margin-top: 0.3471rem;
    margin-bottom: 0.3471rem;
    position: relative;
}

.sfz .rih .inp {
    opacity: 0;
}

.sfz .rih .inp,
.sfz .rih img {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
}

.rih .inp,
.sfz .rih img {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
}

.enter {
    width: 9.345rem;
    height: 1.1748rem;
    background: linear-gradient(-55deg, rgb(244, 64, 54), rgb(244, 64, 54));
    border-radius: 0.1335rem;
    text-align: center;
    color: #fff;
    font-size: 0.4272rem;
    line-height: 1.1748rem;
    margin: 0.801rem auto;
}

input::-webkit-input-placeholder {
    color: #999;
}

.tou {
    opacity: 1;
}

.butou {
    opacity: 0;
}

.auth-status {
    margin: 0.3488rem;
    text-align: center;

    .status-tag {
        display: inline-block;
        padding: 0.1163rem 0.2326rem;
        border-radius: 0.1163rem;
        font-size: 0.3023rem;
        font-weight: bold;
    }

    .status-pending {
        background-color: #e6e6e6;
        color: #666;
    }

    .status-processing {
        background-color: #e6f7ff;
        color: #1890ff;
        border: 1px solid #91d5ff;
    }

    .status-success {
        background-color: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
    }

    .status-failed {
        background-color: #fff2f0;
        color: #ff4d4f;
        border: 1px solid #ffccc7;
    }
}
</style>
