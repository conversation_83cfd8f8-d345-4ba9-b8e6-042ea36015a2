<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="实名认证" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>
        <div class="layout">
            <div class="auth-status" v-if="userInfo.isActive !== undefined">
                <div class="status-tag" :class="getStatusClass(userInfo.isActive)">
                    {{ getStatusText(userInfo.isActive) }}
                </div>
            </div>
            <div class="title" style="padding-top: 0;">请完善您的个人信息</div>
            <div class="form">
                <div class="clabel">姓名</div>
                <div class="input">
                    <input v-if="showBtn" placeholder="请输入姓名" v-model="form.name" />
                    <input placeholder="请输入姓名" v-model="form.name" v-if="!showBtn" readonly />
                </div>
            </div>
            <div class="form">
                <div class="clabel">身份证号</div>
                <div class="input">
                    <input placeholder="请输入身份证号码" v-model="form.idCard" v-if="showBtn" />
                    <input placeholder="请输入身份证号码" v-model="form.idCard" v-if="!showBtn" readonly />
                </div>
            </div>
            <div class="form">
                <div class="clabel">地址</div>
                <div class="input">
                    <input v-if="showBtn" placeholder="请输入地址" v-model="form.addr" />
                    <input placeholder="请输入地址" v-model="form.addr" v-if="!showBtn" readonly />
                </div>
            </div>
            <div class="title">请上传身份证的正反面</div>

            <div class="sfz">
                <div class="let">
                    <h6>身份证正面</h6>
                    <p>上传您的身份证正面</p>
                </div>
                <div class="rih">
                    <!-- :before-upload="beforeAvatarUpload" -->
                    <el-upload v-if="showBtn" :with-credentials="true" class="avatar-uploader" :action="admin + '/user/upload.do'" :http-request="setImg1key" :class="form.img1key ? 'tou' : 'butou'" accept="image/*" list-type="picture-card" name="upload_file" :show-file-list="false" :on-error="handleError" :disabled="!showBtn" :headers="headers">
                        <img v-if="form.img1key" :src="form.img1key" class="id-img avatar" style="width: 100%; height: 100%" v-show="form.img1key" />
                        <i v-else class="iconfont icon-zhaopian"></i>
                        <span v-if="!form.img1key && !imgStatus" class="btn-title">{{
                        $t("hj197")
                    }}</span>
                        <span v-if="imgStatus" class="btn-title">{{ $t("hj198") }}</span>
                    </el-upload>
                    <img v-if="!showBtn && form.img1key" :src="form.img1key" class="id-img avatar" style="width: 100%; height: 100%" v-show="form.img1key" />
                </div>
            </div>
            <div class="sfz">
                <div class="let">
                    <h6>身份证反面</h6>
                    <p>上传您的身份证反面</p>
                </div>
                <div class="rih fan">
                    <!-- <img  :src="imgUrl1" v-show="imgUrl1">
                <input
                     accept="image/*" type="file" class="inp"> -->
                    <!-- :before-upload="beforeAvatarUpload2" -->
                    <el-upload v-if="showBtn" :with-credentials="true" class="avatar-uploader" :action="admin + '/user/upload.do'" :class="form.img2key ? 'tou' : 'butou'" :http-request="setImg2key" accept="image/*" list-type="picture-card" name="upload_file" :show-file-list="false" :on-error="handleError2" :disabled="!showBtn" :headers="headers">
                        <img v-if="form.img2key" :src="form.img2key" class="id-img avatar" style="width: 100%; height: 100%" />
                        <i v-else class="iconfont icon-zhaopian"></i>
                        <span v-if="!form.img2key && !imgStatus2" class="btn-title">{{
                        $t("hj199")
                    }}</span>
                        <span v-if="imgStatus2" class="btn-title">{{ $t("hj198") }}</span>
                    </el-upload>
                    <img v-if="!showBtn && form.img2key" :src="form.img2key" class="id-img avatar" style="width: 100%; height: 100%" v-show="form.img2key" />
                </div>
            </div>

            <div class="ebtn" @click="toSure" v-if="showBtn">提交</div>
        </div>
    </div>
</template>

<script>
import APIUrl from "@/axios/api.url";
import * as api from "@/axios/api";
import { Toast } from "mint-ui";
import { isNull, idCardReg, isName } from "@/utils/utils";
import { compress } from "@/utils/imgupload";
import * as qiniu from "qiniu-js";
import heic2any from "heic2any";

export default {
    data() {
        return {
            imgUrl: "",
            imgUrl1: "",
            form: {
                phone: "",
                name: "",
                idCard: "",
                img1key: "",
                img2key: "",
                img3key: "",
                addr: "",
            },
            img1Key: "",
            img2Key: "",
            img3Key: "",
            showBtn: true,
            admin: APIUrl.baseURL,
            headers: {
                USERTOKEN: localStorage.getItem("USERTOKEN"),
            },
            imgStatus: false,
            imgStatus2: false,
            userInfo: {},
            messFlag: this.$store.state.userInfo.isActive == 3,
            qiniuDomain: "",
        };
    },
    mounted() {
        api.getInitConfig().then((res) => {
            this.qiniuDomain = res.data.qiniuDomain;
            this.getUserInfo();
        });

        this.admin = "https://" + window.location.hostname;

        console.log(this.admin);

        //     if (this.$state.theme == "red") {
        //   document.body.classList.remove("black-bg");
        //   document.body.classList.add("red-bg");
        // }
        // this.admin = process.env.API_HOST;

        // //修改getElementsByName('upload_file')的透明度
        // var upload_file = document.getElementsByName('upload_file');
        // for (var i = 0; i < upload_file.length; i++) {
        //   upload_file[i].style.opacity = 0;
        // }
        // if (this.admin == undefined) {
        //   this.admin = "http://*************:8091";
        // }
    },
    methods: {
        async convertToJPEG(file) {
            if (file.type === "image/jpeg") {
                return file; // 如果已经是 JPEG 格式，直接返回
            }

            if (file.type === "image/heif" || file.type === "image/heic") {
                // 如果是 HEIF/HEIC 格式，使用 heic2any 进行转换
                const jpegBlob = await heic2any({
                    blob: file,
                    toType: "image/jpeg",
                });
                return jpegBlob;
            }

            // 使用 canvas 将其他格式（如 PNG、GIF）转换为 JPEG
            return new Promise((resolve, reject) => {
                const img = new Image();
                const url = URL.createObjectURL(file);

                img.onload = function () {
                    const canvas = document.createElement("canvas");
                    const ctx = canvas.getContext("2d");
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    canvas.toBlob(
                        (blob) => {
                            resolve(blob);
                            URL.revokeObjectURL(url);
                        },
                        "image/jpeg",
                        0.92 // 默认 JPEG 质量为 0.92
                    );
                };

                img.onerror = reject;
                img.src = url;
            });
        },

        async compressImageWithCanvas(file, maxWidth, maxHeight, quality) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                const url = URL.createObjectURL(file);

                img.onload = function () {
                    const canvas = document.createElement("canvas");
                    const ctx = canvas.getContext("2d");

                    let width = img.width;
                    let height = img.height;

                    if (width > height) {
                        if (width > maxWidth) {
                            height = Math.round((maxWidth / width) * height);
                            width = maxWidth;
                        }
                    } else {
                        if (height > maxHeight) {
                            width = Math.round((maxHeight / height) * width);
                            height = maxHeight;
                        }
                    }

                    canvas.width = width;
                    canvas.height = height;
                    ctx.drawImage(img, 0, 0, width, height);

                    canvas.toBlob(
                        (blob) => {
                            resolve(blob);
                            URL.revokeObjectURL(url);
                        },
                        "image/jpeg",
                        quality
                    );
                };

                img.onerror = reject;
                img.src = url;
            });
        },
        async upqiniu(file) {
            // 获取上传 Token
            const res = await api.getUploadToken();
            if (res.status === 0) {
                const uploadToken = res.data;

                if (!file) {
                    this.$message.error("请选择文件");
                    return;
                }

                try {
                    // 统一转换为 JPEG 格式
                    const jpegFile = await this.convertToJPEG(file);

                    // 压缩图片
                    const compressedFile = await this.compressImageWithCanvas(
                        jpegFile,
                        1024,
                        1024,
                        0.7
                    );

                    // 使用时间戳生成文件名
                    const timestamp = Date.now();
                    const fileExtension = "jpg"; // 统一为 JPEG 格式
                    const fileName = `${timestamp}.${fileExtension}`; // 生成新的文件名

                    // 上传配置
                    const putExtra = {};
                    const config = {
                        region: qiniu.region.z2, // 使用华南区域
                    };

                    // 上传压缩后的图片
                    const observable = qiniu.upload(
                        compressedFile,
                        fileName,
                        uploadToken,
                        putExtra,
                        config
                    );
                    const domain = this.qiniuDomain;
                    return new Promise((resolve, reject) => {
                        observable.subscribe({
                            next(res) {
                                console.log(res.total); // 上传过程中会多次触发，可以显示上传进度
                            },
                            error(err) {
                                console.error(err);
                                this.$message.error("文件上传失败");
                                reject(err);
                            },
                            complete(res) {
                                const fileUrl = `${domain}/${res.key}`;
                                console.log(fileUrl);
                                resolve(fileUrl);
                            },
                        });
                    });
                } catch (error) {
                    this.$message.error("图片压缩或转换失败");
                    console.error(error);
                }
            } else {
                this.$message.error("获取上传Token失败");
            }
        },
        async setImg1key(file) {
            console.log(file);
            const url = await this.upqiniu(file.file);
            if (url) {
                this.form.img1key = url;
            }
        },
        async setImg2key(file) {
            console.log(file);
            const url = await this.upqiniu(file.file);
            if (url) {
                this.form.img2key = url;
            }
        },

        // 图片上传
        // async handleChange(e) {
        // this.imgname = e.target.files[0].name.split(".")[0];
        // const files = e.target.files;
        // const data = new FormData();
        // data.append("file", files[0]);
        // let res = await api.uploadimg(data);
        // if (res.status == 200) {
        //     this.imgUrl = APIUrl.url + res.data;
        // }
        // console.log(this.imgUrl)
        // },
        handleAvatarSuccess(res, file) {
            this.imgStatus = false;

            if (res.data.url === "" || !res.data.url.startsWith("http")) {
                this.$message.error(
                    "上传失败请重新上传 返回url:" + res.data.url
                );
                return;
            }

            this.form.img1key = res.data.url;
        },
        beforeAvatarUpload(file) {
            this.imgStatus = true;
            const isJPGOrPNG =
                file.type === "image/jpeg" || file.type === "image/png";

            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isJPGOrPNG) {
                this.$message.error("只许上传 JPG 和 PNG 图片类型!");
                return false;
            }

            if (!isLt10M) {
                this.$message.error(this.$t("hj205"));
                return false;
            } else {
                this.form.img1key = URL.createObjectURL(file);
                compress(file, function (val) {});
            }
        },
        handleError(err, file, fileList) {
            this.imgStatus = false;

            this.$message.error(`文件上传失败: ${file.name}`);

            // 如果 err 是一个 XMLHttpRequest 对象，可以进一步获取响应内容
            if (err && err.response) {
                console.log("响应状态:", err.response.status);
                console.log("响应数据:", err.response.data);
                this.$message.error(`上传失败内容: ${err.response.data}`);
            }
        },

        handleAvatarSuccess2(res, file) {
            this.imgStatus2 = false;
            if (res.data.url === "" || !res.data.url.startsWith("http")) {
                this.$message.error(
                    "上传失败请重新上传 返回url:" + res.data.url
                );
                return;
            }

            this.form.img2key = res.data.url; // URL.createObjectURL(file.raw);
        },
        beforeAvatarUpload2(file) {
            this.imgStatus2 = true;
            // const _that = this
            const isJPGOrPNG =
                file.type === "image/jpeg" || file.type === "image/png";

            if (!isJPGOrPNG) {
                this.$message.error("只许上传 JPG 和 PNG 图片类型!");
                return false;
            }

            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error(this.$t("hj205"));
                return false;
            } else {
                this.form.img2key = URL.createObjectURL(file);
                compress(file, function (val) {});
            }
        },
        handleError2() {
            this.imgStatus2 = false;
            this.$message.error(`文件上传失败: ${file.name}`);

            // 如果 err 是一个 XMLHttpRequest 对象，可以进一步获取响应内容
            if (err && err.response) {
                console.log("响应状态:", err.response.status);
                console.log("响应数据:", err.response.data);
                this.$message.error(`上传失败内容: ${err.response.data}`);
            }
        },
        handleAvatarSuccess3(res, file) {
            this.form.img3key = res.data.url; // URL.createObjectURL(file.raw);
        },
        async getUserInfo() {
            // 获取用户信息
            let data = await api.getUserInfo();
            if (data.status === 0) {
                // 判断是否登录
                this.$store.commit("dialogVisible", false);
                this.$store.state.userInfo = data.data;
                this.userInfo = data.data;
                if (
                    this.$store.state.userInfo.isActive === 1 ||
                    this.$store.state.userInfo.isActive === 2
                ) {
                    this.form.idCard = this.$store.state.userInfo.idCard;
                    this.form.name = this.$store.state.userInfo.realName;
                    this.form.img1key = this.$store.state.userInfo.img1Key;
                    this.form.img2key = this.$store.state.userInfo.img2Key;
                    this.form.addr = this.$store.state.userInfo.addr;
                    //   this.form.img3key = this.$store.state.userInfo.img3Key
                    this.showBtn = false;
                }
            } else {
                // this.$store.commit('dialogVisible',true);
                // 跳转到login
                this.$router.push({ path: "/login" });
            }
        },
        beforeAvatarUpload3(file) {},
        // 上传
        handleFile: function (e) {
            // var that = this
            let $target = e.target || e.srcElement;
            let file = $target.files[0];
            // if(file.size > 1024 * 1024 *20){
            console.log(file, "file");
            let i = false;
            if (i) {
                Toast(this.$t("hj206"));
            } else {
                // Indicator.open('Loading...')
                this.img1Key = file;
                // this.$refs.formDate.submit()
                // this.uploadIdImg({upload_file:file})
                var reader = new FileReader();
                reader.onload = (data) => {
                    let res = data.target || data.srcElement;
                    this.form.img1Key = res.result;
                    // Indicator.close()
                };
                // reader.onloadend = () => {
                //   Indicator.close()
                // }
                reader.readAsDataURL(file);
            }
        },
        toSure() {
            // 实名认证弹框
            if (isNull(this.form.name) || !isName(this.form.name)) {
                Toast(this.$t("hj207"));
            } else if (
                isNull(this.form.idCard) ||
                !idCardReg(this.form.idCard)
            ) {
                Toast(this.$t("hj208"));
            } else if (isNull(this.form.img1key) || isNull(this.form.img2key)) {
                Toast(this.$t("hj209"));
            } else if (isNull(this.form.addr)) {
                Toast("请输入地址");
            } else {
                // 显示确认弹窗
                this.toAuthentication();
            }
        },
        async toAuthentication() {
            let opts = {
                realName: this.form.name,
                idCard: this.form.idCard,
                img1key: this.form.img1key,
                img2key: this.form.img2key,
                img3key: this.form.img3key,
                addr: this.form.addr,
            };
            let data = await api.userAuth(opts);
            if (data.status === 0) {
                Toast(this.$t("hj210"));
                this.goBack();
            } else {
                Toast(data.msg);
            }
        },
        goBack() {
            this.$router.back(-1);
        },
        getStatusText(status) {
            const statusMap = {
                0: "未实名认证",
                1: "实名认证审核中",
                2: "实名认证已通过",
                3: "实名认证未通过",
            };
            return statusMap[status] || "未知状态";
        },
        getStatusClass(status) {
            const classMap = {
                0: "status-pending",
                1: "status-processing",
                2: "status-success",
                3: "status-failed",
            };
            return classMap[status] || "status-pending";
        },
    },
};
</script>

<style lang="less" scoped>
.avatar-uploader {
    overflow: hidden;
}

.butou {
    opacity: 0;
}

/deep/ .avatar-uploader .el-upload__input {
    opacity: 0 !important;
}
.container {
    font-size: 0.3256rem;
    padding: 0;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .layout {
        margin-top: 0.3488rem;
        min-height: calc(100vh - 1.4188rem);
        background: #fff;
        .auth-status {
            padding: 0.3488rem;
            text-align: center;

            .status-tag {
                display: inline-block;
                padding: 0.1163rem 0.2326rem;
                border-radius: 0.1163rem;
                font-size: 0.3023rem;
                font-weight: bold;
            }

            .status-pending {
                background-color: #e6e6e6;
                color: #666;
            }

            .status-processing {
                background-color: #e6f7ff;
                color: #1890ff;
                border: 1px solid #91d5ff;
            }

            .status-success {
                background-color: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
            }

            .status-failed {
                background-color: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
            }
        }
        .title {
            font-size: 0.372rem;
            padding-left: 0.3488rem;
            padding-top: 0.3488rem;
        }
        .form {
            padding: 0.3488rem;
            padding-bottom: 0;
            .clabel {
                font-size: 0.372rem;
            }
            .input {
                margin-top: 0.3488rem;
                background: rgba(245, 247, 250, 1);
                height: 1.1162rem;
                border-radius: 0.2325rem;
                padding: 0 0.3488rem;
                input {
                    height: 1.1162rem;
                    width: 100%;
                }
            }
        }
        .sfz {
            width: 9.2115rem;
            background: rgba(245, 247, 250, 1);
            margin: 0 auto;
            margin-top: 0.3488rem;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: justify;
            -ms-flex-pack: justify;
            justify-content: space-between;
            border-radius: 0.1602rem;
            .let {
                width: 4.1652rem;
                padding-left: 0.3488rem;
                // text-align: center;
                h6 {
                    font-size: 0.4005rem;
                    font-weight: 500;
                    color: #000;
                    margin-top: 0.3488rem;
                }
                p {
                    font-size: 0.267rem;
                    font-weight: 500;
                    color: #333;
                    opacity: 0.5;
                    margin-top: 0.3488rem;
                }
            }
            .rih {
                width: 4.1652rem;
                height: 2.6967rem;
                background: url(~@/assets/images/qiquan26/sfz_zheng.png)
                    no-repeat;
                background-size: cover;
                margin-right: 0.2937rem;
                margin-top: 0.3471rem;
                margin-bottom: 0.3471rem;
                position: relative;
                &.fan {
                    background-image: url(~@/assets/images/qiquan26/sfz_fan.png);
                }

                .inp {
                    opacity: 0;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    top: 0;
                }
                img {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    top: 0;
                }
            }
        }
        .ebtn {
            border-radius: 8px;
            background: rgba(215, 12, 24, 1);
            box-shadow: 0px 2px 4px rgba(224, 57, 54, 0.49);
            height: 1.1162rem;
            line-height: 1.1162rem;
            text-align: center;
            color: #fff;
            margin: 0.3488rem;
        }
    }
}
</style>