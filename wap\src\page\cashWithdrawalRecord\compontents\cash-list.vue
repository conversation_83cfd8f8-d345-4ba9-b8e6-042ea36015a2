<template>
    <div class="container">
        <div v-if="list.length<=0" class="empty">
            暂无提现信息!
        </div>
        <div class="list" v-else v-infinite-scroll="loadMore" infinite-scroll-disabled="loading" infinite-scroll-distance="10">
            <div class="item" v-for="value in list" :key="value.key">
                <div class="item_order">订单号：{{value.orderNo}}</div>
                <div class="item_bank_no">银行卡号：{{value.bankNo}}</div>
                <div class="item_center">
                    <div class="item_name">
                        <!-- <div class="item_name_title">充值</div> -->
                        <div class="item_name_type">提现</div>
                    </div>
                    <div class="item_money">￥{{value.withAmt}}</div>
                </div>
                <div class="item_error" v-if="value.withStatus == 2">失败原因：{{value.withMsg}}</div>
                <div class="item_bottom">
                    <div class="item_date">{{new Date(value.applyTime) | timeFormat}}</div>
                    <div class="item_status_4" v-if="value.withStatus == 0">审核中</div>
                    <div class="item_status_1" v-if="value.withStatus == 1">转出成功</div>
                    <div class="item_status_2" v-if="value.withStatus == 2">转出失败</div>
                    <div class="item_status_3" v-if="value.withStatus == 3">订单取消</div>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
import { Toast } from "mint-ui";
import * as api from "@/axios/api";

export default {
    components: {},
    props: {},
    data() {
        return {
            loading: false,
            list: [],
            pageNum: 1,
            pageSize: 15,
        };
    },
    watch: {},
    computed: {},
    created() {},
    mounted() {
        this.getListDetail();
    },
    methods: {
        async getListDetail() {
            let opt = {
                withStatus: "", // 提现状态 0已提交，1转账成功，2转账失败
                pageNum: this.pageNum,
                pageSize: 15,
            };
            let data = await api.withdrawList(opt);
            if (data.status === 0) {
                data.data.list.forEach((element) => {
                    this.list.push(element);
                });
            } else {
                Toast(data.msg);
            }
        },
        async loadMore() {
            if (this.list.length < 10) {
                return;
            }
            this.loading = true;
            // 加载下一页
            this.pageNum++;
            await this.getListDetail();
            this.loading = false;
        },
        async cancle(val) {
            // 取消提现
            // MessageBox.confirm('您确定要平仓吗?').then(async action => {
            let opt = {
                withId: val,
            };
            let data = await api.canceloutMoney(opt);
            if (data.status === 0) {
                this.list = [];
                Toast(data.msg);
                this.getListDetail();
            } else {
                Toast(data.msg);
            }
            // });
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    .empty {
        margin-top: 0.3488rem;
        text-align: center;
    }
    .list {
        .item {
            background: #fff;
            margin-top: 0.3488rem;
            font-size: 0.3255rem;
            .item_order {
                border-bottom: solid 1px rgba(245, 247, 250, 1);
                line-height: 0.7441rem;
                padding: 0 0.3488rem;
            }
            .item_center {
                display: flex;
                justify-content: space-between;
                padding: 0.3488rem;
                font-size: 0.372rem;
                .item_name {
                    display: flex;
                }
            }
            .item_bank_no {
                padding: 0.3488rem;
            }
            .item_error {
                padding: 0 0.3488rem;
                line-height: 0.7441rem;
                color: red;
            }
            .item_bottom {
                display: flex;
                justify-content: space-between;
                padding: 0.3488rem;
                padding-top: 0;
                align-items: center;
                .item_date {
                    color: rgba(181, 181, 181, 1);
                }
                .item_status_1 {
                    background: rgba(14, 201, 177, 0.08);
                    color: rgba(14, 201, 177, 1);
                    padding: 0.2325rem;
                    border-radius: 0.1162rem;
                }
                .item_status_2 {
                    background: rgba(255, 141, 26, 0.08);
                    color: rgba(255, 141, 26, 1);
                    padding: 0.2325rem;
                    border-radius: 0.1162rem;
                }
                .item_status_3 {
                    background: rgba(255, 141, 26, 0.08);
                    color: rgba(255, 141, 26, 1);
                    padding: 0.2325rem;
                    border-radius: 0.1162rem;
                }
                .item_status_4 {
                    background: rgba(255, 141, 26, 0.08);
                    color: rgba(255, 141, 26, 1);
                    padding: 0.2325rem;
                    border-radius: 0.1162rem;
                }
            }
        }
    }
}
</style>
