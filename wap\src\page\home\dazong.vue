<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="天启护盘记录" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>
        <div class="menu">
            <div :class="`item ${itemIndex == 0 ? 'active' : ''}`" @click="changeItemIndex(0)">
                <div>
                    <span>我的持仓</span>
                    <span></span>
                </div>
            </div>
            <div :class="`item ${itemIndex == 1 ? 'active' : ''}`" @click="changeItemIndex(1)">
                <div>
                    <span>交易记录</span>
                    <span></span>
                </div>
            </div>
            <div :class="`item ${itemIndex == 2 ? 'active' : ''}`" @click="changeItemIndex(2)">
                <div>
                    <span>我的委托</span>
                    <span></span>
                </div>
            </div>
        </div>

        <div class="list" v-if="itemIndex == 0">
            <div class="list_title">
                <div class="item">名称</div>
                <div class="item">持仓 | 市值</div>
                <div class="item">现价 | 成本</div>
                <div class="item">盈亏 | 涨幅</div>
            </div>
            <div class="list_container">
                <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')" @load="getOrderList(1)">
                    <template v-for="value in list">
                        <div class="item" :key="value.id" @click="chicangDetail(value)">
                            <div class="ebox" style="justify-content: left;">
                                <div class="stock">
                                    <div class="name">{{value.stockName}}</div>
                                    <div class="child">
                                        <div class="tag" v-if="value.stockGid.indexOf('sz') > -1">深</div>
                                        <div class="tag" v-if="value.stockGid.indexOf('sh') > -1">沪</div>
                                        <div class="tag" v-if="value.stockGid.indexOf('bj') > -1">北</div>
                                        <div>{{value.stockCode}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="cbox">
                                <span>{{parseNumber(value.buyNum)}}</span>
                                <span>{{parseNumber(value.buyPrice)}}</span>
                            </div>
                            <div class="cbox">
                                <span>{{parseNumber(value.now_price)}}</span>
                                <span>{{parseNumber(value.buyOrderPrice)}}</span>
                            </div>
                            <div class="cbox">
                                <span :class="value.profitAndLose > 0 ? 'red' : 'green'">{{value.profitAndLose}}</span>
                                <span :class="value.profitAndLossRatio > 0 ? 'red' : 'green'">{{value.profitAndLossRatio}}%</span>
                            </div>
                        </div>
                    </template>
                </van-list>
            </div>
        </div>

        <div class="list" v-if="itemIndex == 1">
            <div class="list_title">
                <div class="item">股票 | 代码</div>
                <div class="item">本金 | 数量</div>
                <div class="item">买入 | 卖出价</div>
                <div class="item">收益 | 涨幅</div>
            </div>
            <div class="list_container">
                <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')" @load="getOrderList(1)">
                    <template v-for="value in list">
                        <div :key="value.id" style="border-bottom: solid 1px rgba(223, 223, 223, 1); padding-bottom: 0.3488rem;">
                            <div class="item" style="border: none;">
                                <div class="ebox" style="justify-content: left;">
                                    <div class="stock">
                                        <div class="name">{{value.stockName}}</div>
                                        <div class="child">
                                            <div class="tag" v-if="value.stockGid.indexOf('sz') > -1">深</div>
                                            <div class="tag" v-if="value.stockGid.indexOf('sh') > -1">沪</div>
                                            <div class="tag" v-if="value.stockGid.indexOf('bj') > -1">北</div>
                                            <div>{{value.stockCode}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="cbox">
                                    <span>{{parseNumber(value.buyPrice)}}</span>
                                    <span>{{parseNumber(value.buyNum)}}</span>
                                </div>
                                <div class="cbox">
                                    <span>{{parseNumber(value.buyOrderPrice)}}</span>
                                    <span>{{parseNumber(value.sellOrderPrice)}}</span>
                                </div>
                                <div :class="`cbox ${value.profitAndLossRatio > 0 ? 'red' : 'green'}`">
                                    <span>{{value.profitAndLose}}</span>
                                    <span>{{value.profitAndLossRatio}}%</span>
                                </div>
                            </div>
                            <div class="time">
                                <div>{{ dayjs(value.buyOrderTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                                <div>{{ dayjs(value.sellOrderTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                            </div>
                            <div class="dbtn" @click="chicangDetail(value)">查看详情</div>
                        </div>
                    </template>
                </van-list>
            </div>
        </div>
        <div class="weituo_list" v-if="itemIndex == 2">
            <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')" @load="getOrderList(2)">
                <template v-for="value in list">
                    <div :key="value.id" @click="weituoDetail(value)">
                        <div class="stock">
                            <div class="name">{{value.stockName}}</div>
                            <div class="child">
                                <div class="tag" v-if="value.stockGid.indexOf('sz') > -1">深</div>
                                <div class="tag" v-if="value.stockGid.indexOf('sh') > -1">沪</div>
                                <div class="tag" v-if="value.stockGid.indexOf('bj') > -1">北</div>
                                <div>{{value.stockCode}}</div>
                            </div>
                        </div>
                        <div class="info">
                            <div class="item">
                                <div>买卖类别</div>
                                <div>证券买入</div>
                            </div>
                            <div class="item">
                                <div>当前状态</div>
                                <div>挂单</div>
                            </div>
                            <div class="item">
                                <div>委托手数</div>
                                <div>{{value.orderNum / 100}}</div>
                            </div>
                            <div class="item">
                                <div>委托价格</div>
                                <div>{{parseNumber(value.buyOrderPrice)}}</div>
                            </div>
                        </div>
                    </div>
                </template>
            </van-list>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import Axios from "axios";

export default {
    data() {
        return {
            list: [],
            finished: false,
            loading: true,
            itemIndex: 0,
            pageNum: 1,
            source: "",
        };
    },
    mounted() {
        this.changeItemIndex(0);
    },
    methods: {
        parseNumber(number) {
            return parseFloat(number).toFixed(2);
        },
        changeItemIndex(index) {
            let _this = this;
            _this.pageNum = 1;
            _this.list = [];
            _this.itemIndex = index;
            _this.finished = false;
            _this.getOrderList(index);
        },
        chicangDetail(value) {
            this.$router.push({
                path:
                    "/chicangDetail?type=dazong&item=" + JSON.stringify(value),
            });
        },
        weituoDetail(value) {
            this.$router.push({
                path: "/weituoDetail?item=" + JSON.stringify(value),
            });
        },
        getOrderList(state) {
            let _this = this;
            if (_this.source) {
                _this.source.cancel("close request");
            }
            _this.source = Axios.CancelToken.source();
            _this.loading = true;
            let opt = {
                state: state,
                type: 3,
                stockCode: "",
                stockSpell: "",
                pageNum: this.pageNum,
                pageSize: 15,
            };
            api.getOrderList(opt, {
                cancelToken: _this.source.token,
            }).then((res) => {
                if (res.data.list.length < 15) {
                    _this.finished = true;
                }
                for (let i = 0; i < res.data.list.length; i++) {
                    _this.list.push(res.data.list[i]);
                }
                _this.loading = false;
                _this.pageNum++;
            });
        },
    },
};
</script>


<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    background: #fff;
    min-height: 100vh;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .weituo_list {
        padding: 0 0.3488rem;
        .stock {
            padding: 0.3488rem 0;
            .name {
                font-size: 0.372rem;
            }
            .child {
                margin-top: 0.1162rem;
                font-size: 0.3255rem;
                display: flex;
                .tag {
                    background: #6d9cff;
                    font-size: 0.2791rem;
                    color: #fff;
                    width: 0.3256rem;
                    height: 0.3256rem;
                    line-height: 0.3256rem;
                    text-align: center;
                    margin-right: 0.1162rem;
                }
            }
            border-bottom: solid 1px rgba(223, 223, 223, 1);
        }
        .info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            border-bottom: solid 1px rgba(223, 223, 223, 1);
            padding-bottom: 0.3488rem;
            .item {
                display: flex;
                margin-top: 0.3488rem;
                div:nth-of-type(1) {
                    color: rgba(125, 125, 125, 1);
                }
                div:nth-of-type(2) {
                    margin-left: 0.1162rem;
                }
            }
        }
    }
    .list {
        .list_title {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            background: rgba(240, 240, 240, 1);
            .item {
                flex: 1;
                text-align: center;
                line-height: 0.9302rem;
                color: rgba(125, 125, 125, 1);
            }
        }
        .list_container {
            .item {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr;
                border-bottom: solid 1px rgba(223, 223, 223, 1);
                padding: 0.3488rem 0;
                .ebox {
                    .stock {
                        padding-left: 0.3488rem;
                        .name {
                            font-size: 0.372rem;
                        }
                        .child {
                            margin-top: 0.1162rem;
                            font-size: 0.3255rem;
                            display: flex;
                            .tag {
                                background: #6d9cff;
                                font-size: 0.2791rem;
                                color: #fff;
                                width: 0.3256rem;
                                height: 0.3256rem;
                                line-height: 0.3256rem;
                                text-align: center;
                                margin-right: 0.1162rem;
                            }
                        }
                    }
                }
                .cbox {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    line-height: 0.4651rem;
                }
            }
            .time {
                display: flex;
                justify-content: space-between;
                padding: 0 0.3488rem;
                color: rgba(125, 125, 125, 1);
            }
            .cbtn {
                display: block;
                background: rgba(238, 0, 17, 1);
                height: 0.6976rem;
                color: #fff;
                text-align: center;
                line-height: 0.6976rem;
                border-radius: 0.3488rem;
                width: 1.3953rem;
            }
            .dbtn {
                border: solid 1px rgba(238, 0, 17, 1);
                color: rgba(238, 0, 17, 1);
                height: 0.9302rem;
                border-radius: 0.2325rem;
                line-height: 0.9302rem;
                text-align: center;
                margin: 0.3488rem;
                margin-bottom: 0;
            }
        }
    }
    .menu {
        display: flex;
        padding-top: 0.3488rem;
        .item {
            flex: 1;
            display: flex;
            justify-content: center;
            div {
                position: relative;
                padding-bottom: 0.2325rem;
                span:nth-of-type(2) {
                    background: rgba(238, 0, 17, 1);
                    height: 2px;
                    position: absolute;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    display: none;
                }
            }
            &.active {
                color: rgba(238, 0, 17, 1);
                div {
                    span:nth-of-type(2) {
                        display: block;
                    }
                }
            }
        }
    }
}
</style>