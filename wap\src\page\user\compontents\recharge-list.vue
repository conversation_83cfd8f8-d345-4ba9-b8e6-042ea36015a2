<template>
    <div class="container">
        <div v-if="list.length<=0" class="empty">
            暂无充值信息!
        </div>
        <div class="list" v-else v-infinite-scroll="loadMore" infinite-scroll-disabled="loading" infinite-scroll-distance="10">
            <div class="item" v-for="value in list" :key="value.key">
                <div class="item_order">订单号：{{value.orderSn}}</div>
                <div class="item_center">
                    <div class="item_name">
                        <!-- <div class="item_name_title">充值</div> -->
                        <div class="item_name_type">{{value.payChannel == 0 ? '支付宝' : value.payChannel == 1 ? '对公转账' : value.payChannel}}</div>
                    </div>
                    <div class="item_money">￥{{value.payAmt}}</div>
                </div>
                <div class="item_bottom">
                    <div class="item_date">{{new Date(value.addTime) | timeFormat}}</div>
                    <div class="item_status_1" v-if="value.orderStatus == 1">银证转入成功</div>
                    <div class="item_status_2" v-if="value.orderStatus == 2">银证转入失败</div>
                    <div class="item_status_3" v-if="value.orderStatus == 3">取消银证转入</div>
                    <div class="item_status_4" v-if="value.orderStatus == 4">审核中</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { Toast } from "mint-ui";
import * as api from "@/axios/api";

export default {
    components: {},
    props: {},
    data() {
        return {
            loading: false,
            list: [],
            pageNum: 1,
            pageSize: 15,
            total: 0,
        };
    },
    watch: {},
    computed: {},
    created() {},
    mounted() {
        this.getListDetail();
    },
    methods: {
        async getListDetail() {
            let opt = {
                payChannel: "", // 支付方式
                orderStatus: "", // 订单状态
                pageNum: this.pageNum,
                pageSize: 15,
            };
            let data = await api.rechargeList(opt);
            if (data.status === 0) {
                data.data.list.forEach((element) => {
                    this.list.push(element);
                });
                this.total = data.data.total;
            } else {
                Toast(data.msg);
            }
        },
        async loadMore() {
            if (
                this.list.length < 10 ||
                this.total <= this.pageNum * this.pageNum
            ) {
                return;
            }
            this.loading = true;
            // 加载下一页
            this.pageNum++;
            await this.getListDetail();
            this.loading = false;
        },
    },
};
</script>
<style lang="less" scoped>
.container {
    .empty {
        margin-top: 0.3488rem;
        text-align: center;
    }
    .list {
        .item {
            background: #fff;
            margin-top: 0.3488rem;
            font-size: 0.3255rem;
            .item_order {
                border-bottom: solid 1px rgba(245, 247, 250, 1);
                line-height: 0.7441rem;
                padding: 0 0.3488rem;
            }
            .item_center {
                display: flex;
                justify-content: space-between;
                padding: 0.3488rem;
                font-size: 0.372rem;
                .item_name {
                    display: flex;
                }
            }
            .item_bottom {
                display: flex;
                justify-content: space-between;
                padding: 0.3488rem;
                padding-top: 0;
                align-items: center;
                .item_date {
                    color: rgba(181, 181, 181, 1);
                }
                .item_status_1 {
                    background: rgba(14, 201, 177, 0.08);
                    color: rgba(14, 201, 177, 1);
                    padding: 0.2325rem;
                    border-radius: 0.1162rem;
                }
                .item_status_2 {
                    background: rgba(255, 141, 26, 0.08);
                    color: rgba(255, 141, 26, 1);
                    padding: 0.2325rem;
                    border-radius: 0.1162rem;
                }
                .item_status_3 {
                    background: rgba(255, 141, 26, 0.08);
                    color: rgba(255, 141, 26, 1);
                    padding: 0.2325rem;
                    border-radius: 0.1162rem;
                }
                .item_status_4 {
                    background: rgba(255, 141, 26, 0.08);
                    color: rgba(255, 141, 26, 1);
                    padding: 0.2325rem;
                    border-radius: 0.1162rem;
                }
            }
        }
    }
}
</style>
