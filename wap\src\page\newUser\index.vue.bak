<template>
    <div class="container">
        <div class="bg-[#000] text-[#111]">
            asd
        </div>
    </div>
    <!-- <div class="user_page">
        <div class="head">
            <div class="top-class">
                <img class="touxiang" src="~@/assets/me/logo_txt.png" />
                <img class="tr" src="~@/assets/temp/23.png" @click="$router.push('/setup')" />
            </div>

            <div class="userHead">
                <div class="tl">
                    <div class="mingzi">
                        <p>{{ maskPhoneNumber(userInfo.phone ? userInfo.phone : userInfo.phone) }}</p>
                        <div class="feae" v-if="this.$store.state.userInfo.isActive == 0" @click="$router.push('/smrz')">
                            <img src="~@/assets/temp/25.png" />
                        </div>
                        <div class="feae" v-if="this.$store.state.userInfo.isActive == 3" @click="$router.push('/smrz')">
                            <img src="~@/assets/temp/31.png" />
                        </div>
                        <div class="feae" v-if="this.$store.state.userInfo.isActive == 2">
                            <img src="~@/assets/temp/17.png" />
                        </div>
                        <div class="feae" v-if="this.$store.state.userInfo.isActive == 1">
                            <img src="~@/assets/temp/30.png" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="zijk">
            <h5>我的资产（元）</h5>
            <div class="kunk">
                <div class="zican">
                    <p class="label-class">总资产（元）</p>
                    <p v-if="$store.state.settingForm.indexDisplay && !$store.state.settingForm.futuresDisplay" class="yans">
                        {{ useFormatMoney(($store.state.userInfo.userAmt || 0) + ($store.state.userInfo.userIndexAmt || 0)) }}
                    </p>
                    <p v-else-if="!$store.state.settingForm.indexDisplay && $store.state.settingForm.futuresDisplay" class="yans">
                        {{ useFormatMoney(($store.state.userInfo.userAmt || 0) + ($store.state.userInfo.userFuturesAmt || 0)) }}
                    </p>
                    <p v-else class="yans">
                        {{ useFormatMoney(($store.state.userInfo.userAmt || 0)) }}
                    </p>
                </div>

                <div class="congz">
                    <a class="silverTransfer" @click="$router.push('/recharge')">银证转入</a><a @click="$router.push('/withdraw')" class="silverTransfers">银证转出</a>
                </div>
            </div>
            <div class="totalMarketValue">
                <p class="label-class">
                    持仓总市值
                </p>
                <h6 class="value-class">
                    {{ useFormatMoney($store.state.userInfo.allFreezAmt || 0) }}
                </h6>
            </div>
            <div class="line-class"></div>
            <div class="boxk">
                <div class="line-class"></div>
                <div class="bdan">
                    <p>
                        可提现资金
                    </p>
                    <h6>
                        {{ useFormatMoney($store.state.userInfo.withdrawFunds || 0) }}
                    </h6>
                </div>
                <div class="bdan">
                    <p>
                        可用资金
                    </p>
                    <h6>
                        <template v-if="!selectUserFlag">
                            {{ useFormatMoney($store.state.userInfo.enableIndexAmt || 0) }}
                        </template>
                        <template v-else>
                            {{ useFormatMoney($store.state.userInfo.enableAmt || 0) }}
                        </template>
                    </h6>
                </div>
                <div class="bdan">
                    <p>
                        持仓总盈利
                    </p>
                    <h6>
                        {{ useFormatMoney($store.state.userInfo.allProfitAndLose || 0) }}
                    </h6>
                </div>
                <div class="bdan">
                    <p>
                        账户总盈利
                    </p>
                    <h6>
                        {{ useFormatMoney($store.state.userInfo.accountAllProfitAndLose || 0) }}
                    </h6>
                </div>
            </div>

        </div>
        <div class="usb">
            <div @click="$router.push('/loginPassword')" class="bl">
                <img src="@/assets/temp/19.png" alt="" />
                <p>登录密码</p>
            </div>
            <div @click="$router.push('/setPassword')" class="bl">
                <img src="@/assets/temp/22.png" alt="" />
                <p>支付密码</p>
            </div>
            <div class="bl" @click="$router.push('/FundingDetails')">
                <img src="@/assets/temp/21.png" alt="" />
                <p>资金流水</p>
            </div>

            <div class="bl" @click="$router.push('/bankCard')">
                <img src="@/assets/temp/20.png" alt="" />
                <p>金融卡</p>
            </div>
        </div>
        <ul class="ganh">
            <li @click="$router.push('/about?e=1')">
                <div class="le">
                    <img src="@/assets/temp/15.png" alt="" />
                    <p>版本更新</p>
                </div>
                <img src="@/assets/temp/24.png" alt="" class="go-calss" />
            </li>
            <li @click="$router.push(`/accountOpeningContract?id=${userInfo.id}`)">
                <div class="le">
                    <img src="@/assets/temp/16.png" alt="" />
                    <p>开户合同</p>
                </div>
                <img src="@/assets/temp/24.png" alt="" class="go-calss" />
            </li>
            <li @click="$router.push('/about?e=4')">
                <div class="le">
                    <img src="@/assets/temp/18.png" alt="" />
                    <p>关于我们</p>
                </div>
                <img src="@/assets/temp/24.png" alt="" class="go-calss" />
            </li>
        </ul>
    </div> -->
</template>

<script>
import * as api from "@/axios/api";
import { Toast, MessageBox } from "mint-ui";
import { isNull, pwdReg } from "@/utils/utils";
import { compress } from "@/utils/imgupload";
import { Toast as VantToast } from "vant";

export default {
    name: "newUser",
    data() {
        return {
            name: "",
            selectUserFlag: true,
            settingDialog: false,
            showPopover: false,
            oldPassword: "", // 旧密码
            newPassword: "", // 新密码
            cirNewPassword: "", // 确认新密码
            userInfo: [],
            actions: [],
            onlineService: "",
            currentRate: 100,
            rate: 0,
            showBtn: true,
        };
    },
    components: {},
    created() {
        this.getUserInfo();
        this.getInfoSite();
    },
    methods: {
        useFormatMoney(price, useCurrencySymbol = true, currency = "CNY") {
            const options = {
                minimumFractionDigits: 2, // 最少显示 0 位小数
                maximumFractionDigits: 6, // 最多显示 6 位小数
            };
            if (useCurrencySymbol) {
                options.style = "currency";
                options.currency = currency;
            }
            const number = Number(price || 0); // 确保 price 为数字，即使为 0
            if (isNaN(number)) {
                throw new Error(
                    "Invalid input: price must be a number--->" + price
                );
            }
            // 格式化数字，根据是否包含货币符号
            return number.toLocaleString(undefined, options);
        },
        onSelect() {},
        goOnline() {
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
            this.$router.push("/service");
        },
        async getInfoSite() {
            let data = await api.getInfoSite();
            if (data.status === 0) {
                this.onlineService = data.data.onlineService;
            } else {
                Toast(data.msg);
            }
        },
        goWall() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/wallet");
        },
        handleZh() {
            this.selectUserFlag = !this.selectUserFlag;

            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        async getUserInfo() {
            // if (this.$posthog) {
            //   const userPhone = window.localStorage.getItem("phone");
            //   console.log(userPhone)
            //     this.$posthog.identify(
            //       `东吴-${userPhone}`,  // Replace 'distinct_id' with your user's unique identifier
            //       { email: `东吴-${userPhone}`, name:`东吴-${userPhone}`} // optional: set additional person properties
            //     );
            //   }

            // 获取用户信息
            const toastInfo = VantToast.loading({
                message: "加载中...",
                duration: 0,
                forbidClick: true,
            });
            let data = await api.getUserInfo();
            toastInfo.clear();
            if (data.status === 0) {
                // 判断是否登录
                this.$store.commit("dialogVisible", false);
                this.$store.state.userInfo = data.data;
                this.userInfo = data.data;
                this.rate = (data.data.enableAmt / data.data.userAmt) * 100;
                if (data.data.isActive === 1 || data.data.isActive === 2) {
                    this.showBtn = false;
                }
            } else {
                this.$store.commit("dialogVisible", true);
            }
        },
        goToTopUp() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
            this.$router.push("/wallet");
        },
        handleOutLoginClick() {
            // 退出登录
            MessageBox.confirm(this.$t("hj149") + "?", this.$t("hj165"), {
                confirmButtonText: this.$t("hj161"),
                cancelButtonText: this.$t("hj106"),
            })
                .then(() => {
                    this.toRegister();
                })
                .catch(() => {});
        },
        goToSettings() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            // 每次打开dialog 清空密码数据
            this.settingDialog = !this.settingDialog;
            if (this.settingDialog) {
                this.oldPassword = "";
                this.newPassword = "";
                this.cirNewPassword = "";
            }
        },
        handleGoToTransfer() {
            if (this.userInfo.length == 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/transfers");
        },
        handleGoToAuthentication() {
            if (this.userInfo.length == 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/authentications");
        },
        handleGoToBankCard() {
            if (this.userInfo.length === 0) {
                this.$store.commit("dialogVisible", true);
                return;
            }
            this.$router.push("/bankCard");
        },
        async toRegister() {
            // 注销登录
            window.localStorage.removeItem("USERTOKEN"); // 清空本地存储 USERTOKEN字段
            this.clearCookie();
            let data = await api.logout();
            if (data.status === 0) {
                // Toast(data.msg)
                this.$router.push("/login");
            } else {
                Toast(data.msg);
            }
            this.$router.push("/login");
        },
        async changeLoginPsd() {
            // 修改密码
            if (
                isNull(this.oldPassword) ||
                isNull(this.newPassword) ||
                isNull(this.cirNewPassword)
            ) {
                Toast(this.$t("hj154"));
                this.settingDialog = false;
            } else if (!pwdReg(this.newPassword)) {
                Toast(this.$t("hj19"));
                this.settingDialog = false;
            } else {
                // 修改密码
                if (this.newPassword === this.cirNewPassword) {
                    let opts = {
                        oldPwd: this.oldPassword,
                        newPwd: this.newPassword,
                    };
                    let data = await api.changePassword(opts);
                    if (data.status === 0) {
                        this.changeLoginPsdBox = false;
                        Toast(data.msg);
                        this.settingDialog = false;
                    } else {
                        Toast(data.msg);
                        this.settingDialog = false;
                    }
                } else {
                    Toast(this.$t("hj155"));
                    this.settingDialog = false;
                }
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
        maskPhoneNumber(phoneNumber) {
            if (!phoneNumber) return "";
            return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
        },
    },
};
</script>

<style scoped lang="less">
.user_page {
}

.head {
    height: 4.88rem;
    width: 100%;
    background: url("~@/assets/temp/12.png") no-repeat 50%;
    background-size: 100%;
    position: relative;
    box-sizing: border-box;
    padding: 0.72rem 0.43rem;
}

.tbox {
}

.tbox img {
}

.userHead {
    margin-top: 0.91rem;
}

.tl {
}

.top-class {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.touxiang {
    width: 3.5rem;
    height: 1.17rem;
}

.tr {
    width: 0.75rem;
    height: 0.75rem;
}

.feae {
    margin-top: 0.21rem;
}

.feae img {
    width: 1.81rem;
    height: 0.64rem;
}

.tl div {
}

.mingzi {
    /** 文本1 */
    font-size: 0.48rem;
    font-weight: bolder;
    letter-spacing: 0rem;
    line-height: 0.69rem;
    color: rgba(255, 255, 255, 1);
    text-align: left;
    vertical-align: top;
}

.zijk {
    background-color: #fff;
    padding: 0.43rem;
    box-sizing: border-box;
    margin-top: -0.11rem;
    border-top-right-radius: 0.11rem;
    border-top-left-radius: 0.11rem;
    z-index: 1;
    position: relative;
}

.zijk h5 {
    /** 文本1 */
    font-size: 0.48rem;
    font-weight: bolder;
    letter-spacing: 0rem;
    line-height: 0.69rem;
    color: rgba(0, 0, 0, 1);
    text-align: left;
}

.kunk {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0.35rem 0 0.13rem 0;
}

.line-class {
    border-bottom: 0.03rem solid rgba(0, 0, 0, 0.03);
    margin-bottom: 0.43rem;
}

.boxk {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 0.99rem;
    position: relative;

    .line-class {
        width: 100%;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
    }
}

.bdan {
    font-size: 0.37rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 0.69rem;
    color: rgba(128, 128, 128, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 0.21rem;
}

.bdan h6 {
    font-size: 0.48rem;
    font-weight: bolder;
    letter-spacing: 0rem;
    line-height: 0.69rem;
    color: rgba(0, 0, 0, 1);
}

.congz {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 0.21rem;
}

.silverTransfer {
    width: 2.35rem;
    height: 0.99rem;
    border-radius: 0.11rem;
    background: rgba(216, 81, 76, 1);

    border: 0.03rem solid rgba(224, 57, 54, 1);

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0.21rem 0.43rem 0.21rem 0.43rem;

    /** 文本1 */
    font-size: 0.37rem;
    font-weight: 500;
    letter-spacing: 0rem;
    line-height: 0.69rem;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    white-space: nowrap;
}

.silverTransfers {
    width: 2.35rem;
    height: 0.99rem;
    opacity: 1;
    border-radius: 0.11rem;
    background: rgba(15, 187, 127, 1);

    border: 0.03rem solid rgba(15, 187, 127, 1);

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0.21rem 0.43rem 0.21rem 0.43rem;

    /** 文本1 */
    font-size: 0.37rem;
    font-weight: 500;
    letter-spacing: 0rem;
    line-height: 0.69rem;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    white-space: nowrap;
}

.totalMarketValue {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.21rem;
    width: fit-content;
    height: 1.01rem;
    border-radius: 0.21rem;
    background: rgba(250, 250, 250, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.21rem 0.21rem 0.21rem 0.21rem;
    margin-bottom: 0.43rem;

    .label-class {
        /** 文本1 */
        font-size: 0.37rem;
        font-weight: 400;
        letter-spacing: 0rem;
        line-height: 0.69rem;
        color: rgba(166, 166, 166, 1);
    }

    .value-class {
        /** 文本1 */
        font-size: 0.48rem;
        font-weight: bolder;
        letter-spacing: 0rem;
        line-height: 0.69rem;
        color: rgba(42, 130, 228, 1);
    }
}

.tx {
}

.tile {
}

.usb {
    height: 2.16rem;
    border-radius: 0.11rem;
    background: rgba(255, 255, 255, 1);

    border: 0.05rem solid rgba(237, 237, 237, 1);
    box-sizing: border-box;
    margin: 0.43rem;
    display: flex;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.bl {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 0.08rem;
}

.bl svg {
}

.bl img {
    width: 0.8rem;
    height: 0.8rem;
}

.bl p {
}

.ganh {
    margin-bottom: 2.67rem;

    .go-calss {
        width: 0.43rem;
        height: 0.43rem;
    }
}

.ganh li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0.8rem;
    height: 1.87rem;
    border-bottom: 0.03rem solid rgba(151, 151, 151, 0.24);
}

.le {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 0.43rem;
}

.ganh li .le img {
    width: 0.59rem;
    height: 0.59rem;
}

.ganh li .le p {
    font-size: 0.37rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 19.16rem;
    color: rgba(60, 60, 60, 1);
    text-align: left;
}

.ganh li span {
}

.daxiao {
}

.zican {
    .label-class {
        /** 文本1 */
        font-size: 0.37rem;
        font-weight: 400;
        letter-spacing: 0rem;
        line-height: 0.69rem;
        color: rgba(128, 128, 128, 1);
        text-align: left;
    }
}

.yans {
    font-size: 0.75rem;
    font-weight: bolder;
    letter-spacing: 0rem;
    line-height: 0.69rem;
    color: rgba(0, 0, 0, 1);
    text-align: left;
    margin-top: 0.27rem;
}

.zican p {
}
</style>
