<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="账户交易" left-arrow fixed @click-left="$router.go(-1)" />
        </div>
        <div class="menu">
            <div :class="`option ${tabIndex == 0 ? 'active' : ''}`" @click="changeTabIndex(0)">
                <div class="item">
                    <span>买入</span>
                    <span></span>
                </div>
            </div>
            <div :class="`option ${tabIndex == 1 ? 'active' : ''}`" @click="changeTabIndex(1)">
                <div class="item">
                    <span>卖出</span>
                    <span></span>
                </div>
            </div>
        </div>
        <div class="ebox_container" v-if="tabIndex == 0">
            <div class="ebox">
                <div class="clabel">代码</div>
                <div class="tag_container">
                    <div class="tag" v-if="symbol.indexOf('sz') > -1">深</div>
                    <div class="tag" v-if="symbol.indexOf('sh') > -1">沪</div>
                    <div class="tag" v-if="symbol.indexOf('bj') > -1">北</div>
                    <div>{{name}}[{{code}}]</div>
                </div>
            </div>
            <div class="line"></div>
            <div class="ebox mt">
                <div class="clabel">现价</div>
                <div>{{nowPrice}}</div>
            </div>
            <div class="ebox mt">
                <div class="clabel">买入价格</div>
                <div>{{nowPrice}}</div>
            </div>
            <div class="line"></div>
            <div class="ebox mt">
                <div class="clabel">仓位</div>
                <div class="cang">
                    <div :class="`cang_item ${itemIndex == 0 ? 'active' : ''}`" @click="calculatePortion(1/4, 0)">1/4仓</div>
                    <div :class="`cang_item ${itemIndex == 1 ? 'active' : ''}`" @click="calculatePortion(1/3, 1)">1/3仓</div>
                    <div :class="`cang_item ${itemIndex == 2 ? 'active' : ''}`" @click="calculatePortion(1/2, 2)">1/2仓</div>
                    <div :class="`cang_item ${itemIndex == 3 ? 'active' : ''}`" @click="calculatePortion(1, 3)">全仓</div>
                </div>
            </div>
            <div class="ebox mt">
                <div class="clabel">买入手数</div>
                <div class="input">
                    <input placeholder="买入手数" :value="buyNum" @input="handleInput" type="number" />
                </div>
            </div>
            <div class="line"></div>
            <div class="ebox mt">
                <div class="clabel">手续费（元）</div>
                <div>￥{{ ( this.nowPrice *  this.buyNum  * 100 * this.settingdetail.buyFee).toFixed(2) }}</div>
            </div>
            <div class="ebox mt">
                <div class="clabel">可用金额</div>
                <div>￥{{ userinfo.enableAmt }}</div>
            </div>
            <div class="ebox mt">
                <div class="clabel">应付</div>
                <div>￥{{ (nowPrice * buyNum  * 100  / lever).toFixed(2) }}</div>
            </div>
            <div class="ebtn" @click="setBuy">买入下单</div>
        </div>
        <div class="ebox_container" v-if="tabIndex == 1">
            <van-list v-model="loading" :finished="finished" :finished-text="$t('hj43')" @load="getSellList" :immediate-check="immediate">
                <div class="cbox" v-for="value in sellList" :key="value.id">
                    <div class="flex">
                        <div style="display: flex; align-items: center;">
                            <div class="tag" v-if="symbol.indexOf('sz') > -1">深</div>
                            <div class="tag" v-if="symbol.indexOf('sh') > -1">沪</div>
                            <div class="tag" v-if="symbol.indexOf('bj') > -1">北</div>
                            <div>{{value.stockName}}({{value.stockGid}})</div>
                        </div>
                        <div>最新价格：<span class="red">{{value.now_price}}</span></div>
                    </div>
                    <div class="flex">
                        <div>买入价格：{{value.buyOrderPrice}}</div>
                        <div>买入数量：{{value.buyNum / 100}}手</div>
                    </div>
                    <div class="flex">
                        <div>市值：{{ value.buyPrice }}</div>
                    </div>
                    <div class="flex">
                        <div>浮动盈亏：{{ value.profitAndLose }}</div>
                        <div>总盈亏：{{ value.allProfitAndLose }}</div>
                    </div>
                    <div class="flex">
                        <div>时间</div>
                        <div>{{ value.buyOrderTime | gettime }}</div>
                    </div>
                    <div class="cbtn" @click.stop="getpingcang(value.positionSn)">我要平仓</div>
                </div>
            </van-list>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { Toast } from "vant";
import { MessageBox } from "mint-ui";
import BigNumber from "bignumber.js";
export default {
    components: {},
    props: {},
    data() {
        return {
            itemIndex: -1,
            showPicker: false,
            currentItem: "",
            userinfo: "",
            settingdetail: "",
            siteLeverList: [],
            nowPrice: 0,
            buyType: 0, // 0 买入 1 卖出
            buyNum: "", //买入数量
            lever: "", //杠杆倍数
            name: "",
            code: "",
            id: "",
            buying: false,
            buyfee: 0,
            bigNumbuy: 0,
            symbol: "",
            tabIndex: 0,
            finished: false,
            immediate: false,
            sellList: [],
        };
    },
    mounted() {
        if (this.$route.query.code) {
            this.code = this.$route.query.code;
        }
        if (this.$route.query.name) {
            this.name = this.$route.query.name;
        }
        if (this.$route.query.tab) {
            this.tabIndex = this.$route.query.tab;
        }
        if (this.$route.query.id) {
            this.id = this.$route.query.id;
        }
        if (this.$route.query.m) {
            this.nowPrice = Number(this.$route.query.m);
        }
        if (this.$route.query.symbol) {
            this.symbol = this.$route.query.symbol;
        }
        // this.currentItem = JSON.parse(decodeURIComponent(this.$route.query.item))

        this.init();
    },
    methods: {
        init() {
            if (this.tabIndex == 0) {
                this.getuserSetting(() => {
                    this.getUserInfo();
                });
            } else {
                this.sellList = [];
                this.getSellList();
            }
        },
        getSellList() {
            let _this = this;
            let opt = {
                state: 0,
                stockCode: this.code, // 代码
                stockSpell: "", // 简拼
                pageNum: 1,
                pageSize: 100,
            };
            _this.loading = true;
            api.getOrderList(opt).then((res) => {
                if (res.data.list.length < 15) _this.finished = true;
                for (let i = 0; i < res.data.list.length; i++) {
                    console.log(res.data.list[i]);
                    _this.sellList.push(res.data.list[i]);
                }
                _this.loading = false;
            });
        },
        getpingcang(val) {
            MessageBox.confirm(this.$t("hj139") + "?", this.$t("hj165"), {
                confirmButtonText: this.$t("hj161"),
                cancelButtonText: this.$t("hj106"),
            })
                .then(async () => {
                    let opt = {
                        positionSn: val,
                    };
                    let data = await api.sell(opt);
                    if (data.status === 0) {
                        Toast(data.msg);
                        // 沪深京持仓
                        this.finished = false;
                        this.getListDetail();
                        this.tabsPositionNumArr = [];
                        // 沪深京平仓
                        this.finisheds = false;
                        this.tabsPcArr = [];
                        this.init();
                    } else if (data.msg.indexOf("不在交易时段内") > -1) {
                        Toast(this.$t("hj140"));
                    } else {
                        Toast(data.msg);
                    }
                })
                .catch(() => {});
        },
        changeTabIndex(index) {
            this.tabIndex = index;
            this.init();
        },
        calculatePortion(fraction, index) {
            const portion = this.bigNumbuy
                .multipliedBy(fraction)
                .integerValue(BigNumber.ROUND_FLOOR);
            this.buyNum = portion.toString();
            this.itemIndex = index;
        },
        onConfirm(item) {
            console.log(item);
            this.showPicker = false;
            this.lever = item.value;
        },

        handleInput(event) {
            const value = event.target.value;
            // 移除所有非数字字符
            const numericValue = value.replace(/[^0-9]/g, "");
            // 如果第一个字符是'0'，且长度大于1，则移除第一个字符
            if (numericValue.length > 1 && numericValue.charAt(0) === "0") {
                this.buyNum = numericValue.slice(1);
            } else {
                this.buyNum = numericValue;
            }
            let _buyNum = new BigNumber(this.buyNum);
            let _bigNumbuy = new BigNumber(this.bigNumbuy);
            if (_buyNum.isGreaterThan(_bigNumbuy)) {
                this.buyNum = this.bigNumbuy;
                this.$message.error("最大只能购买" + this.bigNumbuy + "手");
            }
        },
        async getuserSetting(cb) {
            var opt = {};
            var data = await api.getSetting(opt);
            this.settingdetail = data.data;
            this.siteLever = this.settingdetail.siteLever.split("/");
            this.lever = this.siteLever[0];
            this.siteLeverList = [];
            for (let i = 0; i < this.siteLever.length; i++) {
                let val = this.siteLever[i];
                let item = {
                    text: val + "倍",
                    value: val,
                };
                this.siteLeverList.push(item);
            }
            this.buyNum = this.settingdetail.buyMinNum / 100;
            this.buyfee = (
                this.nowPrice *
                this.buyNum *
                100 *
                this.settingdetail.buyFee
            ).toFixed(2);
            cb();
        },
        async getbuyVipQc() {
            if (this.buyNum * 100 < this.settingdetail.buyMinNum) {
                this.$message.error(
                    "交易数量不能小于" +
                        this.settingdetail.buyMinNum / 100 +
                        "手"
                );
                return;
            }
            if (this.buyNum * 100 > this.settingdetail.buyMaxNum) {
                this.$message.error(
                    "交易数量不能大于" +
                        this.settingdetail.buyMaxNum / 100 +
                        "手"
                );
                return;
            }
            var opt = {
                buyNum: this.buyNum * 100,
                stockCode: this.currentItem.c,
                lever: 1,
                buyType: this.buyType,
            };
            var data = await api.buyVipQc(opt);
            if (data.status == 0) {
                this.$toast("下单成功");
            } else {
                this.$toast(data.msg);
            }
        },
        async getUserInfo() {
            // 获取用户信息
            let data = await api.getUserInfo();
            if (data.status === 0) {
                this.userinfo = data.data;

                //   var buyTotle= this.userinfo.enableAmt /(this.nowPrice * 1  * 100 +  ( this.nowPrice   * 100 * this.settingdetail.buyFee))

                //   this.bigNumbuy = Math.floor(buyTotle)
                // ￥{{ (nowPrice * buyNum  * 100  / lever).toFixed(2) }}

                const enableAmt = new BigNumber(this.userinfo.enableAmt);
                const nowPrice = new BigNumber(this.nowPrice);
                const buyFee = new BigNumber(this.settingdetail.buyFee);
                console.log(this.userinfo.enableAmt);
                console.log(this.nowPrice);
                console.log(this.settingdetail.buyFee);
                // 计算买入总量
                const buyTotal = enableAmt.dividedBy(
                    nowPrice
                        .multipliedBy(1)
                        .multipliedBy(100)
                        .plus(nowPrice.multipliedBy(100).multipliedBy(buyFee))
                );
                console.log(buyTotal);
                // 使用 Math.floor 取整
                this.bigNumbuy = buyTotal.integerValue(BigNumber.ROUND_FLOOR);
            }
        },
        //买卖
        setBuy() {
            if (!this.$store.state.userInfo.idCard) {
                Toast(this.$t("hj111"));
                this.$router.push("/smrz");
                return;
            }
            if (this.buyNum * 100 < this.settingdetail.buyMinNum) {
                this.$message.error(
                    "交易数量不能小于" +
                        this.settingdetail.buyMinNum / 100 +
                        "手"
                );
                return;
            }
            if (this.buyNum * 100 > this.settingdetail.buyMaxNum) {
                this.$message.error(
                    "交易数量不能大于" +
                        this.settingdetail.buyMaxNum / 100 +
                        "手"
                );
                return;
            }
            // if (!this.agree) {
            //   Toast('需同意合作协议才能交易!')
            // } else if (isNull(this.selectNumber) && isNull(this.autoNumber)) {
            //   Toast('请选择购买手数')
            // } else if (isNull(this.selectType)) {
            //   Toast('请选择买卖方向')
            // } else if(isNull(this.subaccountNumber)) {
            //   Toast('请选择子账户')
            // } else {}
            if (this.buying) {
                return;
            }
            this.buying = true;
            let opts = {};
            opts = {
                // stockId: this.detail.id,
                // buyNum: this.selectNumber ? this.selectNumber * 100 : 0, // 单位为手
                // buyType: this.selectType,
                // lever: this.selectCycle ? this.selectCycle : 0,
                // subaccountNumber:this.subaccountNumber
                //买入是买涨buyType:0, 卖出是买跌buyType:1,卖出的状态是0，买入的状态是1
                buyNum: Number(this.buyNum) * 100, // 单位为手
                //buyNum: (this.num.match(/\d+/g))[0] * 100, // 单位为手
                lever: this.lever,
                buyType: 0,
                stockId: this.id,
            };
            this.gpBuy(opts);
        },
        //股票买入
        async gpBuy(opts) {
            opts.stockId = this.id;
            let data = await api.buy(opts);
            this.buying = false;
            if (data.status === 0) {
                Toast(data.data);
                this.getUserInfo();
                this.$router.go(-1);
            } else {
                if (data.msg.indexOf("不在交易时段内") > -1) {
                    Toast(this.$t("hj113"));
                } else {
                    Toast(data.msg);
                }
            }
        },
    },
    filters: {
        gettime(time) {
            if (!time) {
                return "";
            }
            var nd = new Date(time);
            var y = nd.getFullYear();
            var mm = nd.getMonth() + 1;
            var d = nd.getDate();
            var h = nd.getHours();
            var m = nd.getMinutes();
            var c = nd.getSeconds();
            if (mm < 10) {
                mm = "0" + mm;
            }
            if (d < 10) {
                d = "0" + d;
            }
            if (h < 10) {
                h = "0" + h;
            }
            if (m < 10) {
                m = "0" + m;
            }
            if (c < 10) {
                c = "0" + c;
            }
            // 17:35:2922-06-2022
            return y + "-" + mm + "-" + d + " " + h + ":" + m + ":" + c;
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    min-height: 100vh;
    background: #fff;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .menu {
        padding-top: 0.2326rem;
        border-bottom: 1px solid #f1f1f1;
        display: flex;
        .option {
            flex: 1;
            display: flex;
            justify-content: center;
            .item {
                display: flex;
                flex-direction: column;
                span:nth-last-of-type(1) {
                    display: none;
                    height: 2px;
                    width: 100%;
                    margin-top: 0.2326rem;
                    background: rgba(238, 0, 17, 1);
                }
            }
            &.active {
                .item {
                    color: rgba(238, 0, 17, 1);
                    span:nth-last-of-type(1) {
                        display: block;
                    }
                }
            }
        }
    }
    .ebox_container {
        // padding: 0.3488rem;
        .line {
            height: 0.3488rem;
            width: 100%;
            background: rgba(245, 247, 250, 1);
        }
        .ebox {
            // box-shadow: 0 0 6px #0003;
            // padding: 0.3488rem;
            // border-radius: 0.2326rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 0.9302rem;
            padding: 0 0.3488rem;

            .clabel {
                color: rgba(125, 125, 125, 1);
                width: 30%;
            }
            .tag_container {
                display: flex;
                align-items: center;
                .tag {
                    background: rgba(255, 141, 26, 1);
                    font-size: 0.2791rem;
                    color: #fff;
                    width: 0.4651rem;
                    height: 0.4651rem;
                    line-height: 0.4651rem;
                    border-radius: 0.1162rem;
                    text-align: center;
                    margin-right: 0.2325rem;
                }
            }

            .cang {
                display: flex;
                .cang_item {
                    background: #eee;
                    margin-left: 0.2326rem;
                    font-size: 0.2791rem;
                    padding: 0.1163rem;
                    &.active {
                        background: rgba(224, 57, 54, 0.05);
                        color: rgba(224, 57, 54, 1);
                    }
                }
            }
            .input {
                // flex: 1;
                input {
                    width: 100%;
                }
            }
            &.mt {
                // margin-top: 0.3488rem;
            }
        }
        .ebtn {
            border-radius: 8px;
            background: rgba(215, 12, 24, 1);
            box-shadow: 0px 2px 4px rgba(224, 57, 54, 0.49);
            height: 1.1162rem;
            line-height: 1.1162rem;
            text-align: center;
            color: #fff;
            position: absolute;
            bottom: 0.3488rem;
            left: 0.3488rem;
            right: 0.3488rem;
        }
        .cbox {
            border-bottom: solid 1px rgba(223, 223, 223, 1);
            font-size: 0.3256rem;
            line-height: 0.6976rem;
            padding: 0.3488rem 0;
            .tag {
                background: #6d9cff;
                font-size: 0.2791rem;
                color: #fff;
                width: 0.3256rem;
                height: 0.3256rem;
                line-height: 0.3256rem;
                text-align: center;
                margin-right: 0.1162rem;
            }
            .flex {
                display: flex;
                justify-content: space-between;
            }
            .cbtn {
                background: rgba(240, 240, 240, 1);
                height: 0.6976rem;
                line-height: 0.6976rem;
                border-radius: 0.3333rem;
                width: 2.3255rem;
                text-align: center;
                margin: auto;
            }
        }
    }
}
</style>