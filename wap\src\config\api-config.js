/**
 * API配置文件
 * 存储所有可能的API服务器地址和健康检查配置
 */

// 服务器地址列表
const API_URLS = [
    'https://hw1s0mm1.gxbdk.com',
    'https://tx1n0qe2.gxbdk.com',
    'https://tx1mpj1zc.rleebt.com',
    'https://tx2dauk9i.rleebt.com',
    'https://hgfqe2.ms62d6.com',
    'https://u2f4fu.ms62d6.com',
    'https://txgdkl2.esgjr.com',
    ' https://txzsapi02.esgjr.com',
    'https://txgdkl2.esgjr.com',
   

    'https://hwgdkl2.esgjr.com'
]

// 远程连接池URL，用于动态获取更多服务器地址
const CONNECTION_POOL_URL = 'https://poolses.fztfsj.com/zszq-api.json'

// 健康检查路径
const HEALTH_CHECK_PATH = '/health-check'

// 健康检查响应验证值
const HEALTH_CHECK_RESPONSE = 'zhaoshang'

export default {
    API_URLS,
    CONNECTION_POOL_URL,
    HEALTH_CHECK_PATH,
    HEALTH_CHECK_RESPONSE
} 