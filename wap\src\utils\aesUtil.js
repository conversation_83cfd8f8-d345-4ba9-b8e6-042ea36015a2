import CryptoJS from 'crypto-js'

const keyHex = 'fde9e848e4e82d451f44f635eceacb05';

export function encrypt(plainText) {
    let jsonString = JSON.stringify(plainText)
    const key = CryptoJS.enc.Utf8.parse(keyHex);
    const encrypted = CryptoJS.AES.encrypt(jsonString, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.toString()
}

/**
 * AES解密 
 * @param {string} cipherText Base64格式密文 
 * @returns {string} 解密后的明文 
 */
export function decrypt(cipherText) {
    if (!cipherText) return '';
    const key = CryptoJS.enc.Utf8.parse(keyHex);
    const decrypted = CryptoJS.AES.decrypt(cipherText, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
}