<template>
    <div class="container">
        <div class="top">
            <!-- <div class="header">
                <van-nav-bar>
                    <template #title>
                        <img src="~@/assets/images/qiquan26/17336181584430A720474.png" style="height: 0.8139rem;">
                    </template>
</van-nav-bar>
</div> -->
            <div class="header">
                <img src="~@/assets/images/qiquan26/logo.jpg" style="height: 0.8139rem;">
                <img src="~@/assets/temp/58.png" style="height: 0.8139rem; margin-left: 0.3488rem;"
                    @click="$router.push('/speedtest')">
            </div>
            <div class="search" @click="getsearch()">
                <!-- <img src="~@/assets/images/qiquan26/search.png" alt=""> -->
                <div class="search_icon">
                    <svg t="1742902499156" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4638" width="64" height="64">
                        <path
                            d="M653.54 707.45C498.1 833.17 270.18 809.07 144.46 653.63 18.84 498.32 42.96 270.17 198.28 144.55 353.72 18.84 581.64 42.93 707.36 198.37c107.37 132.76 107.37 322.5 0 455.26l241.48 241.35c14.87 14.86 14.88 38.96 0.03 53.84-14.87 14.89-38.99 14.9-53.87 0.02L653.54 707.45zM425.8 139.62c-157.93 0-285.96 128.03-285.96 285.96 0 157.93 128.03 285.96 285.96 285.96 157.84 0 285.84-127.89 285.96-285.74-0.08-157.92-128.02-285.97-285.96-286.18z"
                            p-id="4639"></path>
                    </svg>
                </div>
                <span>请输入股票代码/简拼</span>
            </div>
            <div class="banner">
                <van-swipe :autoplay="3000" indicator-color="white">
                    <van-swipe-item>
                        <img src="~@/assets/images/qiquan26/1.png">
                    </van-swipe-item>
                    <van-swipe-item>
                        <img src="~@/assets/images/qiquan26/2.png">
                    </van-swipe-item>
                    <van-swipe-item>
                        <img src="~@/assets/images/qiquan26/3.png">
                    </van-swipe-item>
                    <van-swipe-item>
                        <img src="~@/assets/images/qiquan26/4.png">
                    </van-swipe-item>
                    <van-swipe-item>
                        <img src="~@/assets/images/qiquan26/5.png">
                    </van-swipe-item>
                    <van-swipe-item>
                        <img src="~@/assets/images/qiquan26/6.png">
                    </van-swipe-item>
                </van-swipe>
            </div>
            <div class="menu">
                <div class="home-item" @click="$router.push({ path: '/newshares' })">
                    <div class="item-img"><img src="~@/assets/images/qiquan26/new_shares.png" alt=""></div>
                    <div class="item-name"><span>新股申购</span></div>
                </div>
                <div class="home-item" @click="$router.push({ path: '/peishouhistory?type=1' })">
                    <div class="item-img"><img src="~@/assets/images/qiquan26/my_new.png" alt=""></div>
                    <div class="item-name"><span>我的新股</span></div>
                </div>
                <div class="home-item" @click="$router.push({ path: '/biglist' })">
                    <div class="item-img"><img src="~@/assets/images/qiquan26/dazong.png" alt=""></div>
                    <div class="item-name"><span>天启护盘</span></div>
                </div>
                <div class="home-item" @click="$router.push({ path: '/recharge' })">
                    <div class="item-img"><img src="~@/assets/images/qiquan26/recharge.png" alt=""></div>
                    <div class="item-name"><span>银证转账</span></div>
                </div>
                <div class="home-item" @click="$router.push({ path: '/service' })">
                    <div class="item-img"><img src="~@/assets/images/qiquan26/kefu.png" alt=""></div>
                    <div class="item-name"><span>客服</span></div>
                </div>
            </div>
        </div>
        <div class="data">
            <div class="data_title">
                <img src="~@/assets/images/qiquan26/data_title.png" />
            </div>
            <div class="data_layout">
                <div class="data_item">
                    <div class="data_item_title">总资产</div>
                    <div class="data_item_money">{{ useFormatMoney(($store.state.userInfo.userAmt || 0)) }}</div>
                </div>
                <div class="line"></div>
                <div class="data_item">
                    <div class="data_item_title">可用资金</div>
                    <div class="data_item_money">{{ useFormatMoney($store.state.userInfo.enableAmt || 0) }}</div>
                </div>
            </div>
        </div>
        <div class="stock">
            <div class="stock_title">
                <img src="~@/assets/images/qiquan26/stock_title.png" />
            </div>
            <div class="stock_swiper">
                <div class="index" v-for="value in hotStockList" :key="value.id">
                    <div class="index_title">{{ value.indexName }}</div>
                    <div :class="`index_num ${value.floatRate > 0 ? 'red' : 'green'}`">{{ value.currentPoint }}</div>
                    <div :class="`index_text ${value.floatRate > 0 ? 'red' : 'green'}`">
                        <span>{{ Number(value.floatPoint).toFixed(2) }}</span>
                        <span>{{ value.floatRate }}%</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="news_layout">
            <div class="news_title">
                <img src="~@/assets/images/qiquan26/news_title.png" />
            </div>
            <div class="news_menu">
                <div :class="`news_menu_item ${tabactive == 1 ? 'active' : ''}`" @click="getNewsList(1)">财经要闻</div>
                <div :class="`news_menu_item ${tabactive == 2 ? 'active' : ''}`" @click="getNewsList(2)">经济数据</div>
                <div :class="`news_menu_item ${tabactive == 3 ? 'active' : ''}`" @click="getNewsList(3)">7*24全球</div>
                <div :class="`news_menu_item ${tabactive == 4 ? 'active' : ''}`" @click="getNewsList(4)">国际经济</div>
            </div>
            <div class="news">
                <div class="news_item" v-for="value in newsContent1" :key="value.id" @click="getnewsdetail(value)">
                    <div class="news_title">{{ value.title }}</div>
                    <!-- <div class="news_time">发布时间：{{ new Date(value.addTime) | timeFormat }}</div> -->
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { init, dispose } from "klinecharts";
export default {
    components: {},
    props: {},
    data() {
        return {
            isShow: false,
            tabactive: 1,
            newsContent1: [],
            noticebar: "",
            hushentiao: "",
            isshow: true,
            hotStockList: [],
        };
    },
    mounted() {
        this.getUserInfo();
        this.getNewsList(1);
        this.stockgetZdfNumber();
        // this.kLineChart = init("Zline");
        // this.kLineChart.setStyleOptions({
        //     candle: {
        //         type: "area",
        //     },
        // });
        this.getListMarket();
        // this.getkline();
    },
    destroyed: function () {
        dispose("chart-type-k-line");
        this.isshow = false;
    },
    methods: {
        useFormatMoney(price, useCurrencySymbol = true, currency = "CNY") {
            const options = {
                minimumFractionDigits: 2, // 最少显示 0 位小数
                maximumFractionDigits: 6, // 最多显示 6 位小数
            };
            if (useCurrencySymbol) {
                options.style = "currency";
                options.currency = currency;
            }
            const number = Number(price || 0); // 确保 price 为数字，即使为 0
            if (isNaN(number)) {
                throw new Error(
                    "Invalid input: price must be a number--->" + price
                );
            }
            // 格式化数字，根据是否包含货币符号
            return number.toLocaleString(undefined, options);
        },
        getListMarket() {
            let val = {
                pageNum: 1,
                pageSize: 15,
            };
            api.getListMarket(val).then((res) => {
                for (let i = 0; i < 3; i++) {
                    this.hotStockList.push(res.data[i]);
                }
            });
        },
        async getkline(
            baseTimestamp = Date.now(),
            basePrice = 5000,
            dataSize = 800
        ) {
            var opt = {
                code: "000001",
                time: 5,
                ma: 5,
                size: 100,
            };
            let data = await api.getMinK_Echarts(opt);
            var klinelist = data.data.values.reverse();
            const dataList = [];
            for (let i = 0; i < klinelist.length; i++) {
                // const element = klinelist[i];
                const kLineModel = {
                    open: klinelist[i][0],
                    low: klinelist[i][2],
                    high: klinelist[i][3],
                    close: klinelist[i][1],
                    volume: klinelist[i][4],
                    timestamp: klinelist[i][5],
                };
                dataList.unshift(kLineModel);
            }
            // const dataList = []
            // let timestamp = Math.floor(baseTimestamp / 60 / 1000) * 60 * 1000
            // let baseValue = basePrice
            // const prices = []
            // for (let i = 0; i < dataSize; i++) {
            //     baseValue = baseValue + Math.random() * 20 - 10
            //     for (let j = 0; j < 4; j++) {
            //         prices[j] = (Math.random() - 0.5) * 12 + baseValue
            //     }
            //     prices.sort()
            //     const openIdx = +Math.round(Math.random() * 3).toFixed(0)
            //     let closeIdx = +Math.round(Math.random() * 2).toFixed(0)
            //     if (closeIdx === openIdx) {
            //         closeIdx++
            //     }
            //     const volume = Math.random() * 50 + 10
            //     const kLineModel = {
            //         open: prices[openIdx],
            //         low: prices[0],
            //         high: prices[3],
            //         close: prices[closeIdx],
            //         volume: volume,
            //         timestamp
            //     }
            //     timestamp -= 60 * 1000
            //     kLineModel.turnover = (kLineModel.open + kLineModel.close + kLineModel.high + kLineModel.low) / 4 * volume
            //     dataList.unshift(kLineModel)
            // }
            // console.log(dataList, generatedKLineDataList())
            var that = this;
            setTimeout(() => {
                this.kLineChart.applyNewData(dataList);
            }, 500);
            if (this.isshow) {
                setTimeout(() => {
                    that.getkline();
                }, 3000);
            }
        },

        async getUserInfo() {
            // 获取用户信息
            let data = await api.getUserInfo();
            if (data.status === 0) {
                // 判断是否登录
                this.$store.commit("dialogVisible", false);
                this.$store.state.userInfo = data.data;
                this.userInfo = data.data;
                this.rate = (data.data.enableAmt / data.data.userAmt) * 100;
                if (data.data.isActive === 1 || data.data.isActive === 2) {
                    this.showBtn = false;
                }
            } else {
                this.$store.commit("dialogVisible", true);
            }
        },
        getsearch() {
            this.$router.push({
                path: "/Searchlist",
            });
        },
        async stockgetZdfNumber() {
            let data = await api.stockgetZdfNumber();
            this.hushentiao = data.data;
        },
        async getNewsList(type) {
            this.tabactive = type;
            this.newsContent1 = [];
            let data = await api.queryNewsList(type);
            // console.log('xinwen:', data)
            this.newsContent1 = data.data.list;
            this.noticebar = data.data.list[0].title;
        },
        getnewsdetail(item) {
            this.$router.push({
                path: "/newPage",
                query: {
                    listid: item.id,
                },
            });
        },
        getHeaderlink(val) {
            if (val == 0) {
                this.$router.push({
                    path: "/MyList",
                });
            } else if (val == 1) {
                this.$router.push({
                    path: "/trading-list",
                });
            } else if (val == 2) {
                this.$router.push({
                    // path: '/jijin'
                    path: "/about?e=6",
                });
            } else if (val == 3) {
                this.$router.push({
                    // path: '/college'
                    path: "/about?e=6",
                });
            } else if (val === 4) {
                // var name = this.userInfo.phone
                // var id = this.userInfo.id
                // const metadata = `&metadata={"name":"${name || 'Guest'}(${id || 0})","uid":"${id || 0}","id":"${id || 0}"}`
                // window.location.href = '/static/web/chatlink.html?language=tw' + metadata

                this.$router.push({
                    path: "/service",
                });
            } else if (val == 5) {
                this.$router.push({
                    path: "/openaccount",
                });
            }
        },
        getHeaderlink1(val) {
            switch (val) {
                case 1:
                    this.$router.push({
                        path: "/Subscription?idx=1",
                    });
                    break;
                case 2:
                    this.$router.push({
                        path: "/Subscription?idx=3",
                    });
                    break;
                case 3:
                    this.$router.push({
                        path: "/Subscription?idx=4",
                    });
                    break;
                case 4:
                    this.$router.push({
                        path: "/Subscription?idx=5",
                    });
                    break;
                case 5:
                    this.$router.push({
                        path: "/Subscription?idx=2",
                    });
                    break;
                case 6:
                    this.$router.push({
                        path: "/DragonTiger",
                    });
                    break;
                case 7:
                    this.$router.push({
                        path: "/stopRecovery",
                    });
                    break;
                case 8:
                    this.$router.push({
                        path: "/topTen",
                    });
                    break;
                case 9:
                    this.$router.push({
                        path: "/daylimit",
                    });
                    break;
                case 10:
                    this.$router.push("/about?e=5");
                    break;
            }
        },
        async getInfoSite() {
            let data = await api.getInfoSite();
            if (data.status === 0) {
                this.onlineService = data.data.onlineService;
            } else {
                this.$store.commit("elAlertShow", {
                    elAlertShow: true,
                    elAlertText: data.msg,
                });
            }
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    padding-bottom: 1.3488rem;

    .top {
        background: #fff;
        padding-bottom: 0.3488rem;
    }

    .header {
        width: 100%;
        padding: 0.3488rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .banner {
        margin: 0.3488rem;
        margin-top: 0;
        border-radius: 0.2325rem;
        overflow: hidden;
        height: 4rem;

        img {
            width: 100%;
            height: 100%;
        }

        .van-swipe {
            height: 100%;
        }
    }

    .search {
        border: solid 1px rgba(224, 57, 54, 1);
        margin: 0.3488rem;
        margin-top: 0;
        height: 0.7441rem;
        border-radius: 0.2325rem;
        overflow: hidden;
        padding: 0 0.3488rem;
        display: flex;
        align-items: center;

        .search_icon {
            svg {
                width: 0.3488rem;
                height: 0.3488rem;
                fill: rgba(181, 181, 181, 1);
            }
        }

        span {
            color: rgba(181, 181, 181, 1);
            margin-left: 0.3488rem;
        }
    }

    .data {
        margin: 0.3488rem;
        border-radius: 0.2325rem;
        background: linear-gradient(180deg,
                rgba(255, 255, 255, 1) 0%,
                rgba(255, 252, 245, 1) 100%);

        .data_title {
            img {
                height: 0.3255rem;
                margin-left: 0.3488rem;
                margin-top: 0.3488rem;
            }
        }

        .data_layout {
            display: flex;
            padding: 0.3488rem;

            .line {
                background: rgba(242, 242, 242, 1);
                width: 1px;
                margin: 0 0.3488rem;
            }

            .data_item {
                flex: 1;

                .data_item_money {
                    margin-top: 0.3255rem;
                }
            }
        }
    }

    .stock {
        margin: 0.3488rem;
        border-radius: 0.2325rem;
        background: linear-gradient(180deg,
                rgba(255, 255, 255, 1) 0%,
                rgba(255, 245, 245, 1) 100%);

        .stock_title {
            img {
                height: 0.3255rem;
                margin-left: 0.3488rem;
                margin-top: 0.3488rem;
            }
        }

        .stock_swiper {
            display: flex;

            .index {
                flex: 1;
                padding: 0.3488rem;

                .index_num {
                    margin-top: 0.2325rem;
                }

                .index_text {
                    margin-top: 0.2325rem;
                }
            }
        }
    }

    .menu {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
        gap: 0.3488rem;
        padding: 0 0.3488rem;

        .home-item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .item-img {
                width: 0.6976rem;
                height: 0.6976rem;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .item-name {
                font-size: 0.3255rem;
                margin-top: 0.1162rem;
            }
        }
    }

    .news_layout {
        background: #fff;
        margin: 0.3488rem;
        border-radius: 0.2325rem;

        .news_title {
            img {
                height: 0.3255rem;
                margin-left: 0.3488rem;
                margin-top: 0.3488rem;
            }
        }

        .news_menu {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            padding: 0 0.3488rem;
            margin-top: 0.3488rem;
            gap: 0.3488rem;

            .news_menu_item {
                font-size: 0.372rem;
                text-align: center;
                line-height: 0.6976rem;
                border-radius: 0.1162rem;
                background: rgba(173, 173, 173, 0.05);

                &.active {
                    // background: #fff;
                    background: rgba(224, 57, 54, 0.05);
                    color: rgba(238, 0, 17, 1);
                }
            }
        }

        .news {
            .news_item {
                border-bottom: solid 1px rgba(223, 223, 223, 1);

                &:nth-last-child(1) {
                    border: none;
                }

                padding: 0.3488rem;

                .news_title {
                    font-size: 0.372rem;
                    line-height: 0.4651rem;
                }

                .news_time {
                    color: rgba(125, 125, 125, 1);
                    margin-top: 0.1162rem;
                    font-size: 0.279rem;
                }
            }
        }
    }
}
</style>