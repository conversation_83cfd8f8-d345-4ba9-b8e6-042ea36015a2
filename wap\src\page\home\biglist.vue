<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="天启护盘" left-arrow @click-left="$router.go(-1)" fixed right-text="记录" @click-right="$router.push({path: '/dazong?type=2'})"></van-nav-bar>
        </div>
        <div class="list">
            <div class="list_title">
                <div class="item">股票名称</div>
                <div class="item">最新价格</div>
                <div class="item">操作</div>
            </div>
            <div class="list_container">
                <van-list v-model="loading" :finished="finished" :immediate-check="false" :finished-text="$t('hj43')" @load="getOrderList(1)">
                    <template v-for="value in list">
                        <div class="item" :key="value.id">
                            <div class="ebox" style="justify-content: left;">
                                <div class="stock">
                                    <div class="name">{{value.stockName}}</div>
                                    <div class="child">
                                        <div class="tag">深</div>
                                        <div>{{value.stockCode}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="cbox">{{parseNumber(value.price)}}</div>
                            <div class="ebox" style="display: flex; justify-content: center; align-items: center;">
                                <div class="cbtn" @click="getdetail(value)">买入</div>
                            </div>
                        </div>
                    </template>
                </van-list>
            </div>
        </div>

        <van-popup v-model="show" round position="bottom">
            <div class="boxd">
                <div class="boxh"> 天启护盘 <span @click="show = false"></span></div>
                <div class="express-class" style="width: 9.48rem; margin: 0 auto;">
                    <div class="label-class" style="font-size: 0.37rem; color: #333; font-weight: 500; margin-top: 0.32rem;">仓位</div>
                    <div class="value-class" style="margin-top: 0.32rem; display: flex;">
                        <van-tag v-if="itemIndex == 0" size="medium" type="danger" @click="calculatePortion(1/4, 0)" style="flex: 1; font-size: 0.32rem; justify-content: center; height: 0.8rem;">1/4</van-tag>
                        <van-tag v-else size="medium" plain type="primary" @click="calculatePortion(1/4, 0)" style="flex: 1; font-size: 0.32rem; justify-content: center; height: 0.8rem;">1/4</van-tag>

                        <van-tag v-if="itemIndex == 1" size="medium" type="danger" @click="calculatePortion(1/3, 1)" style="flex: 1; margin: 0 0.32rem; font-size: 0.32rem; justify-content: center; height: 0.8rem;">1/3</van-tag>
                        <van-tag v-else size="medium" plain type="primary" @click="calculatePortion(1/3, 1)" style="flex: 1; margin: 0 0.32rem; font-size: 0.32rem; justify-content: center; height: 0.8rem;">1/3</van-tag>

                        <van-tag v-if="itemIndex == 2" size="medium" type="danger" @click="calculatePortion(1/2, 2)" style="flex: 1; font-size: 0.32rem; justify-content: center; height: 0.8rem;">1/2</van-tag>
                        <van-tag v-else size="medium" plain type="primary" @click="calculatePortion(1/2, 2)" style="flex: 1; font-size: 0.32rem; justify-content: center; height: 0.8rem;">1/2</van-tag>

                        <van-tag v-if="itemIndex == 3" size="medium" type="danger" @click="calculatePortion(1, 3)" style="flex: 1; margin-left: 0.32rem; font-size: 0.32rem; justify-content: center; height: 0.8rem;">全仓</van-tag>
                        <van-tag v-else size="medium" plain type="primary" @click="calculatePortion(1, 3)" style="flex: 1; margin-left: 0.32rem; font-size: 0.32rem; justify-content: center; height: 0.8rem;">全仓</van-tag>

                        <!-- <van-tag size="medium" plain type="primary" @click="calculatePortion(1/3, 1)">1/3</van-tag>
                    <van-tag size="medium" plain type="primary" @click="calculatePortion(1/2)">1/2</van-tag>
                    <van-tag size="medium" type="danger" @click="calculatePortion(1)">全仓</van-tag> -->
                    </div>
                </div>
                <h5>买入价格</h5>
                <h6>{{ currentItem.price ? currentItem.price : '' }}</h6>
                <div class="erty tghj"><input placeholder="请输入数量" type="number" class="inpy" v-model="num" @input="num = num.replace(/^(0+)|[^\d]+/g, '')"><a>手</a></div>
                <p class="plm"><span>购买金额</span><a>{{ currentItem.price ? (currentItem.price * num * 100).toFixed(2) :
                        '0.00'
                }}</a></p>
                <!--                <div class="erty"><input placeholder="请输入秘钥" type="password" class="inpy" v-model="password"></div>-->
                <p class="plm"><span>可用资金</span><a>{{ userinfo.enableAmt }}</a></p>
                <div class="maik" @click="getxiadan">买入</div>
            </div>
        </van-popup>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import BigNumber from "bignumber.js";

export default {
    data() {
        return {
            loading: false,
            finished: false,
            list: [],
            userinfo: {},
            show: false,
            itemIndex: -1,
            currentItem: {},
            num: "",
            settingDetail: {},
            bigNumbuy: 0,
            buyNum: 0,
        };
    },
    mounted() {
        this.getSetting(() => {
            this.stockgetDzList();
            this.getUserInfo();
        });
    },
    methods: {
        parseNumber(number) {
            return parseFloat(number).toFixed(2);
        },
        async getSetting(cb) {
            var opt = {};
            var data = await api.getSetting(opt);
            this.settingDetail = data.data;
            this.buyNum = this.settingDetail.buyMinNum / 100;
            cb();
        },
        stockgetDzList() {
            let _this = this;
            _this.loading = true;
            api.stockgetDzList().then((res) => {
                _this.list = res.data;
                _this.finished = true;
                _this.loading = false;
            });
        },
        getdetail(item) {
            const enableAmt = new BigNumber(this.userinfo.enableAmt);
            const nowPrice = new BigNumber(this.parseNumber(item.price));
            const buyFee = new BigNumber(this.settingDetail.buyFee);
            const buyTotal = enableAmt.dividedBy(
                nowPrice
                    .multipliedBy(1)
                    .multipliedBy(100)
                    .plus(nowPrice.multipliedBy(100).multipliedBy(buyFee))
            );
            this.bigNumbuy = buyTotal.integerValue(BigNumber.ROUND_FLOOR);
            item.price = this.parseNumber(item.price);
            this.currentItem = item;
            this.show = true;
        },
        async getUserInfo() {
            let data = await api.getUserInfo();
            if (data.status === 0) {
                this.userinfo = data.data;
            }
        },
        async getxiadan() {
            if (!this.num) {
                this.show = false;
                this.$toast("请输入数量");
                return;
            }
            var opt = {
                stockCode: this.currentItem.stockCode,
                password: "",
                num: this.num * 100,
            };
            let res = await api.buyStockDz(opt);
            if (res.status == 0) {
                this.$toast("买入成功");
            } else {
                this.$toast(res.msg);
            }
            this.show = false;
        },
        calculatePortion(fraction, index) {
            const portion = this.bigNumbuy
                .multipliedBy(fraction)
                .integerValue(BigNumber.ROUND_FLOOR);
            this.num = portion.toString();
            this.itemIndex = index;
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    background: #fff;
    min-height: 100vh;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .list {
        .list_title {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            background: rgba(240, 240, 240, 1);
            .item {
                flex: 1;
                text-align: center;
                line-height: 0.9302rem;
                color: rgba(125, 125, 125, 1);
            }
        }
        .list_container {
            .item {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                border-bottom: solid 1px rgba(223, 223, 223, 1);
                padding: 0.3488rem 0;
                .ebox {
                    .stock {
                        padding-left: 0.3488rem;
                        .name {
                            font-size: 0.372rem;
                        }
                        .child {
                            margin-top: 0.1162rem;
                            font-size: 0.3255rem;
                            display: flex;
                            .tag {
                                background: #6d9cff;
                                font-size: 0.2791rem;
                                color: #fff;
                                width: 0.3256rem;
                                height: 0.3256rem;
                                line-height: 0.3256rem;
                                text-align: center;
                                margin-right: 0.1162rem;
                            }
                        }
                    }
                }
                .cbox {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    line-height: 0.4651rem;
                }
            }
            .time {
                display: flex;
                justify-content: space-between;
                padding: 0 0.3488rem;
                color: rgba(125, 125, 125, 1);
            }
            .cbtn {
                display: block;
                background: rgba(238, 0, 17, 1);
                height: 0.6976rem;
                color: #fff;
                text-align: center;
                line-height: 0.6976rem;
                border-radius: 0.3488rem;
                width: 1.3953rem;
            }
        }
    }
}

.boxd {
    background: #fff;
    border-radius: 0.266rem 0.266rem 0 0;
    padding-bottom: 0.53rem;

    .boxh {
        height: 1.2rem;
        border-bottom: 0.0266rem solid #e0e0e0;
        text-align: center;
        line-height: 1.2rem;
        color: #333;
        font-size: 0.43rem;
        width: 9.48rem;
        margin: 0 auto;
        position: relative;

        span {
            position: absolute;
            width: 0.32rem;
            height: 0.32rem;
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAStJREFUSEutlk1qhDAUgF+EzG5EWsYTzK6HKO0hcgB3gscR3IgH8BBTeojuegJLQWg3NqDlyUQwRn0xcWeQ7zMv7ycMAKCu61PTNOcsy77x3fUpy/IhDMNfIcQfQ3jbtvUwDFfO+UuSJF8ugqqqLlLKN8bYZxRFguV5/sg5fweAJwD4cJEouGJJKZ8Z/m1RFHEQBDcXiQ7v+/41TdNmFLhK1uDInQRHJVvwhcBWsgc3CqgSCnxVsCehwjcFaxJcxzynZtzskE0Fpqfw/RtyzewKDDvBJXJBkgRazP0K9AP1GiIdjn2q6zpm01ZWQ2SCq05r07uMgi24be9aCChwG8lMYAOnSibBEThFMgpc4HsSryNTz65xZPoe+koyDX3cou9rC14k4jj+wWvLP1ylVM57GzhpAAAAAElFTkSuQmCC)
                no-repeat 50%;
            background-size: 100%;
            right: 0.266rem;
            top: 0.4rem;
        }
    }

    h5 {
        color: #333;
        font-size: 0.37rem;
        font-weight: 500;
        width: 9.48rem;
        margin: 0 auto;
        margin-top: 0.32rem;
    }

    h6 {
        color: #ea3544;
        font-size: 0.43rem;
        width: 9.48rem;
        margin: 0 auto;
        margin-top: 0.32rem;
        font-weight: 600;
    }

    .erty {
        width: 9.21rem;
        height: 1.07rem;
        border: 0.0266rem solid #999;
        border-radius: 0.13rem;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        margin-top: 0.59rem;

        .inpy {
            height: 1.07rem;
            width: 5.34rem;
            margin-left: 0.266rem;
            background: transparent;
            font-size: 0.37rem;
            color: #000;
        }

        a {
            height: 0.64rem;
            border-left: 0.0266rem solid #999;
            width: 1.15rem;
            margin-top: 0.266rem;
            text-align: center;
            font-size: 0.37rem;
            color: #000;
            line-height: 0.64rem;
        }
    }

    .tghj {
        border: 0.0266rem solid #3b4fde;
        margin-top: 0.45rem;
    }

    .plm {
        width: 8.94rem;
        margin: 0 auto;
        margin-top: 0.266rem;

        span {
            color: #999;
            font-size: 0.32rem;
        }

        a {
            color: #f33030;
            margin-left: 0.11rem;
        }
    }

    .maik {
        width: 9.21rem;
        height: 1.07rem;
        background: linear-gradient(
            -55deg,
            rgb(80, 122, 250),
            rgb(115, 131, 251)
        );
        border-radius: 0.26rem;
        margin: 0 auto;
        margin-top: 0.56rem;
        text-align: center;
        line-height: 1.07rem;
        color: #fff;
        font-size: 0.37rem;
    }
}
</style>