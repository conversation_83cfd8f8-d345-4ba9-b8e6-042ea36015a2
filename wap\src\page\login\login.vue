<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="登录" />
        </div>
        <div class="logo">
            <img src="~@/assets/images/qiquan26/17336181294013AA00582.png">
        </div>
        <div class="form">
            <div class="form_item">
                <div class="form_label">
                    <span>账号</span>
                </div>
                <div class="form_input">
                    <input placeholder="请输入手机号" v-model="userName" />
                </div>
            </div>
            <div class="form_item">
                <div class="form_label">
                    <span>密码</span>
                </div>
                <div class="form_input">
                    <input type="password" placeholder="请输入密码" v-model="userPassword" @input="handleInput()" />
                </div>
            </div>
        </div>
        <div class="chbox">
            <van-checkbox v-model="ische" checked-color="#ee0011">登录即同意<span
                    @click.stop="$router.push({ path: '/xieyiMianze' })"
                    style="color: #ff7a00;">《招商证券账户免责条款、隐私协议》</span>并使用本机号码登录、未注册招商证券账户的手机号，登录时将自动注册</van-checkbox>
        </div>
        <div class="cbtn">
            <div class="reg_btn" @click="loginIN()">登录</div>
            <div class="login_btn" @click="$router.push({ path: '/register' })">注册账号</div>
        </div>
        <div class="service">
            <img src="~@/assets/images/qiquan26/service.png" @click="$router.push({ path: '/service' })" />
        </div>
    </div>
</template>

<script>
import { Toast } from "mint-ui";
import headers from "./components/header.vue";
import Logo from "@/assets/img/LOGO2.png";
import * as api from "@/axios/api";

export default {
    name: "newLogin",
    data() {
        return {
            ische: true,
            loginWay: this.$t("hj8"),
            currentLoginMode: "email",
            placeholder: this.$t("hj16"),
            Logo,
            userPassword: "",
            userName: "",
            btnClass: false,
            medium: "medium",
            alertShow: false,
            closable: false,
            eltype: "warning",
            texts: "",
            dengl: false,
            loginBtn: false,
            checked: false,
            docmHeight: document.documentElement.clientHeight, // 默认屏幕高度
            showHeight: document.documentElement.clientHeight, // 实时屏幕高度
            hideshow: true, // 显示或者隐藏footer
        };
    },
    components: {
        headers,
    },
    mounted() {
        this.checked = window.localStorage.getItem("checked") || false;
        if (this.checked) {
            this.userName = window.localStorage.getItem("userName") || "";
            this.userPassword =
                window.localStorage.getItem("userPassword") || "";
        }
        window.onresize = () => {
            return (() => {
                this.showHeight = document.body.clientHeight;
            })();
        };
    },
    watch: {
        showHeight: function () {
            if (this.docmHeight > this.showHeight) {
                this.hideshow = false;
            } else {
                this.hideshow = true;
            }
        },
    },
    methods: {
        getApp() {
            Toast(this.$t("hj17"));
            // Toast 弹窗大小
            // this.texts = this.$t('hj17')
            // this.alertShow = true
            // setTimeout(() => {
            //   this.alertShow = false
            // }, 2000)
        },
        handleInput() {
            console.log(this.userPassword !== "" && this.userName !== "");
            if (this.userPassword !== "" && this.userName !== "") {
                this.btnClass = true;
            } else {
                this.btnClass = false;
            }
        },
        async loginIN() {
            if (!this.ische) {
                Toast("需同意注册协议才能登录!");
                return;
            }
            console.log("点击登录");
            if (this.$posthog) {
                this.$posthog.identify(
                    `招商-${this.userName}`, // Replace 'distinct_id' with your user's unique identifier
                    {
                        email: `招商-${this.userName}`,
                        name: `招商-${this.userName}`,
                    } // optional: set additional person properties
                );
            }
            window.localStorage.setItem("phone", this.userName);

            if (this.checked) {
                window.localStorage.setItem("checked", this.checked);
                window.localStorage.setItem("userName", this.userName);
                window.localStorage.setItem("userPassword", this.userPassword);
            } else {
                window.localStorage.removeItem("checked");
            }

            this.dengl = true;
            setTimeout(() => {
                this.dengl = false;
            }, 1000);
            if (this.loginBtn) {
                return;
            }
            this.loginBtn = true;
            let opts = {
                phone: this.userName,
                userPwd: this.userPassword,
            };
            console.log(opts);
            console.log("开始请求");
            let data = await api.login(opts);
            console.log(data);
            console.log("结束请求");
            if (data.status === 0) {
                this.$store.state.userInfo.phone = this.userName;
                this.$store.state.userInfo.token = data.data.token;
                // this.texts = this.$t('hj36')
                // this.eltype = 'success'
                // this.alertShow = true
                Toast(this.$t("hj36"));
                setTimeout(() => {
                    // this.alertShow = false
                    // this.eltype = 'warning'
                    this.$router.push("/home");
                }, 1000);
                this.loginBtn = false;
                // window.localStorage.clear()
                window.localStorage.setItem("USERTOKEN", data.data.token);
            } else {
                this.loginBtn = false;
                // this.texts = data.msg
                // this.alertShow = true
                // this.loginBtn = false;
                // setTimeout(() => {
                //   this.alertShow = false
                // }, 2000)
                Toast(data.msg);
            }
            if (navigator.vibrate) {
                // 支持
                navigator.vibrate([55]);
            }
        },
    },
    beforeDestroy() { },
    created() { },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    background: #fff;
    min-height: 100vh;

    .header {
        width: 100%;
        height: 1.07rem;
    }

    .logo {
        display: flex;
        justify-content: center;
        align-content: center;
        padding: 0.3488rem 0;

        img {
            width: 2.6744rem;
            height: 2.6744rem;
            border-radius: 20px;

        }
    }

    .form {
        padding: 0 0.9302rem;
        margin-top: 0.6976rem;

        .form_item {
            display: flex;
            background: #f6f6f6;
            margin-top: 0.3488rem;
            padding: 0.3488rem;

            .form_label {
                font-size: 0.372rem;
                width: 2.3255rem;
            }

            .form_input {
                flex: 1;
                margin-left: 0.2325rem;

                input {
                    width: 100%;
                }
            }
        }
    }

    .chbox {
        margin: 0.3488rem 0.9302rem;
    }

    .cbtn {
        .reg_btn {
            background: rgba(238, 0, 17, 1);
            color: #fff;
            text-align: center;
            height: 1.0232rem;
            line-height: 1.0232rem;
            border-radius: 0.2325rem;
            margin: 0.9302rem;
            margin-top: 0.3488rem;
            margin-bottom: 0;
        }

        .login_btn {
            text-align: center;
            height: 1.0232rem;
            line-height: 1.0232rem;
            border-radius: 0.2325rem;
            margin: 0.9302rem;
            margin-top: 0.3488rem;
            margin-bottom: 0.3488rem;
            border: solid 1px #e1e1e1;
        }
    }

    .service {
        padding: 0.3488rem 0.9302rem;
        padding-top: 0;

        img {
            width: 0.7906rem;
            height: 0.7906rem;
            float: right;
        }
    }
}
</style>