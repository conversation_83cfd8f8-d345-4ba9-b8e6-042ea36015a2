<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="持仓详情" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>
        <div class="ebox">
            <div class="elabel">股票代码</div>
            <div class="enr">{{currentItem.stockCode}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">股票名称</div>
            <div class="enr">{{currentItem.stockName}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">持股数</div>
            <div class="enr">{{currentItem.buyNum}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">买入价格</div>
            <div class="enr">{{currentItem.buyOrderPrice}}</div>
        </div>
        <div class="ebox" v-if="currentItem.sellOrderId">
            <div class="elabel">卖出价格</div>
            <div class="enr">{{currentItem.sellOrderPrice}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">买入市值</div>
            <div class="enr">{{currentItem.buyPrice}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">手续费</div>
            <div class="enr">{{currentItem.orderFee}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">印花税</div>
            <div class="enr">{{currentItem.orderSpread}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">盈亏</div>
            <div class="enr">{{parseNumber(currentItem.profitAndLose)}}</div>
        </div>
        <div class="ebox">
            <div class="elabel">盈亏比例</div>
            <div :class="`enr ${currentItem.profitAndLossRatio > 0 ? 'red' : 'green'}`">{{parseNumber(currentItem.profitAndLossRatio)}}%</div>
        </div>
        <div class="ebox">
            <div class="elabel">买入时间</div>
            <div class="enr">{{ dayjs(currentItem.buyOrderTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
        </div>
        <div class="ebox" v-if="currentItem.sellOrderId">
            <div class="elabel">卖出时间</div>
            <div class="enr">{{ dayjs(currentItem.sellOrderTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
        </div>
        <div class="ebtn" v-if="currentItem.sellOrderId" @click="$router.go(-1)">返回</div>
        <div class="ebtn" v-else @click="getpingcang(currentItem)">我要平仓</div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { MessageBox, Toast } from "mint-ui";

export default {
    data() {
        return {
            currentItem: {},
        };
    },
    mounted() {
        this.currentItem = JSON.parse(
            decodeURIComponent(this.$route.query.item)
        );
        console.log(this.currentItem);
    },
    methods: {
        parseNumber(number) {
            return parseFloat(number).toFixed(2);
        },
        getpingcang(val) {
            let _this = this;
            if (val.sellOrderId != null) {
                return;
            }

            const that = this;
            MessageBox.confirm(this.$t("hj139") + "?", this.$t("hj165"), {
                confirmButtonText: this.$t("hj161"),
                cancelButtonText: this.$t("hj106"),
            })
                .then(async () => {
                    let opt = {
                        positionSn: val.positionSn,
                    };
                    let data = await api.sell(opt);
                    if (data.status === 0) {
                        Toast(data.msg);
                        _this.$router.go(-1);
                    } else if (data.msg.indexOf("不在交易时段内") > -1) {
                        Toast(this.$t("hj140"));
                    } else {
                        Toast(data.msg);
                    }
                })
                .catch((e) => {
                    Toast(e.toString());
                });
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    background: #fff;
    min-height: 100vh;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .ebox {
        border-bottom: solid 1px #f1f1f1;
        display: flex;
        justify-content: space-between;
        margin: 0 0.3488rem;
        padding: 0.3488rem 0;
        .elabel {
            color: rgba(125, 125, 125, 1);
        }
    }
    .ebtn {
        display: block;
        background: rgba(238, 0, 17, 1);
        height: 0.9302rem;
        color: #fff;
        text-align: center;
        line-height: 0.9302rem;
        border-radius: 0.4651rem;
        margin: 0.3488rem;
    }
}
</style>