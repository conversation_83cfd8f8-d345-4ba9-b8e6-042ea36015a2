<template>
    <div class="container">
        <div class="header">
            <van-nav-bar title="新股详情" left-arrow @click-left="$router.go(-1)" fixed></van-nav-bar>
        </div>
        <div class="layout">
            <div class="layout_item">
                <div class="title">基本信息</div>
                <div class="ebox">
                    <div>
                        <span>股票代码：</span>
                        <span>{{currentItem.code}}</span>
                    </div>
                    <div>
                        <span>申购代码：</span>
                        <span>{{currentItem.code}}</span>
                    </div>
                    <div>
                        <span>股票名称：</span>
                        <span>{{currentItem.name}}</span>
                    </div>
                    <div>
                        <span>所属板块：</span>
                        <span>{{currentItem.stockType}}</span>
                    </div>
                </div>
            </div>
            <div class="layout_item">
                <div class="title" style="margin-top: 0.3488rem;">发行信息</div>
                <div class="ebox">
                    <div>
                        <span>发行价格</span>
                        <span>￥{{currentItem.price}} / 股</span>
                    </div>
                    <div>
                        <span>发行数量</span>
                        <span>{{currentItem.orderNumber}} 万股</span>
                    </div>
                    <div>
                        <span>市盈率</span>
                        <span>{{currentItem.pe}}</span>
                    </div>
                    <div>
                        <span>申购时间</span>
                        <span>{{ dayjs(currentItem.subscribeTime).format('YYYY-MM-DD') }}</span>
                    </div>
                </div>
            </div>
            <div class="ebtn" @click="getshengou">立即申购</div>
        </div>
    </div>
</template>

<script>
import * as api from "@/axios/api";
import { Toast, MessageBox } from "mint-ui";
export default {
    components: {},
    props: {},
    data() {
        return {
            currentItem: "",
            userinfo: {},
        };
    },
    mounted() {
        this.currentItem = JSON.parse(
            decodeURIComponent(this.$route.query.item)
        );
        this.getUserInfo();
    },
    methods: {
        getUserInfo() {
            let _this = this;
            api.getUserInfo().then((res) => {
                _this.userinfo = res.data;
            });
        },
        getshengou() {
            let _this = this;
            MessageBox.confirm(
                "确定申购" + _this.currentItem.name + "?",
                _this.$t("hj165"),
                {
                    confirmButtonText: _this.$t("hj161"),
                    cancelButtonText: this.$t("hj106"),
                }
            )
                .then(() => {
                    let totalNum = Math.floor(
                        _this.userinfo.enableAmt / _this.currentItem.price
                    );
                    if (totalNum < 1) {
                        totalNum = 0;
                    }
                    const opt = {
                        newCode: _this.currentItem.code,
                        applyNums: totalNum,
                        phone: _this.userinfo.phone,
                        type: 1,
                    };
                    api.getNewAdd(opt).then((res) => {
                        console.log(res);
                        if (res.status == 0) {
                            _this.$toast("申购成功");
                            _this.$router.go(-1);
                        } else {
                            _this.$toast(res.msg);
                        }
                    });
                })
                .catch(() => {});
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-size: 0.3256rem;
    padding: 0;
    // padding-bottom: 1.3488rem;
    // height: 100vh;
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .layout {
        // padding: 0.3488rem;
        // background: #fff;
        // margin-top: 0.3488rem;
        // min-height: calc(100vh - 1.4188rem);

        .layout_item {
            background: #fff;
            margin-top: 0.3488rem;
            padding: 0.3488rem;
        }

        .title {
            font-size: 0.372rem;
        }
        .ebox {
            // box-shadow: 0 0 10px #0000001a;
            // border-radius: 0.2325rem;
            // background: #fff;
            // margin-top: 0.3488rem;
            // padding: 0.3488rem;
            margin-top: 0.3488rem;
            // font-size: 0.3255rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.3488rem;
            div {
                span:nth-of-type(1) {
                    color: rgba(125, 125, 125, 1);
                }
            }
        }
        .cbox {
            box-shadow: 0 0 10px #0000001a;
            border-radius: 0.2325rem;
            padding: 0.3488rem;
            margin-top: 0.3488rem;
            font-size: 0.3255rem;
            padding-top: 0;
            div {
                display: flex;
                justify-content: space-between;
                padding-top: 0.3488rem;
                span:nth-of-type(1) {
                    color: rgba(125, 125, 125, 1);
                }
            }
        }
        .ebtn {
            border-radius: 8px;
            background: rgba(215, 12, 24, 1);
            box-shadow: 0px 2px 4px rgba(224, 57, 54, 0.49);
            height: 1.1162rem;
            line-height: 1.1162rem;
            text-align: center;
            color: #fff;
            position: absolute;
            bottom: 0.3488rem;
            left: 5%;
            right: 5%;
        }
    }
}
</style>