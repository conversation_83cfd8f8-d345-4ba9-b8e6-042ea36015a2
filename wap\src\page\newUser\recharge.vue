<template>
    <div :class="`container ${type == 0 ? 'bg' : ''}`">
        <div class="header">
            <van-nav-bar title="银证转账" left-arrow fixed @click-left="$router.go(-1)" />
        </div>
        <template v-if="type == 0">
            <div class="content">
                <div class="title">简介说明</div>
                <div class="text">
                    <p>由于该账户受证券监管部门监督，根据证监部门2021反洗黑钱金融条例，为了避免出现不法分子黑钱投入机构单元账户，导致三方存管账户全面冻结带来的损失，因采取更高效审查机制支付通道，用户需通过三方存管和银行之间搭建的支付通道风控机制，进行出入金交易，三方存管账户进行一道关卡过滤黑钱！</p>
                    <p>目前机构共有四种支付方式进行注资存款：</p>
                    <p>1.银证转入—小额无卡注资额度1-5万元</p>
                    <p>2.银行划扣一通过第三方存管公司进行转账划扣</p>
                    <p>3.现金注资—工作人员上门收取现金完成注资10万元以上</p>
                    <p>4.黄金通道注资—通过黄金商家合作购买投资类金条置换注资﻿</p>
                    <p>各位机构成员如有不了解的地方请联系您的专属顾问进行了解！</p>
                </div>
            </div>
            <div class="bt-class">
                <van-button type="info" size="large" round class="bt-s-class" @click="silverTransfersInDescription">联系客服转入</van-button>
            </div>
        </template>
        <template v-if="type == 1">
            <div class="recharge_container">
                <div class="recharge_container_bg"></div>
                <div class="main">
                    <div class="main_top">
                        <div class="main_top_title">银证转入</div>
                        <input type="text" :placeholder="`请输入金额 ${channelMinLimit} - ${channelMaxLimit}`" v-model="price">
                    </div>
                    <div class="ebtn" @click="checkPriceEvent">确认充值</div>
                    <div class="main_bottom">
                        <div class="main_bottom_title">简介说明</div>
                        <div class="main_bottom_content">
                            <p>由于该账户受证券监管部门监督，根据证监部门2021反洗黑钱金融条例，为了避免出现不法分子黑钱投入机构交易单元席位，导致三方存管账户全面冻结带来的损失，因采取更高效审查机制支付通道，用户需通过三方存管和银行之间搭建的支付通道风控机制，进行出入金交易，三方存管账户进行一道关卡过滤黑钱！</p>
                            <p>目前机构共有三种支付方式进行注资存款：</p>
                            <p>1.银证转入一小额无卡注资</p>
                            <p>2.现金注资一工作人员上门服务收取现金完成注资</p>
                            <p>3.银行划扣一通过第三方存管公司进行转账划扣</p>
                            <p>各位机构成员如有不了解的地方请联系您的专属顾问进行咨询！</p>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>
<script>
import * as api from "@/axios/api";
import { Toast } from "mint-ui";

export default {
    name: "recharge",
    data() {
        return {
            type: 1, // 1 快捷支付
            price: "",
            channelMinLimit: 0,
            channelMaxLimit: 0,
        };
    },
    mounted() {
        if (this.type == 1) {
            this.getPayInfoEvent();
        }
    },
    methods: {
        silverTransfersInDescription() {
            this.$router.push({
                path: "/service",
            });
        },
        checkPriceEvent() {
            if (!/^[0-9]+.?[0-9]*$/.test(this.price)) {
                Toast("请输入正确的金额");
                return;
            }
            if (this.price < this.channelMinLimit) {
                Toast(
                    "金额不能小于" + this.useFormatMoney(this.channelMinLimit)
                );
                return;
            }
            if (this.price > this.channelMaxLimit) {
                Toast(
                    "金额不能大于" + this.useFormatMoney(this.channelMaxLimit)
                );
                return;
            }
            this.$router.push({
                path: "/rechargePay",
                query: {
                    price: this.price,
                },
            });
        },
        useFormatMoney(price, useCurrencySymbol = true, currency = "CNY") {
            const options = {
                minimumFractionDigits: 2, // 最少显示 0 位小数
                maximumFractionDigits: 6, // 最多显示 6 位小数
            };
            if (useCurrencySymbol) {
                options.style = "currency";
                options.currency = currency;
            }
            const number = Number(price || 0); // 确保 price 为数字，即使为 0
            if (isNaN(number)) {
                throw new Error(
                    "Invalid input: price must be a number--->" + price
                );
            }
            // 格式化数字，根据是否包含货币符号
            return number.toLocaleString(undefined, options);
        },
        getPayInfoEvent() {
            const _this = this;
            api.getPayInfo().then((res) => {
                _this.channelMinLimit = res.data[0].channelMinLimit;
                _this.channelMaxLimit = res.data[0].channelMaxLimit;
            });
        },
    },
};
</script>
<style scoped lang="less">
.container {
    padding: 0;
    min-height: 100vh;
    &.bg {
        background: #fff;
    }
    .header {
        width: 100%;
        height: 1.07rem;
    }
    .content {
        padding: 30px;

        .title {
            text-align: center;
            font-size: 0.4272rem;
            font-weight: bolder;
            margin-bottom: 20px;
        }

        .text {
            p {
                font-size: 0.3467rem;
                line-height: 0.5333rem;
                color: #333;
                margin-bottom: 0.2667rem;
                text-indent: 2em;
                color: red;
            }
        }
    }
    .recharge_container {
        .recharge_container_bg {
            background: linear-gradient(
                180deg,
                rgba(224, 57, 54, 1) 0%,
                rgba(224, 57, 54, 0) 100%
            );
            position: absolute;
            left: 0;
            right: 0;
            top: 1.07rem;
            height: calc(100vh * (100 / 3 / 100));
            z-index: 0;
        }
        .main {
            background: #fff;
            border-radius: 0.2325rem;
            padding: 0.3488rem;
            position: absolute;
            left: 0.4651rem;
            right: 0.4651rem;
            top: calc(1.07rem + 0.4651rem);
            z-index: 1;

            .ebtn {
                border-radius: 8px;
                background: rgba(215, 12, 24, 1);
                box-shadow: 0px 2px 4px rgba(224, 57, 54, 0.49);
                height: 1.1162rem;
                line-height: 1.1162rem;
                text-align: center;
                color: #fff;
                margin-top: 0.4651rem;
            }
            .main_top {
                .main_top_title {
                    font-size: 0.372rem;
                }
                input {
                    border-bottom: solid 1px rgba(201, 201, 201, 1);
                    width: 100%;
                    margin-top: 0.3488rem;
                    height: 0.8372rem;
                    font-size: 0.372rem;
                }
            }
            .main_bottom {
                background: rgba(245, 247, 250, 1);
                border-radius: 0.2325rem;
                padding: 0.3488rem;
                margin-top: 0.4651rem;
                .main_bottom_title {
                    font-size: 0.372rem;
                }
                .main_bottom_content {
                    margin-top: 0.3488rem;
                    line-height: 200%;
                    font-size: 0.3255rem;
                }
            }
        }
    }
}

.bt-class {
    width: 80%;
    margin: 0 auto;
    margin-top: 20px;
    .bt-s-class {
        height: 55px;
    }
}
.head {
    width: 100%;
    background: linear-gradient(-55deg, rgb(223, 48, 28), rgb(223, 48, 28));
    text-align: center;
}

.head h2 {
    text-align: center;
    height: 1.2549rem;
    width: 100%;
    position: relative;
    line-height: 1.2549rem;
    font-size: 0.4806rem;
    color: #fff;
    background: transparent;
    font-weight: 500;
    z-index: 3;
}

.hbnh {
    position: absolute;
    left: 0.4005rem;
    font-size: 0.4272rem;
    font-weight: 500;
}

.fan {
    width: 0.2403rem;
    height: 0.4272rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAAAXNSR0IArs4c6QAAAVdJREFUSEut1usqBVEYxvH/c1MuRJJDkhwTEpIkJUlyCEk++CJJckpycw49Gs3Wa+y9Z82ePd/Xr5n38KwRNR7bE0APMK1OHdtTwAWQGZcdQbZngPMc+QImK0O2Z4GzgIxLuqoE2Z4DTgMyJuk6K08yZHseOAnIqKSbRo2TINsLwHFARiTdxkaVQrYXgaOADEu6K3a7LWR7CTjMD2XdaYq0rZHtFWA/Rz6BIUn3reau6RvZXgX28kMfOfLQbnj/QbbXgN380HuOPJZtwB/I9jqwE5ABSc9lyJ8a2d4AtgPSL+klBfmFbG8CWwHpk/SaivxAtjMgg7Inq0mvpLcqSHehTOvKpzU+oSvFDlj99gesOJCDkp7Kip+yIknT3XL7C0tbum9lMbIMHKQkQEqwJWVSKZTPWTElq0dt6GYxt6uHf8DqX0cBq39BBqz+lR2w+j8RAfv9rfkGqF24CUdT9E4AAAAASUVORK5CYII=)
        no-repeat 50%;
    background-size: 100%;
    display: inline-block;
    margin-right: 0.1335rem;
    vertical-align: middle;
    margin-top: -0.0534rem;
}

.xind {
    position: absolute;
    right: 0.4005rem;
    color: #fff;
    font-size: 0.3738rem;
    font-weight: 500;
}

.shaux {
    position: absolute;
    width: 0.4806rem;
    height: 0.4806rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAA8VJREFUWEfNmEloXVUYx3//e18qCWitA4JUFLEURdOotcWpEKsLFbUVBONKKYIbRcEJ3VhwarvSiAspVlzoRpyow0Lr0No2LbW4cACllCK2DhREjWhy71/OzU087/W9vPuSl5K7u2f4zu9+9xuPmGeP5hkPlYFsLyTjBsQgZgBxDrAQMPA75gBiP+YTUrZK+nsmH9sWyPZSch4GhoDeiof8CbxCwiZJhyruKZa1BLLdR86TwL1ArROh0dqgpY0kPCVprIqMpkC2l5DzJnBhEyGjiBHMt8DRcv5UxPmYlU21aPaQslbST+2gjgGyfSk5HwCn1202O4DnSXlX0j/NBNvuJeMW4D7E5Q1rfiRhUNIP00HVAdk+j5ydDTBHMPeopnfafV0873HfjhgGTovGA9QKSYdbyZoCKm1mpO43mRFSbpb0Sycwk2ttLyZnK7Bsar/ZScoqSVkzmf8DZd4EPBhtDDCrJf01E5gIahE5nwEXRXIeUaqNLYFKI/4a6CkXHSbhYkk/zwYmgjqLnK+AReXYHyQsaSa/0JAzvwzcFWlnrWp6uxswU1DjvhOxJZK5QakebTxDtk8k5wjQV0yaHarp6m7CFGLthJwvI3v6jYQzG+OTSm94PdLObarpjW4DFVDjXofYPCU75zr16KP4LDnzS8Dd5eAoCae0ijOzhbQdDPxXIC1lPa1Uj9cDjXs3KiJsSCTblGj1bA+ebr8z7wcGyvM+VKLrGzUUiCeD1wtKFXJX154ivsEJkUlsxtxavn9HwhWNQCENLCgH1yvVE12jCXYz5kESPp4ukUfnbQk2NKdAhTFnfq7Ib9M/h0joD0Bz+stKl+8lJ9jO0hZMJuda9WhbcPvjYtS2V5LzReRhMduwUhUaPL5unzkUfHVuDnxPwoCk0QmgcQ8hXou8YO4Co72AnD1RtM5IuErS7snzm6WO7appVTc9LZZlu5+cvaVnP6NUj9W5fekFIZyvi7S0ptOCrJMPcOaQVIdIuEzSv8cCTVSK38xV+dEIazukjrMlHWiciwu0UDA9FGmpKwVaJ5orjHpyQ1nC7gL6G6BmU8KeCyyW9HlVsDkp8m2LjDsQLwJjJCyXdLAKVLM26BJy3gfOqBNgtgPDbdqgPjLWAA8glkf795FwZZWyplWjGIz8rY4aRXMBYkWLRnEvKTdVqdHbtdLrgftn0UqHxL2hbKXr3LvV76ty2RDa6om4Uf2yIbROr5LwbNcuG5rEjpPIuBFxDWZZeR1zcumpRzEHEfswn5Ly3kz7ubYaquIZ3Vwz74D+A2+DgOcUuMpOAAAAAElFTkSuQmCC)
        no-repeat 50%;
    background-size: 100%;
    right: 0.4005rem;
    top: 0.4005rem;
}

.head h6 {
    font-weight: 500;
    font-size: 0.6408rem;
    color: #fff;
    margin-top: 0.9345rem;
}

.head p {
    color: #fff;
    font-size: 0.3204rem;
    margin-top: 0.3738rem;
}
</style>