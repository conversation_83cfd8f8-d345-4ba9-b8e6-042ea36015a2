webpackJsonp([13],{"+0UJ":function(t,e){},"+yag":function(t,e,i){t.exports=i.p+"static/img/2.32e52e4.png"},"1zby":function(t,e,i){var n;n=function(){"use strict";var t={name:"BBI",shortName:"BBI",series:"price",precision:2,calcParams:[3,6,12,24],shouldCheckParamCount:!0,shouldOhlc:!0,plots:[{key:"bbi",title:"BBI: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=Math.max.apply(null,i),r=[],a=[];return t.map(function(e,o){var s={},c=e.close;if(i.forEach(function(e,i){r[i]=(r[i]||0)+c,e-1>o||(a[i]=r[i]/e,r[i]-=t[o-(e-1)].close)}),o>=n-1){var h=0;a.forEach(function(t){h+=t}),s.bbi=h/4}return s})}},e={name:"DMA",shortName:"DMA",calcParams:[10,50,10],plots:[{key:"dma",title:"DMA: ",type:"line"},{key:"ama",title:"AMA: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=Math.max(i[0],i[1]),r=0,a=0,o=0,s=[];return t.forEach(function(e,c){var h,l,u={},f=e.close;if(r+=f,a+=f,i[0]-1>c||(h=r/i[0],r-=t[c-(i[0]-1)].close),i[1]-1>c||(l=a/i[1],a-=t[c-(i[1]-1)].close),c>=n-1){var d=h-l;u.dma=d,o+=d,n+i[2]-2>c||(u.ama=o/i[2],o-=s[c-(i[2]-1)].dma)}s.push(u)}),s}},i={name:"DMI",shortName:"DMI",calcParams:[14,6],plots:[{key:"pdi",title:"PDI: ",type:"line"},{key:"mdi",title:"MDI: ",type:"line"},{key:"adx",title:"ADX: ",type:"line"},{key:"adxr",title:"ADXR: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=0,o=0,s=0,c=0,h=0,l=0,u=[];return t.forEach(function(e,f){var d={},v=t[f-1]||e,p=v.close,_=e.high,m=e.low,y=_-m,x=Math.abs(_-p),g=Math.abs(p-m),S=_-v.high,k=v.low-m,w=Math.max(Math.max(y,x),g),b=S>0&&S>k?S:0,E=k>0&&k>S?k:0;if(n+=w,r+=b,a+=E,f>=i[0]-1){f>i[0]-1?(o=o-o/i[0]+w,s=s-s/i[0]+b,c=c-c/i[0]+E):(o=n,s=r,c=a);var P=0,C=0;0!==o&&(P=100*s/o,C=100*c/o),d.pdi=P,d.mdi=C;var A=0;C+P!==0&&(A=Math.abs(C-P)/(C+P)*100),h+=A,2*i[0]-2>f||(d.adx=l=f>2*i[0]-2?(l*(i[0]-1)+A)/i[0]:h/i[0],2*i[0]+i[1]-3>f||(d.adxr=(u[f-(i[1]-1)].adx+l)/2))}u.push(d)}),u}},n={name:"MACD",shortName:"MACD",calcParams:[12,26,9],plots:[{key:"dif",title:"DIF: ",type:"line"},{key:"dea",title:"DEA: ",type:"line"},{key:"macd",title:"MACD: ",type:"bar",baseValue:0,color:function(t,e){var i=(t.current.technicalIndicatorData||{}).macd;return i>0?e.bar.upColor:0>i?e.bar.downColor:e.bar.noChangeColor},isStroke:function(t){return(t.current.technicalIndicatorData||{}).macd>(t.prev.technicalIndicatorData||{}).macd}}],calcTechnicalIndicator:function(t,e){var i,n,r=e.params,a=0,o=0,s=0,c=0,h=Math.max(r[0],r[1]);return t.map(function(t,e){var l={},u=t.close;return a+=u,r[0]-1>e||(i=e>r[0]-1?(2*u+(r[0]-1)*i)/(r[0]+1):a/r[0]),r[1]-1>e||(n=e>r[1]-1?(2*u+(r[1]-1)*n)/(r[1]+1):a/r[1]),h-1>e||(l.dif=o=i-n,s+=o,h+r[2]-2>e||(l.macd=2*(o-(c=e>h+r[2]-2?(2*o+c*(r[2]-1))/(r[2]+1):s/r[2])),l.dea=c)),l})}},r={name:"CR",shortName:"CR",calcParams:[26,10,20,40,60],plots:[{key:"cr",title:"CR: ",type:"line"},{key:"ma1",title:"MA1: ",type:"line"},{key:"ma2",title:"MA2: ",type:"line"},{key:"ma3",title:"MA3: ",type:"line"},{key:"ma4",title:"MA4: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=Math.ceil(i[1]/2.5+1),r=Math.ceil(i[2]/2.5+1),a=Math.ceil(i[3]/2.5+1),o=Math.ceil(i[4]/2.5+1),s=0,c=[],h=0,l=[],u=0,f=[],d=0,v=[],p=[];return t.forEach(function(e,_){var m={},y=t[_-1]||e,x=(y.high+y.close+y.low+y.open)/4,g=Math.max(0,e.high-x),S=Math.max(0,x-e.low);i[0]-1>_||(m.cr=0!==S?g/S*100:0,s+=m.cr,h+=m.cr,u+=m.cr,d+=m.cr,i[0]+i[1]-2>_||(c.push(s/i[1]),i[0]+i[1]+n-3>_||(m.ma1=c[c.length-1-n]),s-=p[_-(i[1]-1)].cr),i[0]+i[2]-2>_||(l.push(h/i[2]),i[0]+i[2]+r-3>_||(m.ma2=l[l.length-1-r]),h-=p[_-(i[2]-1)].cr),i[0]+i[3]-2>_||(f.push(u/i[3]),i[0]+i[3]+a-3>_||(m.ma3=f[f.length-1-a]),u-=p[_-(i[3]-1)].cr),i[0]+i[4]-2>_||(v.push(d/i[4]),i[0]+i[4]+o-3>_||(m.ma4=v[v.length-1-o]),d-=p[_-(i[4]-1)].cr)),p.push(m)}),p}},a={name:"AO",shortName:"AO",calcParams:[5,34],shouldCheckParamCount:!0,plots:[{key:"ao",title:"AO: ",type:"bar",baseValue:0,color:function(t,e){return(t.current.technicalIndicatorData||{}).ao>(t.prev.technicalIndicatorData||{}).ao?e.bar.upColor:e.bar.downColor},isStroke:function(t){return(t.current.technicalIndicatorData||{}).ao>(t.prev.technicalIndicatorData||{}).ao}}],calcTechnicalIndicator:function(t,e){var i=e.params,n=Math.max(i[0],i[1]),r=0,a=0,o=0,s=0;return t.map(function(e,c){var h={},l=(e.low+e.high)/2;if(r+=l,a+=l,c>=i[0]-1){o=r/i[0];var u=t[c-(i[0]-1)];r-=(u.low+u.high)/2}if(c>=i[1]-1){s=a/i[1];var f=t[c-(i[1]-1)];a-=(f.low+f.high)/2}return n-1>c||(h.ao=o-s),h})}},o={name:"CCI",shortName:"CCI",calcParams:[20],plots:[{key:"cci",title:"CCI: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=i[0]-1,r=0,a=[];return t.map(function(e,o){var s={},c=(e.high+e.low+e.close)/3;if(r+=c,a.push(c),o>=n){var h=r/i[0],l=0;a.slice(o-n,o+1).forEach(function(t){l+=Math.abs(t-h)});var u=l/i[0];s.cci=0!==u?(c-h)/u/.015:0,r-=(t[o-n].high+t[o-n].low+t[o-n].close)/3}return s})}},s={name:"RSI",shortName:"RSI",calcParams:[6,12,24],shouldCheckParamCount:!1,plots:[{key:"rsi1",title:"RSI1: ",type:"line"},{key:"rsi2",title:"RSI2: ",type:"line"},{key:"rsi3",title:"RSI3: ",type:"line"}],regeneratePlots:function(t){return t.map(function(t,e){var i=e+1;return{key:"rsi".concat(i),title:"RSI".concat(i,": "),type:"line"}})},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots,r=[],a=[];return t.map(function(e,o){var s={},c=e.close-(t[o-1]||e).close;return i.forEach(function(e,i){if(c>0?r[i]=(r[i]||0)+c:a[i]=(a[i]||0)+Math.abs(c),o>=e-1){s[n[i].key]=0!==a[i]?100-100/(1+r[i]/a[i]):0;var h=t[o-(e-1)],l=h.close-(t[o-e]||h).close;l>0?r[i]-=l:a[i]-=Math.abs(l)}}),s})}};function c(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=Number.MIN_SAFE_INTEGER,i=Number.MAX_SAFE_INTEGER;return t.forEach(function(t){e=Math.max(t.high,e),i=Math.min(t.low,i)}),{hn:e,ln:i}}var h={name:"KDJ",shortName:"KDJ",calcParams:[9,3,3],plots:[{key:"k",title:"K: ",type:"line"},{key:"d",title:"D: ",type:"line"},{key:"j",title:"J: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=[];return t.forEach(function(e,r){var a={},o=e.close;if(r>=i[0]-1){var s=c(t.slice(r-(i[0]-1),r+1)),h=s.ln,l=s.hn-h;a.k=((i[1]-1)*(n[r-1].k||50)+(o-h)/(0===l?1:l)*100)/i[1],a.d=((i[2]-1)*(n[r-1].d||50)+a.k)/i[2],a.j=3*a.k-2*a.d}n.push(a)}),n}},l={name:"WR",shortName:"WR",calcParams:[6,10,14],shouldCheckParamCount:!1,plots:[{key:"wr1",title:"WR1: ",type:"line"},{key:"wr2",title:"WR2: ",type:"line"},{key:"wr3",title:"WR3: ",type:"line"}],regeneratePlots:function(t){return t.map(function(t,e){return{key:"wr".concat(e+1),title:"WR".concat(e+1,": "),type:"line"}})},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots;return t.map(function(e,r){var a={},o=e.close;return i.forEach(function(e,i){var s=e-1;if(r>=s){var h=c(t.slice(r-s,r+1)),l=h.hn,u=l-h.ln;a[n[i].key]=0===u?0:(o-l)/u*100}}),a})}},u={name:"BOLL",shortName:"BOLL",series:"price",calcParams:[20,{value:2,allowDecimal:!0}],precision:2,shouldOhlc:!0,plots:[{key:"up",title:"UP: ",type:"line"},{key:"mid",title:"MID: ",type:"line"},{key:"dn",title:"DN: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=i[0]-1,r=0;return t.map(function(e,a){var o={};if(r+=e.close,a>=n){o.mid=r/i[0];var s=function(t,e){var i=t.length,n=0;t.forEach(function(t){var i=t.close-e;n+=i*i});var r=n>0,a=Math.sqrt((n=Math.abs(n))/i);return r?a:-1*a}(t.slice(a-n,a+1),o.mid);o.up=o.mid+i[1]*s,o.dn=o.mid-i[1]*s,r-=t[a-n].close}return o})}},f={name:"SAR",shortName:"SAR",series:"price",calcParams:[2,2,20],precision:2,shouldOhlc:!0,plots:[{key:"sar",title:"SAR: ",type:"circle",color:function(t,e){var i=t.current,n=i.kLineData||{};return(n.high+n.low)/2>(i.technicalIndicatorData||{}).sar?e.circle.upColor:e.circle.downColor}}],calcTechnicalIndicator:function(t,e){var i=e.params,n=i[0]/100,r=i[1]/100,a=i[2]/100,o=n,s=-100,c=!1,h=0;return t.map(function(e,i){var l=h,u=e.high,f=e.low;if(c){(-100===s||u>s)&&(s=u,o=Math.min(o+r,a)),h=l+o*(s-l);var d=Math.min(t[Math.max(1,i)-1].low,f);h>e.low?(h=s,o=n,s=-100,c=!c):h>d&&(h=d)}else{(-100===s||s>f)&&(s=f,o=Math.min(o+r,a)),h=l+o*(s-l);var v=Math.max(t[Math.max(1,i)-1].high,u);e.high>h?(h=s,o=0,s=-100,c=!c):v>h&&(h=v)}return{sar:h}})}},d={technicalIndicatorExtensions:{},shapeExtensions:{},addTechnicalIndicatorTemplate:function(t){var e=this;t&&[].concat(t).forEach(function(t){t.name&&(e.technicalIndicatorExtensions[t.name]=t)})},addShapeTemplate:function(t){var e=this;t&&[].concat(t).forEach(function(t){t.name&&(e.shapeExtensions[t.name]=t)})}};function v(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function p(t,e){for(var i=0;e.length>i;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function _(t,e,i){return e&&p(t.prototype,e),i&&p(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function y(t,e){if(k(t)&&k(e))for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&i in t){var n=t[i],r=e[i];k(r)&&k(n)&&!g(r)&&!g(n)?y(n,r):b(e[i])&&(t[i]=e[i])}}function x(t){if(!k(t))return t;var e;for(var i in e=g(t)?[]:{},t)if(Object.prototype.hasOwnProperty.call(t,i)){var n=t[i];e[i]=k(n)?x(n):n}return e}function g(t){return"[object Array]"===Object.prototype.toString.call(t)}function S(t){return t&&"function"==typeof t}function k(t){return!!t&&"object"===m(t)}function w(t){return"number"==typeof t&&!isNaN(t)}function b(t){return null!=t}function E(t){return"boolean"==typeof t}function P(t){return"string"==typeof t}var C="stroke",A="fill",I="dash",T="solid",M="left",D="right",O="normal",L="percentage",R="log",B="candle_solid",F="area",z="always",V="rect",H="standard",j="circle",N="rect",Y="triangle",W="diamond",X="custom",$="point",G="top",U="bottom",Z={grid:{show:!0,horizontal:{show:!0,size:1,color:"#EDEDED",style:I,dashValue:[2,2]},vertical:{show:!0,size:1,color:"#EDEDED",style:I,dashValue:[2,2]}},candle:{margin:{top:.2,bottom:.1},type:B,bar:{upColor:"#26A69A",downColor:"#EF5350",noChangeColor:"#999999"},area:{lineSize:2,lineColor:"#2196F3",value:"close",backgroundColor:[{offset:0,color:"rgba(33, 150, 243, 0.01)"},{offset:1,color:"rgba(33, 150, 243, 0.2)"}]},priceMark:{show:!0,high:{show:!0,color:"#76808F",textMargin:5,textSize:10,textFamily:"Helvetica Neue",textWeight:"normal"},low:{show:!0,color:"#76808F",textMargin:5,textSize:10,textFamily:"Helvetica Neue",textWeight:"normal"},last:{show:!0,upColor:"#26A69A",downColor:"#EF5350",noChangeColor:"#888888",line:{show:!0,style:I,dashValue:[4,4],size:1},text:{show:!0,size:12,paddingLeft:2,paddingTop:2,paddingRight:2,paddingBottom:2,color:"#FFFFFF",family:"Helvetica Neue",weight:"normal",borderRadius:2}}},tooltip:{showRule:z,showType:H,labels:["时间: ","开: ","收: ","高: ","低: ","成交量: "],values:null,defaultValue:"n/a",rect:{paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:6,offsetLeft:8,offsetTop:8,offsetRight:8,borderRadius:4,borderSize:1,borderColor:"#F2F3F5",backgroundColor:"#FEFEFE"},text:{size:12,family:"Helvetica Neue",weight:"normal",color:"#76808F",marginLeft:8,marginTop:6,marginRight:8,marginBottom:0}}},technicalIndicator:{margin:{top:.2,bottom:.1},bar:{upColor:"rgba(38, 166, 154, .65)",downColor:"rgba(239, 83, 80, .65)",noChangeColor:"#888888"},line:{size:1,dashValue:[2,2],colors:["#FF9600","#9D65C9","#2196F3","#E11D74","#01C5C4"]},circle:{upColor:"rgba(38, 166, 154, .65)",downColor:"rgba(239, 83, 80, .65)",noChangeColor:"#888888"},lastValueMark:{show:!1,text:{show:!1,color:"#FFFFFF",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:3,paddingTop:2,paddingRight:3,paddingBottom:2,borderRadius:2}},tooltip:{showRule:z,showType:H,showName:!0,showParams:!0,defaultValue:"n/a",text:{size:12,family:"Helvetica Neue",weight:"normal",color:"#76808F",marginTop:6,marginRight:8,marginBottom:0,marginLeft:8}}},xAxis:{show:!0,height:null,axisLine:{show:!0,color:"#DDDDDD",size:1},tickText:{show:!0,color:"#76808F",size:12,family:"Helvetica Neue",weight:"normal",paddingTop:3,paddingBottom:6},tickLine:{show:!0,size:1,length:3,color:"#DDDDDD"}},yAxis:{show:!0,width:null,type:O,position:D,inside:!1,reverse:!1,axisLine:{show:!0,color:"#DDDDDD",size:1},tickText:{show:!0,color:"#76808F",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:3,paddingRight:6},tickLine:{show:!0,size:1,length:3,color:"#DDDDDD"}},separator:{size:1,color:"#DDDDDD",fill:!0,activeBackgroundColor:"rgba(33, 150, 243, 0.08)"},crosshair:{show:!0,horizontal:{show:!0,line:{show:!0,style:I,dashValue:[4,2],size:1,color:"#76808F"},text:{show:!0,color:"#FFFFFF",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:2,paddingRight:2,paddingTop:2,paddingBottom:2,borderSize:1,borderColor:"#686D76",borderRadius:2,backgroundColor:"#686D76"}},vertical:{show:!0,line:{show:!0,style:I,dashValue:[4,2],size:1,color:"#76808F"},text:{show:!0,color:"#FFFFFF",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:2,paddingRight:2,paddingTop:2,paddingBottom:2,borderSize:1,borderRadius:2,borderColor:"#686D76",backgroundColor:"#686D76"}}},shape:{point:{backgroundColor:"#2196F3",borderColor:"rgba(33, 150, 243, 0.35)",borderSize:1,radius:5,activeBackgroundColor:"#2196F3",activeBorderColor:"rgba(33, 150, 243, 0.35)",activeBorderSize:3,activeRadius:5},line:{style:T,color:"#2196F3",size:1,dashValue:[2,2]},polygon:{style:C,stroke:{style:T,size:1,color:"#2196F3",dashValue:[2,2]},fill:{color:"#2196F3"}},arc:{style:C,stroke:{style:T,size:1,color:"#2196F3",dashValue:[2,2]},fill:{color:"#2196F3"}},text:{style:A,color:"#2196F3",size:12,family:"Helvetica Neue",weight:"normal",offset:[0,0]}},annotation:{position:G,offset:[20,0],symbol:{type:W,size:8,color:"#2196F3",activeSize:10,activeColor:"#FF9600"}},tag:{position:$,offset:0,line:{show:!0,style:I,dashValue:[4,2],size:1,color:"#2196F3"},text:{color:"#FFFFFF",backgroundColor:"#2196F3",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:2,paddingRight:2,paddingTop:2,paddingBottom:2,borderRadius:2,borderSize:1,borderColor:"#2196F3"},mark:{offset:0,color:"#FFFFFF",backgroundColor:"#2196F3",size:12,family:"Helvetica Neue",weight:"normal",paddingLeft:2,paddingRight:2,paddingTop:2,paddingBottom:2,borderRadius:2,borderSize:1,borderColor:"#2196F3"}}};function K(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"--";if(k(t)){var n=t[e];if(b(n))return n}return i}function q(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"MM-DD hh:mm";if(w(e)){var n=t.format(new Date(e)).split(", "),r=n[0].split("/"),a={YYYY:r[2],MM:r[0],DD:r[1],"hh:mm":"24"===n[1].match(/^[\d]{2}/)[0]?n[1].replace(/^[\d]{2}/,"00"):n[1]};return i.replace(/YYYY|MM|DD|(hh:mm)/g,function(t){return a[t]})}return"--"}function J(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,i=+t;return(i||0===i)&&w(i)?i.toFixed(e):"".concat(i)}function Q(t){return w(+t)?t>1e9?"".concat(+(t/1e9).toFixed(3),"B"):t>1e6?"".concat(+(t/1e6).toFixed(3),"M"):t>1e3?"".concat(+(t/1e3).toFixed(3),"K"):t:"--"}function tt(t,e,i){var n=0,r=0;for(r=t.length-1;n!==r;){var a=Math.floor((r+n)/2),o=r-n,s=t[a][e];if(i===t[n][e])return n;if(i===t[r][e])return r;if(i===s)return a;if(i>s?n=a:r=a,2>=o)break}return n}function et(t,e){return null==e&&(e=10),+(t=(+t).toFixed(e=Math.min(Math.max(0,e),20)))}function it(t){return Math.log(t)/Math.log(10)}function nt(t){return Math.pow(10,t)}var rt={ZOOM:"zoom",SCROLL:"scroll",CROSSHAIR:"crosshair",TOOLTIP:"tooltip",PANE_DRAG:"pane_drag"};function at(t){return Object.values(rt).indexOf(t)>-1}var ot=function(){function t(e){v(this,t),this._chartStore=e,this._dateTimeFormat=new Intl.DateTimeFormat("en",{hour12:!1,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),this._zoomEnabled=!0,this._scrollEnabled=!0,this._loading=!0,this._loadMoreCallback=null,this._more=!0,this._totalDataSpace=0,this._dataSpace=6,this._barSpace=this._calcBarSpace(),this._offsetRightSpace=50,this._offsetRightBarCount=this._offsetRightSpace/this._dataSpace,this._leftMinVisibleBarCount=2,this._rightMinVisibleBarCount=2,this._from=0,this._to=0,this._preOffsetRightBarCount=0}return _(t,[{key:"_calcBarSpace",value:function(){return Math.max(1,Math.min(Math.floor(.82*this._dataSpace),Math.floor(this._dataSpace)-1))}},{key:"adjustFromTo",value:function(){var t=this._chartStore.dataList().length,e=this._totalDataSpace/this._dataSpace,i=e-Math.min(this._leftMinVisibleBarCount,t);this._offsetRightBarCount>i&&(this._offsetRightBarCount=i);var n=-t+Math.min(this._rightMinVisibleBarCount,t);n>this._offsetRightBarCount&&(this._offsetRightBarCount=n),this._to=Math.round(this._offsetRightBarCount+t+.5),this._from=Math.round(this._to-e)-1,this._to>t&&(this._to=t),0>this._from&&(this._from=0),this._chartStore.adjustVisibleDataList(),0===this._from&&this._more&&!this._loading&&S(this._loadMoreCallback)&&(this._loading=!0,this._loadMoreCallback(K(this._chartStore.dataList()[0],"timestamp")))}},{key:"setMore",value:function(t){this._more=t}},{key:"setLoading",value:function(t){this._loading=t}},{key:"dateTimeFormat",value:function(){return this._dateTimeFormat}},{key:"setTimezone",value:function(t){var e;try{e=new Intl.DateTimeFormat("en",{hour12:!1,timeZone:t,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(t){}e&&(this._dateTimeFormat=e)}},{key:"timezone",value:function(){return this._dateTimeFormat.resolvedOptions().timeZone}},{key:"dataSpace",value:function(){return this._dataSpace}},{key:"barSpace",value:function(){return this._barSpace}},{key:"halfBarSpace",value:function(){return this._barSpace/2}},{key:"setDataSpace",value:function(t,e){1>t||t>50||this._dataSpace===t||(this._dataSpace=t,this._barSpace=this._calcBarSpace(),e&&e(),this.adjustFromTo(),this._chartStore.crosshairStore().recalculate(!0),this._chartStore.invalidate())}},{key:"setTotalDataSpace",value:function(t){this._totalDataSpace!==t&&(this._totalDataSpace=t,this.adjustFromTo(),this._chartStore.crosshairStore().recalculate(!0))}},{key:"setOffsetRightSpace",value:function(t,e){this._offsetRightSpace=t,this._offsetRightBarCount=t/this._dataSpace,e&&(this.adjustFromTo(),this._chartStore.crosshairStore().recalculate(!0),this._chartStore.invalidate())}},{key:"resetOffsetRightSpace",value:function(){this.setOffsetRightSpace(this._offsetRightSpace)}},{key:"offsetRightSpace",value:function(){return this._offsetRightSpace}},{key:"offsetRightBarCount",value:function(){return this._offsetRightBarCount}},{key:"setOffsetRightBarCount",value:function(t){this._offsetRightBarCount=t}},{key:"setLeftMinVisibleBarCount",value:function(t){this._leftMinVisibleBarCount=t}},{key:"setRightMinVisibleBarCount",value:function(t){this._rightMinVisibleBarCount=t}},{key:"from",value:function(){return this._from}},{key:"to",value:function(){return this._to}},{key:"startScroll",value:function(){this._preOffsetRightBarCount=this._offsetRightBarCount}},{key:"scroll",value:function(t,e){if(this._scrollEnabled){var i=t/this._dataSpace;this._chartStore.actionStore().execute(rt.SCROLL,{barCount:i,distance:t}),this._offsetRightBarCount=this._preOffsetRightBarCount-i,this.adjustFromTo();var n=e||this._chartStore.crosshairStore().get();this._chartStore.crosshairStore().set(n,!0),this._chartStore.invalidate()}}},{key:"getDataByDataIndex",value:function(t){return this._chartStore.dataList()[t]}},{key:"coordinateToFloatIndex",value:function(t){var e=this._chartStore.dataList().length;return Math.round(1e6*(e+this._offsetRightBarCount-(this._totalDataSpace-t)/this._dataSpace))/1e6}},{key:"dataIndexToTimestamp",value:function(t){var e=this.getDataByDataIndex(t);if(e)return e.timestamp}},{key:"timestampToDataIndex",value:function(t){return 0===this._chartStore.dataList().length?0:tt(this._chartStore.dataList(),"timestamp",t)}},{key:"dataIndexToCoordinate",value:function(t){var e=this._chartStore.dataList().length;return this._totalDataSpace-(e+this._offsetRightBarCount-t-.5)*this._dataSpace}},{key:"coordinateToDataIndex",value:function(t){return Math.ceil(this.coordinateToFloatIndex(t))-1}},{key:"zoom",value:function(t,e){var i=this;if(this._zoomEnabled){if(!e||!b(e.x)){var n=this._chartStore.crosshairStore().get();e={x:b(n.x)?n.x:this._totalDataSpace/2}}this._chartStore.actionStore().execute(rt.ZOOM,{coordinate:e,scale:t});var r=this.coordinateToFloatIndex(e.x);this.setDataSpace(this._dataSpace+t*(this._dataSpace/10),function(){i._offsetRightBarCount+=r-i.coordinateToFloatIndex(e.x)})}}},{key:"setZoomEnabled",value:function(t){this._zoomEnabled=t}},{key:"zoomEnabled",value:function(){return this._zoomEnabled}},{key:"setScrollEnabled",value:function(t){this._scrollEnabled=t}},{key:"scrollEnabled",value:function(){return this._scrollEnabled}},{key:"setLoadMoreCallback",value:function(t){this._loadMoreCallback=t}},{key:"clear",value:function(){this._more=!0,this._loading=!0,this._from=0,this._to=0}}]),t}();function st(t,e){return(st=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ct(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&st(t,e)}function ht(t,e){if(e&&("object"===m(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function lt(t){return(lt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ut(t,e,i,n,r,a,o){try{var s=t[a](o),c=s.value}catch(t){return void i(t)}s.done?e(c):Promise.resolve(c).then(n,r)}var ft={exports:{}},dt={exports:{}};!function(t){function e(i){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(i)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports}(dt),function(t){var e=dt.exports.default;function i(){t.exports=i=function(){return n},t.exports.__esModule=!0,t.exports.default=t.exports;var n={},r=Object.prototype,a=r.hasOwnProperty,o=Object.defineProperty||function(t,e,i){t[e]=i.value},s="function"==typeof Symbol?Symbol:{},c=s.iterator||"@@iterator",h=s.asyncIterator||"@@asyncIterator",l=s.toStringTag||"@@toStringTag";function u(t,e,i){return Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,i){return t[e]=i}}function f(t,e,i,n){var r=Object.create((e&&e.prototype instanceof p?e:p).prototype),a=new C(n||[]);return o(r,"_invoke",{value:function(t,e,i){var n="suspendedStart";return function(r,a){if("executing"===n)throw Error("Generator is already running");if("completed"===n){if("throw"===r)throw a;return{value:void 0,done:!0}}for(i.method=r,i.arg=a;;){var o=i.delegate;if(o){var s=b(o,i);if(s){if(s===v)continue;return s}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===n)throw n="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n="executing";var c=d(t,e,i);if("normal"===c.type){if(n=i.done?"completed":"suspendedYield",c.arg===v)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(n="completed",i.method="throw",i.arg=c.arg)}}}(t,i,a)}),r}function d(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}n.wrap=f;var v={};function p(){}function _(){}function m(){}var y={};u(y,c,function(){return this});var x=Object.getPrototypeOf,g=x&&x(x(A([])));g&&g!==r&&a.call(g,c)&&(y=g);var S=m.prototype=p.prototype=Object.create(y);function k(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function w(t,i){function n(r,o,s,c){var h=d(t[r],t,o);if("throw"!==h.type){var l=h.arg,u=l.value;return u&&"object"==e(u)&&a.call(u,"__await")?i.resolve(u.__await).then(function(t){n("next",t,s,c)},function(t){n("throw",t,s,c)}):i.resolve(u).then(function(t){l.value=t,s(l)},function(t){return n("throw",t,s,c)})}c(h.arg)}var r;o(this,"_invoke",{value:function(t,e){function a(){return new i(function(i,r){n(t,e,i,r)})}return r=r?r.then(a,a):a()}})}function b(t,e){var i=t.iterator[e.method];if(void 0===i){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,b(t,e),"throw"===e.method))return v;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return v}var n=d(i,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,v;var r=n.arg;return r?r.done?(e[t.resultName]=r.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,v):r:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function A(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,n=function e(){for(;++i<t.length;)if(a.call(t,i))return e.value=t[i],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:I}}function I(){return{value:void 0,done:!0}}return _.prototype=m,o(S,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:_,configurable:!0}),_.displayName=u(m,l,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},n.awrap=function(t){return{__await:t}},k(w.prototype),u(w.prototype,h,function(){return this}),n.AsyncIterator=w,n.async=function(t,e,i,r,a){void 0===a&&(a=Promise);var o=new w(f(t,e,i,r),a);return n.isGeneratorFunction(e)?o:o.next().then(function(t){return t.done?t.value:o.next()})},k(S),u(S,l,"Generator"),u(S,c,function(){return this}),u(S,"toString",function(){return"[object Generator]"}),n.keys=function(t){var e=Object(t),i=[];for(var n in e)i.push(n);return i.reverse(),function t(){for(;i.length;){var n=i.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},n.values=A,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function i(i,n){return o.type="throw",o.arg=t,e.next=i,n&&(e.method="next",e.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n],o=r.completion;if("root"===r.tryLoc)return i("end");if(this.prev>=r.tryLoc){var s=a.call(r,"catchLoc"),c=a.call(r,"finallyLoc");if(s&&c){if(r.catchLoc>this.prev)return i(r.catchLoc,!0);if(r.finallyLoc>this.prev)return i(r.finallyLoc)}else if(s){if(r.catchLoc>this.prev)return i(r.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(r.finallyLoc>this.prev)return i(r.finallyLoc)}}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i];if(this.prev>=n.tryLoc&&a.call(n,"finallyLoc")&&n.finallyLoc>this.prev){var r=n;break}}r&&("break"===t||"continue"===t)&&e>=r.tryLoc&&r.finallyLoc>=e&&(r=null);var o=r?r.completion:{};return o.type=t,o.arg=e,r?(this.method="next",this.next=r.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.finallyLoc===t)return this.complete(i.completion,i.afterLoc),P(i),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc===t){var n=i.completion;if("throw"===n.type){var r=n.arg;P(i)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,e,i){return this.delegate={iterator:A(t),resultName:e,nextLoc:i},"next"===this.method&&(this.arg=void 0),v}},n}t.exports=i,t.exports.__esModule=!0,t.exports.default=t.exports}(ft);var vt=ft.exports(),pt=vt;try{regeneratorRuntime=vt}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=vt:Function("r","regeneratorRuntime = r")(vt)}var _t="line",mt="circle",yt={PRICE:"price",VOLUME:"volume",NORMAL:"normal"};function xt(t,e,i,n,r,a){var o=a.color,s=a.isStroke,c=a.isDashed,h={prev:{kLineData:t[i-1],technicalIndicatorData:e[i-1]},current:{kLineData:t[i],technicalIndicatorData:e[i]},next:{kLineData:t[i+1],technicalIndicatorData:e[i+1]}};return b(n.color)&&(o=S(n.color)?n.color(h,r)||a.color:n.color||a.color),b(n.isStroke)&&(s=S(n.isStroke)?n.isStroke(h):n.isStroke),b(n.isDashed)&&(c=S(n.isDashed)?n.isDashed(h):n.isDashed),{color:o,isStroke:s,isDashed:c}}var gt=function(){function t(e){var i=e.name,n=e.shortName,r=e.series,a=e.calcParams,o=e.plots,s=e.precision,c=e.shouldCheckParamCount,h=e.shouldOhlc,l=e.shouldFormatBigNumber,u=e.minValue,f=e.maxValue,d=e.styles,p=e.extendData;v(this,t),this.name=i||"",this.shortName=b(n)?n:i,this.series=-1!==Object.values(yt).indexOf(r)?r:yt.NORMAL,this.precision=w(s)&&s>=0?s:4,this._precisionFlag=!1,this.calcParams=g(a)?a:[],this.plots=g(o)?o:[],this.shouldCheckParamCount=!E(c)||c,this.shouldOhlc=!!E(h)&&h,this.shouldFormatBigNumber=!!E(l)&&l,this.minValue=u,this.maxValue=f,this.styles=d,this.extendData=p,this.result=[]}var e,i;return _(t,[{key:"_createParams",value:function(t){return t.map(function(t){return k(t)?t.value:t})}},{key:"setShortName",value:function(t){return!(!b(t)||this.shortName===t||(this.shortName=t,0))}},{key:"setPrecision",value:function(t,e){return!(!w(t)||0>t||e&&(!e||this._precisionFlag)||(this.precision=parseInt(t,10),e||(this._precisionFlag=!0),0))}},{key:"setCalcParams",value:function(t){if(!g(t))return!1;if(this.shouldCheckParamCount&&t.length!==this.calcParams.length)return!1;for(var e=[],i=0;t.length>i;i++){var n=t[i],r=void 0,a=void 0;k(n)?(r=n.value,a=n.allowDecimal):(r=n,a=!1);var o=this.calcParams[i];if(k(o)&&E(o.allowDecimal)&&(a=o.allowDecimal),!w(r)||!a&&parseInt(r,10)!==r)return!1;e.push({allowDecimal:a,value:r})}this.calcParams=e;var s=this.regeneratePlots(this._createParams(e));return s&&g(s)&&(this.plots=s),!0}},{key:"setShouldOhlc",value:function(t){return!(!E(t)||this.shouldOhlc===t||(this.shouldOhlc=t,0))}},{key:"setShouldFormatBigNumber",value:function(t){return!(!E(t)||this.shouldFormatBigNumber===t||(this.shouldFormatBigNumber=t,0))}},{key:"setStyles",value:function(t,e){return!!k(t)&&(this.styles||(this.styles={margin:x(e.margin),bar:x(e.bar),line:x(e.line),circle:x(e.circle)}),y(this.styles,t),!0)}},{key:"setExtendData",value:function(t){return void 0!==t&&this.extendData!==t&&(this.extendData=t,!0)}},{key:"calc",value:(e=pt.mark(function t(e){return pt.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.calcTechnicalIndicator(e,{params:this._createParams(this.calcParams),plots:this.plots,extendData:this.extendData});case 2:if(t.t0=t.sent,t.t0){t.next=5;break}t.t0=[];case 5:this.result=t.t0;case 6:case"end":return t.stop()}},t,this)}),i=function(){var t=this,i=arguments;return new Promise(function(n,r){var a=e.apply(t,i);function o(t){ut(a,n,r,o,s,"next",t)}function s(t){ut(a,n,r,o,s,"throw",t)}o(void 0)})},function(t){return i.apply(this,arguments)})},{key:"calcTechnicalIndicator",value:function(t,e){}},{key:"regeneratePlots",value:function(t){}}]),t}();function St(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}var kt=function(){function t(e){v(this,t),this._chartStore=e,this._templates=this._createTemplates(),this._instances=new Map}return _(t,[{key:"_createTechInfo",value:function(t){return{name:t.name,shortName:t.shortName,series:t.series,calcParams:t.calcParams,shouldCheckParamCount:t.shouldCheckParamCount,shouldOhlc:t.shouldOhlc,shouldFormatBigNumber:t.shouldFormatBigNumber,precision:t.precision,styles:t.styles,plots:t.plots,result:t.result||[]}}},{key:"_createTemplates",value:function(){var t={},e=d.technicalIndicatorExtensions;for(var i in e){var n=this._createTemplateInstance(e[i]);n&&(t[i]=n)}return t}},{key:"_createTemplateInstance",value:function(t){var e,i=t.name,n=t.shortName,r=t.series,a=t.calcParams,o=t.plots,s=t.precision,c=t.shouldCheckParamCount,h=t.shouldOhlc,l=t.shouldFormatBigNumber,u=t.minValue,f=t.maxValue,d=t.styles,p=t.extendData,m=t.calcTechnicalIndicator,g=t.regeneratePlots,w=t.createToolTipDataSource,b=t.render;if(!i||!S(m))return null;if(k(d)){var E=this._chartStore.styleOptions().technicalIndicator;y(e={margin:x(E.margin),bar:x(E.bar),line:x(E.line),circle:x(E.circle)},d)}var P=function(t){ct(m,gt);var d=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(m);function m(){return v(this,m),d.call(this,{name:i,shortName:n,series:r,calcParams:a,plots:o,precision:s,shouldCheckParamCount:c,shouldOhlc:h,shouldFormatBigNumber:l,minValue:u,maxValue:f,defaultStyles:e,extendData:p})}return _(m)}();return P.prototype.calcTechnicalIndicator=m,S(g)&&(P.prototype.regeneratePlots=g),S(w)&&(P.prototype.createToolTipDataSource=w),S(b)&&(P.prototype.render=b),new P}},{key:"addTemplate",value:function(t){var e=this;t.forEach(function(t){var i=e._createTemplateInstance(t||{});i&&(e._templates[i.name]=i)})}},{key:"hasTemplate",value:function(t){return!!this._templates[t]}},{key:"getTemplateInfo",value:function(t){if(!b(t)){var e={};for(var i in this._templates)e[i]=this._createTechInfo(this._templates[i]);return e}var n=this._templates[t];return n?this._createTechInfo(n):{}}},{key:"addInstance",value:function(t,e,i){var n=e.name,r=e.calcParams,a=e.precision,o=e.shouldOhlc,s=e.shouldFormatBigNumber,c=e.styles,h=e.extendData,l=this._instances.get(t);if(!l||!l.has(n)){l||(l=new Map,this._instances.set(t,l));var u=this._templates[n],f=Object.create(Object.getPrototypeOf(u));for(var d in u)Object.prototype.hasOwnProperty.call(u,d)&&(f[d]=u[d]);return f.setCalcParams(r),f.setPrecision(a),f.setShouldOhlc(o),f.setShouldFormatBigNumber(s),f.setStyles(c,this._chartStore.styleOptions().technicalIndicator),f.setExtendData(h),i||l.clear(),l.set(n,f),f.calc(this._chartStore.dataList())}}},{key:"instances",value:function(t){return this._instances.get(t)||new Map}},{key:"removeInstance",value:function(t,e){var i=!1;if(this._instances.has(t)){var n=this._instances.get(t);b(e)?n.has(e)&&(n.delete(e),i=!0):(n.clear(),i=!0),0===n.size&&this._instances.delete(t)}return i}},{key:"hasInstance",value:function(t){return this._instances.has(t)}},{key:"calcInstance",value:function(t,e){var i=this,n=[];if(b(t))if(b(e)){var r=this._instances.get(e);r&&r.has(t)&&n.push(r.get(t).calc(this._chartStore.dataList()))}else this._instances.forEach(function(e){e.has(t)&&n.push(e.get(t).calc(i._chartStore.dataList()))});else this._instances.forEach(function(t){t.forEach(function(t){n.push(t.calc(i._chartStore.dataList()))})});return Promise.all(n)}},{key:"getInstanceInfo",value:function(t,e){var i=this,n=function(t){var n,r=[],a=function(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return St(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?St(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}(t);try{for(a.s();!(n=a.n()).done;){var o=n.value[1];if(o){var s=i._createTechInfo(o);if(o.name===e)return s;r.push(s)}}}catch(t){a.e(t)}finally{a.f()}return r};if(!b(t)){var r={};return this._instances.forEach(function(t,e){r[e]=n(t)}),r}return this._instances.has(t)?n(this._instances.get(t)):{}}},{key:"setSeriesPrecision",value:function(t,e){var i=function(i){i.series===yt.PRICE&&i.setPrecision(t,!0),i.series===yt.VOLUME&&i.setPrecision(e,!0)};for(var n in this._templates)i(this._templates[n]);this._instances.forEach(function(t){t.forEach(function(t){i(t)})})}},{key:"override",value:function(t,e){var i=this,n=t.name,r=t.shortName,a=t.calcParams,o=t.precision,s=t.shouldOhlc,c=t.shouldFormatBigNumber,h=t.styles,l=t.extendData,u=this._chartStore.styleOptions().technicalIndicator,f=new Map;if(b(e))this._instances.has(e)&&f.set(e,this._instances.get(e));else{f=this._instances;var d=this._templates[n];d&&(d.setCalcParams(a),d.setShortName(r),d.setPrecision(o),d.setShouldOhlc(s),d.setShouldFormatBigNumber(c),d.setStyles(h,u),d.setExtendData(l))}var v=!1,p=[];if(f.forEach(function(t){if(t.has(n)){var e=t.get(n),f=e.setShortName(r),d=e.setCalcParams(a),_=e.setPrecision(o),m=e.setShouldOhlc(s),y=e.setShouldFormatBigNumber(c),x=e.setStyles(h,u),g=e.setExtendData(l);(f||d||_||m||y||x||g)&&(v=!0),d&&p.push(e.calc(i._chartStore.dataList()))}}),v)return Promise.all(p)}}]),t}();function wt(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function bt(t,e){e.forEach(function(e){var i=e.key,n=e.fn;S(n)&&(t[i]=n)})}var Et=function(){function t(e){var i=e.id,n=e.chartStore,r=e.points,a=e.xAxis,o=e.yAxis;v(this,t),this._id=i,this._chartStore=n,this._points=r,this._xAxis=a,this._yAxis=o,this._styles=null}return _(t,[{key:"draw",value:function(t){}},{key:"setStyles",value:function(t,e){return!!k(t)&&(this._styles||(this._styles=x(e)),y(this._styles,t),!0)}},{key:"id",value:function(){return this._id}},{key:"styles",value:function(){return this._styles}},{key:"points",value:function(){return this._points}},{key:"setYAxis",value:function(t){t&&(this._yAxis=t)}},{key:"checkEventCoordinateOn",value:function(t){}},{key:"onClick",value:function(t){}},{key:"onRightClick",value:function(t){}},{key:"onMouseEnter",value:function(t){}},{key:"onMouseLeave",value:function(t){}}]),t}();function Pt(t,e,i,n){t.fillStyle=e,t.beginPath(),t.arc(i.x,i.y,n,0,2*Math.PI),t.closePath(),t.fill()}function Ct(t,e,i){var n=Math.abs(e.x-t.x),r=Math.abs(e.y-t.y),a=Math.abs(i.x-t.x);return Math.abs(n*Math.abs(i.y-t.y)-a*r)/2}function At(t,e){var i=t.x-e.x;if(0!==i){var n=(t.y-e.y)/i;return{k:n,b:t.y-n*t.x}}}function It(t,e,i){return Tt(At(t,e),i)}function Tt(t,e){return t?e.x*t.k+t.b:e.y}function Mt(t,e,i){if(!i||!t||!e)return!1;if(t.x===e.x)return 2>Math.abs(i.x-t.x);var n=At(t,e),r=Tt(n,i),a=Math.abs(r-i.y);return 4>a*a/(n.k*n.k+1)}function Dt(t,e,i){return!!Mt(t,e,i)&&(t.x===e.x?e.y>t.y?2>t.y-i.y:2>i.y-t.y:e.x>t.x?2>t.x-i.x:2>i.x-t.x)}function Ot(t,e,i){return!!Mt(t,e,i)&&(t.x===e.x?4>Math.abs(t.y-i.y)+Math.abs(e.y-i.y)-Math.abs(t.y-e.y):4>Math.abs(t.x-i.x)+Math.abs(e.x-i.x)-Math.abs(t.x-e.x))}function Lt(t,e,i){if(!i)return!1;var n=i.x-t.x,r=i.y-t.y;return!(n*n+r*r>e*e)}function Rt(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=[];if(t.length>1)if(t[0].x===t[1].x){var r=e.y;if(n.push([{x:t[0].x,y:0},{x:t[0].x,y:r}]),t.length>2){n.push([{x:t[2].x,y:0},{x:t[2].x,y:r}]);for(var a=t[0].x-t[2].x,o=0;i>o;o++){var s=a*(o+1);n.push([{x:t[0].x+s,y:0},{x:t[0].x+s,y:r}])}}}else{var c=e.x,h=At(t[0],t[1]),l=h.k,u=h.b;if(n.push([{x:0,y:0*l+u},{x:c,y:c*l+u}]),t.length>2){var f=t[2].y-l*t[2].x;n.push([{x:0,y:0*l+f},{x:c,y:c*l+f}]);for(var d=u-f,v=0;i>v;v++){var p=u+d*(v+1);n.push([{x:0,y:0*l+p},{x:c,y:c*l+p}])}}}return n}function Bt(t,e,i){t.save(),t.lineWidth%2&&t.translate(.5,.5),t.beginPath();var n=!0;e.forEach(function(e){e&&(n?(t.moveTo(e.x,e.y),n=!1):t.lineTo(e.x,e.y))}),i(),t.restore()}function Ft(t,e){Bt(t,e,function(){t.closePath(),t.stroke()})}function zt(t,e){Bt(t,e,function(){t.closePath(),t.fill()})}function Vt(t,e,i,n){t.beginPath();var r=t.lineWidth%2?.5:0;t.moveTo(i,e+r),t.lineTo(n,e+r),t.stroke(),t.closePath()}function Ht(t,e,i,n){t.beginPath();var r=t.lineWidth%2?.5:0;t.moveTo(e+r,i),t.lineTo(e+r,n),t.stroke(),t.closePath()}function jt(t,e){Bt(t,e,function(){t.stroke(),t.closePath()})}function Nt(t){return t.ownerDocument&&t.ownerDocument.defaultView&&t.ownerDocument.defaultView.devicePixelRatio||2}function Yt(t,e){return Math.round(t.measureText(e).width)}function Wt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:12,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Helvetica Neue";return"".concat(e," ").concat(t,"px ").concat(i)}function Xt(t,e,i){t.font=Wt(i.size,i.weight,i.family);var n=Yt(t,e);return i.paddingLeft+i.paddingRight+n+2*(i.borderSize||0)}function $t(t){return t.paddingTop+t.paddingBottom+t.size+2*(t.borderSize||0)}function Gt(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}var Ut="point",Zt="none",Kt={NORMAL:"normal",WEAK_MAGNET:"weak_magnet",STRONG_MAGNET:"strong_magnet"},qt=function(t){ct(i,Et);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t){var n,r=t.id,a=t.name,o=t.totalStep,s=t.chartStore,c=t.xAxis,h=t.yAxis,l=t.points,u=void 0===l?[]:l,f=t.styles,d=t.lock,p=t.mode,_=t.data;return v(this,i),(n=e.call(this,{id:r,chartStore:s,points:u,xAxis:c,yAxis:h}))._name=a,n._totalStep=o,n._lock=d,n._mode=Kt.NORMAL,n.setMode(p),n._data=_,n._drawStep=1,n.setPoints(u),n.setStyles(f,s.styleOptions().shape),n._prevPressPoint=null,n._prevPoints=null,n._coordinates=[],n}return _(i,[{key:"setPoints",value:function(t){if(g(t)&&t.length>0){var e;this._totalStep-1>t.length?(this._drawStep=t.length+1,this._points=x(t),e=t.length):(this._drawStep=-1,this._points=t.slice(0,this._totalStep-1),e=this._totalStep-1);for(var i=0;e>i;i++)this.performEventMoveForDrawing({step:i+2,mode:this._mode,points:this._points,movePoint:this._points[i],xAxis:this._xAxis,yAxis:this._yAxis});return-1===this._drawStep&&this.performEventPressedMove({mode:this._mode,points:this._points,pressPointIndex:this._points.length-1,pressPoint:this._points[this._points.length-1],xAxis:this._xAxis,yAxis:this._yAxis}),!0}}},{key:"_timestampOrDataIndexToCoordinateX",value:function(t){var e=t.timestamp,i=t.dataIndex;return e&&(i=this._chartStore.timeScaleStore().timestampToDataIndex(e)),this._xAxis.convertToPixel(i)}},{key:"_drawLines",value:function(t,e,i,n){t.save(),t.strokeStyle=i.color||n.color,t.lineWidth=i.size||n.size,i.style===I&&t.setLineDash(i.dashValue||n.dashValue),e.forEach(function(e){var i,n;if(e.length>1)switch((i=e[0]).x===(n=e[1]).x?2:i.y===n.y?1:0){case 0:jt(t,e);break;case 1:Vt(t,e[0].y,e[0].x,e[1].x);break;case 2:Ht(t,e[0].x,e[0].y,e[1].y)}}),t.restore()}},{key:"_drawContinuousLines",value:function(t,e,i,n){t.save(),t.strokeStyle=i.color||n.color,t.lineWidth=i.size||n.size,i.style===I&&t.setLineDash(i.dashValue||n.dashValue),e.forEach(function(e){e.length>0&&jt(t,e)}),t.restore()}},{key:"_drawPolygons",value:function(t,e,i,n){var r;if(t.save(),i.style===A)t.fillStyle=(i.fill||n.fill).color,r=zt;else{var a=i.stroke||n.stroke;a.style===I&&t.setLineDash(a.dashValue),t.lineWidth=a.size,t.strokeStyle=a.color,r=Ft}e.forEach(function(e){e.length>0&&r(t,e)}),t.restore()}},{key:"_drawArcs",value:function(t,e,i,n){if(t.save(),i.style===A)t.fillStyle=(i.fill||n.fill).color;else{var r=i.stroke||n.stroke;r.style===I&&t.setLineDash(r.dashValue),t.lineWidth=r.size,t.strokeStyle=r.color}e.forEach(function(e){var n=e.x,r=e.y,a=e.radius,o=e.startAngle,s=e.endAngle;t.beginPath(),t.arc(n,r,a,o,s),i.style===A?(t.closePath(),t.fill()):(t.stroke(),t.closePath())}),t.restore()}},{key:"_drawText",value:function(t,e,i,n){var r;t.save(),i.style===C?(t.strokeStyle=i.color||n.color,r=t.strokeText):(t.fillStyle=i.color||n.color,r=t.fillText),t.font=Wt(i.size||n.size,i.weight||n.weight,i.family||n.family);var a=i.offset||n.offset||[0,0];e.forEach(function(e){r.call(t,e.text,e.x+a[1],e.y+a[0])}),t.restore()}},{key:"draw",value:function(t){var e=this;this._coordinates=this._points.map(function(t){var i=t.value;return{x:e._timestampOrDataIndexToCoordinateX({timestamp:t.timestamp,dataIndex:t.dataIndex}),y:e._yAxis.convertToPixel(i)}});var i=this._styles||this._chartStore.styleOptions().shape;if(1!==this._drawStep&&this._coordinates.length>0){var n={width:this._xAxis.width(),height:this._yAxis.height()},r={price:this._chartStore.pricePrecision(),volume:this._chartStore.volumePrecision()};this._shapeDataSources=this.createShapeDataSource({step:this._drawStep,mode:this._mode,points:this._points,coordinates:this._coordinates,viewport:{width:this._xAxis.width(),height:this._yAxis.height()},precision:{price:this._chartStore.pricePrecision(),volume:this._chartStore.volumePrecision()},styles:i,xAxis:this._xAxis,yAxis:this._yAxis,data:this._data})||[],this._shapeDataSources.forEach(function(n){var r=n.styles,a=n.dataSource,o=void 0===a?[]:a;if(n.isDraw)switch(n.type){case"line":e._drawLines(t,o,r||i.line,i.line);break;case"continuous_line":e._drawContinuousLines(t,o,r||i.line,i.line);break;case"polygon":e._drawPolygons(t,o,r||i.polygon,i.polygon);break;case"arc":e._drawArcs(t,o,r||i.arc,i.arc);break;case"text":e._drawText(t,o,r||i.text,i.text)}}),this.drawExtend&&(t.save(),this.drawExtend({ctx:t,dataSource:this._shapeDataSources,styles:i,viewport:n,precision:r,mode:this._mode,xAxis:this._xAxis,yAxis:this._yAxis,data:this._data}),t.restore())}var a=this._chartStore.shapeStore().eventOperate();(a.hover.id===this._id&&a.hover.element!==Zt||a.click.id===this._id&&a.click.element!==Zt||this.isDrawing())&&this._coordinates.forEach(function(n,r){var o=n.x,s=n.y,c=i.point.radius,h=i.point.backgroundColor,l=i.point.borderColor,u=i.point.borderSize;a.hover.id===e._id&&a.hover.element===Ut&&r===a.hover.elementIndex&&(c=i.point.activeRadius,h=i.point.activeBackgroundColor,l=i.point.activeBorderColor,u=i.point.activeBorderSize),Pt(t,l,{x:o,y:s},c+u),Pt(t,h,{x:o,y:s},c)})}},{key:"setLock",value:function(t){this._lock=t}},{key:"name",value:function(){return this._name}},{key:"lock",value:function(){return this._lock}},{key:"totalStep",value:function(){return this._totalStep}},{key:"mode",value:function(){return this._mode}},{key:"setMode",value:function(t){Object.values(Kt).indexOf(t)>-1&&(this._mode=t)}},{key:"setData",value:function(t){return void 0!==t&&t!==this._data&&(this._data=t,!0)}},{key:"data",value:function(){return this._data}},{key:"isDrawing",value:function(){return-1!==this._drawStep}},{key:"isStart",value:function(){return 1===this._drawStep}},{key:"checkEventCoordinateOn",value:function(t){for(var e=this._styles||this._chartStore.styleOptions().shape,i=this._coordinates.length-1;i>-1;i--)if(Lt(this._coordinates[i],e.point.radius,t))return{id:this._id,element:Ut,elementIndex:i,instance:this};if(this._shapeDataSources){var n,r=function(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return Gt(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?Gt(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}(this._shapeDataSources);try{for(r.s();!(n=r.n()).done;){var a=n.value,o=a.key,s=a.type,c=a.dataSource,h=void 0===c?[]:c;if(a.isCheck)for(var l=0;h.length>l;l++)if(this.checkEventCoordinateOnShape({key:o,type:s,dataSource:h[l],eventCoordinate:t}))return{id:this._id,element:"other",elementIndex:l,instance:this}}}catch(t){r.e(t)}finally{r.f()}}}},{key:"_performValue",value:function(t,e,i){var n=this._yAxis.convertFromPixel(t);if(this._mode===Kt.NORMAL||"candle_pane"!==i)return n;var r=this._chartStore.timeScaleStore().getDataByDataIndex(e);if(!r)return n;if(n>r.high){if(this._mode===Kt.WEAK_MAGNET){var a=this._yAxis.convertToPixel(r.high);return this._yAxis.convertFromPixel(a-8)>n?r.high:n}return r.high}if(r.low>n){if(this._mode===Kt.WEAK_MAGNET){var o=this._yAxis.convertToPixel(r.low);return n>this._yAxis.convertFromPixel(o-8)?r.low:n}return r.low}var s=Math.max(r.open,r.close);if(n>s)return r.high-n>n-s?s:r.high;var c=Math.min(r.open,r.close);return c>n?c-n>n-r.low?r.low:c:n-c>s-n?s:c}},{key:"mouseMoveForDrawing",value:function(t,e){var i=this._xAxis.convertFromPixel(t.x),n=this._chartStore.timeScaleStore().dataIndexToTimestamp(i),r=this._performValue(t.y,i,e.paneId);this._points[this._drawStep-1]={timestamp:n,value:r,dataIndex:i},this.performEventMoveForDrawing({step:this._drawStep,mode:this._mode,points:this._points,movePoint:{timestamp:n,value:r,dataIndex:i},xAxis:this._xAxis,yAxis:this._yAxis}),this.onDrawing({id:this._id,step:this._drawStep,points:this._points})}},{key:"mouseLeftButtonDownForDrawing",value:function(){this._drawStep===this._totalStep-1?(this._drawStep=-1,this._chartStore.shapeStore().progressInstanceComplete(),this.onDrawEnd({id:this._id,points:this._points})):this._drawStep++}},{key:"mousePressedPointMove",value:function(t,e){var i=this._chartStore.shapeStore().eventOperate(),n=i.click.elementIndex;if(!this._lock&&i.click.id===this._id&&i.click.element===Ut&&-1!==n){var r=this._xAxis.convertFromPixel(t.x),a=this._chartStore.timeScaleStore().dataIndexToTimestamp(r),o=this._performValue(t.y,r,e.paneId);this._points[n].timestamp=a,this._points[n].dataIndex=r,this._points[n].value=o,this.performEventPressedMove({points:this._points,mode:this._mode,pressPointIndex:n,pressPoint:{dataIndex:r,timestamp:a,value:o},xAxis:this._xAxis,yAxis:this._yAxis}),this.onPressedMove({id:this._id,element:Ut,points:this._points,event:e})}}},{key:"startPressedOtherMove",value:function(t){var e=this._xAxis.convertFromPixel(t.x),i=this._yAxis.convertFromPixel(t.y);this._prevPressPoint={dataIndex:e,value:i},this._prevPoints=x(this._points)}},{key:"mousePressedOtherMove",value:function(t,e){var i=this;if(!this._lock&&this._prevPressPoint){var n=this._xAxis.convertFromPixel(t.x),r=this._yAxis.convertFromPixel(t.y),a=n-this._prevPressPoint.dataIndex,o=r-this._prevPressPoint.value;this._points=this._prevPoints.map(function(t){b(t.dataIndex)||(t.dataIndex=i._chartStore.timeScaleStore().timestampToDataIndex(t.timestamp));var e=t.dataIndex+a;return{dataIndex:e,value:t.value+o,timestamp:i._chartStore.timeScaleStore().dataIndexToTimestamp(e)}}),this.onPressedMove({id:this._id,element:"other",points:this._points,event:e})}}},{key:"onDrawStart",value:function(t){}},{key:"onDrawing",value:function(t){}},{key:"onDrawEnd",value:function(t){}},{key:"onPressedMove",value:function(t){}},{key:"onRemove",value:function(t){}},{key:"checkEventCoordinateOnShape",value:function(t){}},{key:"createShapeDataSource",value:function(t){}},{key:"performEventMoveForDrawing",value:function(t){}},{key:"performEventPressedMove",value:function(t){}}]),i}();function Jt(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}function Qt(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Jt(Object(i),!0).forEach(function(e){wt(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Jt(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function te(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return ee(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?ee(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function ee(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}var ie=function(){function t(e){v(this,t),this._chartStore=e,this._templates=this._createTemplates(),this._eventOperate={click:{id:"",element:Zt,elementIndex:-1},hover:{id:"",element:Zt,elementIndex:-1}},this._progressInstance=null,this._pressedInstance=null,this._instances=new Map}return _(t,[{key:"_createTemplates",value:function(){var t={},e=d.shapeExtensions;for(var i in e){var n=this._createTemplateClass(e[i]);n&&(t[i]=n)}return t}},{key:"_createTemplateClass",value:function(t){var e=t.name,i=t.totalStep,n=t.checkEventCoordinateOnShape,r=t.createShapeDataSource,a=t.performEventPressedMove,o=t.performEventMoveForDrawing,s=t.drawExtend;if(!(e&&w(i)&&S(n)&&S(r)))return null;var c=function(t){ct(r,qt);var n=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(r);function r(t){var a=t.id,o=t.chartStore,s=t.xAxis,c=t.yAxis,h=t.points,l=t.styles,u=t.lock,f=t.mode,d=t.data;return v(this,r),n.call(this,{id:a,name:e,totalStep:i,chartStore:o,xAxis:s,yAxis:c,points:h,styles:l,lock:u,mode:f,data:d})}return _(r)}();return c.prototype.checkEventCoordinateOnShape=n,c.prototype.createShapeDataSource=r,S(a)&&(c.prototype.performEventPressedMove=a),S(o)&&(c.prototype.performEventMoveForDrawing=o),S(s)&&(c.prototype.drawExtend=s),c}},{key:"addTemplate",value:function(t){var e=this;t.forEach(function(t){var i=e._createTemplateClass(t);i&&(e._templates[t.name]=i)})}},{key:"getTemplate",value:function(t){return this._templates[t]}},{key:"getInstance",value:function(t){var e,i=te(this._instances);try{for(i.s();!(e=i.n()).done;){var n=(e.value[1]||[]).find(function(e){return e.id()===t});if(n)return n}}catch(t){i.e(t)}finally{i.f()}return null}},{key:"hasInstance",value:function(t){return!!this.getInstance(t)}},{key:"addInstance",value:function(t,e){t.isDrawing()?this._progressInstance={paneId:e,instance:t,fixed:b(e)}:(this._instances.has(e)||this._instances.set(e,[]),this._instances.get(e).push(t)),this._chartStore.invalidate(1)}},{key:"progressInstance",value:function(){return this._progressInstance||{}}},{key:"progressInstanceComplete",value:function(){var t=this.progressInstance(),e=t.instance,i=t.paneId;e&&!e.isDrawing()&&(this._instances.has(i)||this._instances.set(i,[]),this._instances.get(i).push(e),this._progressInstance=null)}},{key:"updateProgressInstance",value:function(t,e){var i=this.progressInstance(),n=i.instance;n&&!i.fixed&&(n.setYAxis(t),this._progressInstance.paneId=e)}},{key:"pressedInstance",value:function(){return this._pressedInstance||{}}},{key:"updatePressedInstance",value:function(t,e,i){this._pressedInstance=t?{instance:t,paneId:e,element:i}:null}},{key:"instances",value:function(t){return this._instances.get(t)||[]}},{key:"setInstanceOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.id,i=t.styles,n=t.lock,r=t.mode,a=t.data,o=t.points,s=this._chartStore.styleOptions().shape,c=!1,h=function(t){t.setLock(n),t.setMode(r),(t.setStyles(i,s)||t.setData(a)||t.setPoints(o))&&(c=!0)};if(b(e)){var l=this.getInstance(e);l&&h(l)}else this._instances.forEach(function(t){return t.forEach(h)});c&&this._chartStore.invalidate(1)}},{key:"getInstanceInfo",value:function(t){var e=function(t){return{name:t.name(),id:t.id(),totalStep:t.totalStep(),lock:t.lock(),mode:t.mode(),points:t.points(),styles:t.styles(),data:t.data()}},i=this.progressInstance();if(!b(t)){var n={};return this._instances.forEach(function(t,r){n[r]=t.map(function(t){return e(t)}),i.paneId===r&&i.instance&&n[r].push(e(i.instance))}),n}if(i.instance&&i.instance.id()===t)return e(i.instance);var r=this.getInstance(t);return r?e(r):null}},{key:"removeInstance",value:function(t){var e=!1,i=this.progressInstance().instance;if(!i||b(t)&&i.id()!==t||(i.onRemove({id:i.id()}),this._progressInstance=null,e=!0),b(t)){var n,r=te(this._instances);try{for(r.s();!(n=r.n()).done;){var a=n.value,o=a[1]||[],s=o.findIndex(function(e){return e.id()===t});if(s>-1){o[s].onRemove({id:o[s].id()}),o.splice(s,1),0===o.length&&this._instances.delete(a[0]),e=!0;break}}}catch(t){r.e(t)}finally{r.f()}}else this._instances.forEach(function(t){t.length>0&&t.forEach(function(t){t.onRemove({id:t.id()})})}),this._instances.clear(),e=!0;e&&this._chartStore.invalidate(1)}},{key:"eventOperate",value:function(){return this._eventOperate}},{key:"setEventOperate",value:function(t){var e,i,n=this._eventOperate,r=n.hover,a=n.click;return!t.hover||r.id===t.hover.id&&r.element===t.hover.element&&r.elementIndex===t.hover.elementIndex||(this._eventOperate.hover=Qt({},t.hover),e=!0),!t.click||a.id===t.click.id&&a.element===t.click.element&&a.elementIndex===t.click.elementIndex||(this._eventOperate.click=Qt({},t.click),i=!0),e||i}},{key:"isEmpty",value:function(){return 0===this._instances.size&&!this.progressInstance().instance}},{key:"isDrawing",value:function(){var t=this.progressInstance().instance;return t&&t.isDrawing()}},{key:"isPressed",value:function(){return!!this.pressedInstance().instance}}]),t}();function ne(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}function re(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}var ae=function(){function t(e){v(this,t),this._chartStore=e,this._annotations=new Map,this._visibleAnnotations=new Map,this._eventOperate={id:""}}return _(t,[{key:"eventOperate",value:function(){return this._eventOperate}},{key:"setEventOperate",value:function(t){t&&this._eventOperate.id!==t.id&&(this._eventOperate=function(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?re(Object(i),!0).forEach(function(e){wt(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):re(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}({},t))}},{key:"createVisibleAnnotations",value:function(){var t=this;this._visibleAnnotations.clear(),this._annotations.size>0&&this._chartStore.visibleDataList().forEach(function(e){var i=e.data,n=e.x;t._annotations.forEach(function(e,r){if(e.size>0){var a=e.get(i.timestamp)||[];if(a.length>0){var o,s=function(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return ne(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?ne(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}(a);try{for(s.s();!(o=s.n()).done;){var c=o.value;c.createSymbolCoordinate(n),t._visibleAnnotations.has(r)?t._visibleAnnotations.get(r).push(c):t._visibleAnnotations.set(r,[c])}}catch(t){s.e(t)}finally{s.f()}}}})})}},{key:"add",value:function(t,e){var i=this;this._annotations.has(e)||this._annotations.set(e,new Map),t.forEach(function(t){var n=i._annotations.get(e),r=t.points().timestamp;n.has(r)?n.get(r).push(t):n.set(r,[t])}),this.createVisibleAnnotations(),this._chartStore.invalidate(1)}},{key:"get",value:function(t){return this._visibleAnnotations.get(t)}},{key:"remove",value:function(t,e){var i=!1;if(b(t)){if(this._annotations.has(t))if(b(e)){var n=this._annotations.get(t);[].concat(e).forEach(function(t){var e=t.timestamp;n.has(e)&&(i=!0,n.delete(e))}),0===n.size&&this._annotations.delete(t),i&&this.createVisibleAnnotations()}else i=!0,this._annotations.delete(t),this._visibleAnnotations.delete(t)}else i=!0,this._annotations.clear(),this._visibleAnnotations.clear();i&&this._chartStore.invalidate(1)}},{key:"isEmpty",value:function(){return 0===this._visibleAnnotations.size}}]),t}(),oe=function(){function t(e){v(this,t),this._chartStore=e,this._tags=new Map}return _(t,[{key:"_getById",value:function(t,e){var i=this.get(e);return i?i.get(t):null}},{key:"has",value:function(t,e){return!!this._getById(t,e)}},{key:"update",value:function(t,e,i){var n=this._getById(t,e);return!!n&&n.update(i)}},{key:"get",value:function(t){return this._tags.get(t)}},{key:"add",value:function(t,e){this._tags.has(e)||this._tags.set(e,new Map);var i=this._tags.get(e);t.forEach(function(t){i.set(t.id(),t)}),this._chartStore.invalidate(1)}},{key:"remove",value:function(t,e){var i=!1;if(b(t)){if(this._tags.has(t))if(b(e)){var n=this._tags.get(t);[].concat(e).forEach(function(t){n.has(t)&&(i=!0,n.delete(t))}),0===n.size&&this._tags.delete(t)}else i=!0,this._tags.delete(t)}else i=!0,this._tags.clear();i&&this._chartStore.invalidate(1)}}]),t}();function se(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}function ce(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?se(Object(i),!0).forEach(function(e){wt(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):se(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}var he=function(){function t(e){v(this,t),this._chartStore=e,this._crosshair={}}return _(t,[{key:"set",value:function(t,e){var i,n,r=this._chartStore.dataList(),a=t||{},o=r[n=b(a.x)?0>(i=this._chartStore.timeScaleStore().coordinateToDataIndex(a.x))?0:i>r.length-1?r.length-1:i:i=r.length-1],s=this._chartStore.timeScaleStore().dataIndexToCoordinate(i),c=this._crosshair.x,h=this._crosshair.y,l=this._crosshair.paneId;this._crosshair=ce(ce({},a),{},{realX:s,kLineData:o,realDataIndex:i,dataIndex:n}),o&&this._chartStore.crosshairChange(this._crosshair),c===a.x&&h===a.y&&l===a.paneId||e||this._chartStore.invalidate(1)}},{key:"recalculate",value:function(t){this.set(this._crosshair,t)}},{key:"get",value:function(){return this._crosshair}}]),t}(),le=function(){function t(){v(this,t),this._observers=[]}return _(t,[{key:"subscribe",value:function(t){0>this._observers.indexOf(t)&&this._observers.push(t)}},{key:"unsubscribe",value:function(t){var e=this._observers.indexOf(t);e>-1?this._observers.splice(e,1):this._observers=[]}},{key:"execute",value:function(t){this._observers.forEach(function(e){e(t)})}},{key:"hasObservers",value:function(){return this._observers.length>0}}]),t}(),ue=function(){function t(){v(this,t),this._delegates=new Map}return _(t,[{key:"execute",value:function(t,e){this.has(t)&&this._delegates.get(t).execute(e)}},{key:"has",value:function(t){return this._delegates.has(t)&&this._delegates.get(t).hasObservers()}},{key:"subscribe",value:function(t,e){this._delegates.has(t)||this._delegates.set(t,new le),this._delegates.get(t).subscribe(e)}},{key:"unsubscribe",value:function(t,e){if(at(t)){var i=this._delegates.get(t);i&&(i.unsubscribe(e),i.hasObservers()||this._delegates.delete(t))}}}]),t}(),fe=function(){function t(e,i){v(this,t),this._handler=i,this._styleOptions=x(Z),y(this._styleOptions,e),this._pricePrecision=2,this._volumePrecision=0,this._dataList=[],this._visibleDataList=[],this._dragPaneFlag=!1,this._timeScaleStore=new ot(this),this._technicalIndicatorStore=new kt(this),this._shapeStore=new ie(this),this._annotationStore=new ae(this),this._tagStore=new oe(this),this._crosshairStore=new he(this),this._actionStore=new ue}return _(t,[{key:"adjustVisibleDataList",value:function(){this._visibleDataList=[];for(var t=this._timeScaleStore.from(),e=this._timeScaleStore.to(),i=t;e>i;i++){var n=this._dataList[i],r=this._timeScaleStore.dataIndexToCoordinate(i);this._visibleDataList.push({index:i,x:r,data:n})}this._annotationStore.createVisibleAnnotations()}},{key:"styleOptions",value:function(){return this._styleOptions}},{key:"applyStyleOptions",value:function(t){y(this._styleOptions,t)}},{key:"pricePrecision",value:function(){return this._pricePrecision}},{key:"volumePrecision",value:function(){return this._volumePrecision}},{key:"setPriceVolumePrecision",value:function(t,e){this._pricePrecision=t,this._volumePrecision=e,this._technicalIndicatorStore.setSeriesPrecision(t,e)}},{key:"dataList",value:function(){return this._dataList}},{key:"visibleDataList",value:function(){return this._visibleDataList}},{key:"addData",value:function(t,e,i){if(k(t)){if(g(t)){this._timeScaleStore.setLoading(!1),this._timeScaleStore.setMore(!E(i)||i);var n=0===this._dataList.length;this._dataList=t.concat(this._dataList),n&&this._timeScaleStore.resetOffsetRightSpace(),this._timeScaleStore.adjustFromTo()}else if(e<this._dataList.length)this._dataList[e]=t,this.adjustVisibleDataList();else{this._dataList.push(t);var r=this._timeScaleStore.offsetRightBarCount();0>r&&this._timeScaleStore.setOffsetRightBarCount(--r),this._timeScaleStore.adjustFromTo()}this._crosshairStore.recalculate(!0)}}},{key:"clearDataList",value:function(){this._dataList=[],this._visibleDataList=[],this._timeScaleStore.clear()}},{key:"timeScaleStore",value:function(){return this._timeScaleStore}},{key:"technicalIndicatorStore",value:function(){return this._technicalIndicatorStore}},{key:"shapeStore",value:function(){return this._shapeStore}},{key:"annotationStore",value:function(){return this._annotationStore}},{key:"tagStore",value:function(){return this._tagStore}},{key:"crosshairStore",value:function(){return this._crosshairStore}},{key:"actionStore",value:function(){return this._actionStore}},{key:"invalidate",value:function(t){this._handler.invalidate(t)}},{key:"crosshairChange",value:function(t){this._handler.crosshair(t)}},{key:"dragPaneFlag",value:function(){return this._dragPaneFlag}},{key:"setDragPaneFlag",value:function(t){this._dragPaneFlag=t}}]),t}();function de(){return(de="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,i){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=lt(t)););return t}(t,e);if(n){var r=Object.getOwnPropertyDescriptor(n,e);return r.get?r.get.call(3>arguments.length?t:i):r.value}}).apply(this,arguments)}function ve(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=document.createElement(t);for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(i.style[n]=e[n]);return i}var pe=function(){function t(e){v(this,t),this._height=-1,this._container=e.container,this._chartStore=e.chartStore,this._initBefore(e),this._initElement(),this._mainWidget=this._createMainWidget(this._element,e),this._yAxisWidget=this._createYAxisWidget(this._element,e)}return _(t,[{key:"_initBefore",value:function(t){}},{key:"_initElement",value:function(){this._element=ve("div",{width:"100%",margin:"0",padding:"0",position:"relative",overflow:"hidden",boxSizing:"border-box"});var t=this._container.lastChild;t?this._container.insertBefore(this._element,t):this._container.appendChild(this._element)}},{key:"_createMainWidget",value:function(t,e){}},{key:"_createYAxisWidget",value:function(t,e){}},{key:"container",value:function(t){switch(t){case"content":return this._mainWidget.container();case"yAxis":return this._yAxisWidget.container();default:return this._element}}},{key:"width",value:function(){return this._element.offsetWidth}},{key:"setWidth",value:function(t,e){this._mainWidget.setWidth(t),this._yAxisWidget&&this._yAxisWidget.setWidth(e)}},{key:"height",value:function(){return this._height}},{key:"setHeight",value:function(t){this._height=t,this._mainWidget.setHeight(t),this._yAxisWidget&&this._yAxisWidget.setHeight(t)}},{key:"setOffsetLeft",value:function(t,e){this._mainWidget.setOffsetLeft(t),this._yAxisWidget&&this._yAxisWidget.setOffsetLeft(e)}},{key:"layout",value:function(){this._element.offsetHeight!==this._height&&(this._element.style.height="".concat(this._height,"px")),this._mainWidget.layout(),this._yAxisWidget&&this._yAxisWidget.layout()}},{key:"invalidate",value:function(t){this._yAxisWidget&&this._yAxisWidget.invalidate(t),this._mainWidget.invalidate(t)}},{key:"createHtml",value:function(t){var e=t.id,i=t.content,n=t.style;return"yAxis"===t.position?this._yAxisWidget&&this._yAxisWidget.createHtml({id:e,content:i,style:n}):this._mainWidget.createHtml({id:e,content:i,style:n})}},{key:"removeHtml",value:function(t){this._yAxisWidget&&this._yAxisWidget.removeHtml(t),this._mainWidget.removeHtml(t)}},{key:"getImage",value:function(t){var e=this._element.offsetWidth,i=this._element.offsetHeight,n=ve("canvas",{width:"".concat(e,"px"),height:"".concat(i,"px"),boxSizing:"border-box"}),r=n.getContext("2d"),a=Nt(n);n.width=e*a,n.height=i*a,r.scale(a,a);var o=this._mainWidget.getElement(),s=o.offsetWidth,c=o.offsetHeight,h=parseInt(o.style.left,10);if(r.drawImage(this._mainWidget.getImage(t),h,0,s,c),this._yAxisWidget){var l=this._yAxisWidget.getElement(),u=l.offsetWidth,f=l.offsetHeight,d=parseInt(l.style.left,10);r.drawImage(this._yAxisWidget.getImage(t),d,0,u,f)}return n}},{key:"destroy",value:function(){this._container.removeChild(this._element)}}]),t}();function _e(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}var me=function(){function t(e){v(this,t),this._width=0,this._height=0,this._initElement(e.container),this._mainView=this._createMainView(this._element,e),this._overlayView=this._createOverlayView(this._element,e),this._htmlBaseId=0,this._htmls=new Map}return _(t,[{key:"_initElement",value:function(t){this._element=ve("div",{margin:"0",padding:"0",position:"absolute",top:"0",overflow:"hidden",boxSizing:"border-box"}),t.appendChild(this._element)}},{key:"_createMainView",value:function(t,e){}},{key:"_createOverlayView",value:function(t,e){}},{key:"container",value:function(){return this._element}},{key:"createHtml",value:function(t){var e=t.id,i=t.content,n=t.style,r=ve("div",function(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?_e(Object(i),!0).forEach(function(e){wt(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):_e(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}({boxSizing:"border-box",position:"absolute",zIndex:12},void 0===n?{}:n));if(P(i)){var a=i.replace(/(^\s*)|(\s*$)/g,"");r.innerHTML=a}else r.appendChild(i);var o=e||"html_".concat(++this._htmlBaseId);return this._htmls.has(o)?this._element.replaceChild(r,this._htmls.get(o)):this._element.appendChild(r),this._htmls.set(o,r),o}},{key:"removeHtml",value:function(t){var e=this;t?[].concat(t).forEach(function(t){var i=e._htmls.get(t);i&&(e._element.removeChild(i),e._htmls.delete(t))}):(this._htmls.forEach(function(t){e._element.removeChild(t)}),this._htmls.clear())}},{key:"getElement",value:function(){return this._element}},{key:"setWidth",value:function(t){this._width=t,this._mainView.setWidth(t),this._overlayView.setWidth(t)}},{key:"setHeight",value:function(t){this._height=t,this._mainView.setHeight(t),this._overlayView.setHeight(t)}},{key:"setOffsetLeft",value:function(t){this._element.style.left="".concat(t,"px")}},{key:"layout",value:function(){this._element.offsetWidth!==this._width&&(this._element.style.width="".concat(this._width,"px")),this._element.offsetHeight!==this._height&&(this._element.style.height="".concat(this._height,"px")),this._mainView.layout(),this._overlayView.layout()}},{key:"invalidate",value:function(t){switch(t){case 1:this._overlayView.flush();break;case 2:case 3:this._mainView.flush(),this._overlayView.flush()}}},{key:"getImage",value:function(t){var e=ve("canvas",{width:"".concat(this._width,"px"),height:"".concat(this._height,"px"),boxSizing:"border-box"}),i=e.getContext("2d"),n=Nt(e);return e.width=this._width*n,e.height=this._height*n,i.scale(n,n),i.drawImage(this._mainView.getImage(),0,0,this._width,this._height),t&&this._overlayView&&i.drawImage(this._overlayView.getImage(),0,0,this._width,this._height),e}}]),t}();function ye(t){return window.requestAnimationFrame?window.requestAnimationFrame(t):window.setTimeout(t,20)}function xe(t){window.cancelAnimationFrame||clearTimeout(t),window.cancelAnimationFrame(t)}var ge=function(){function t(e,i){v(this,t),this._chartStore=i,this._initCanvas(e)}return _(t,[{key:"_initCanvas",value:function(t){this._canvas=ve("canvas",{position:"absolute",top:"0",left:"0",zIndex:"2",boxSizing:"border-box"}),this._ctx=this._canvas.getContext("2d"),t.appendChild(this._canvas)}},{key:"_redraw",value:function(t){this._ctx.clearRect(0,0,this._canvas.offsetWidth,this._canvas.offsetHeight),t&&t(),this._draw()}},{key:"_draw",value:function(){}},{key:"setWidth",value:function(t){this._width=t}},{key:"setHeight",value:function(t){this._height=t}},{key:"layout",value:function(){var t=this;this._height!==this._canvas.offsetHeight||this._width!==this._canvas.offsetWidth?this._redraw(function(){var e=Nt(t._canvas);t._canvas.style.width="".concat(t._width,"px"),t._canvas.style.height="".concat(t._height,"px"),t._canvas.width=Math.floor(t._width*e),t._canvas.height=Math.floor(t._height*e),t._ctx.scale(e,e)}):this.flush()}},{key:"flush",value:function(){var t=this;this.requestAnimationId&&(xe(this.requestAnimationId),this.requestAnimationId=null),this.requestAnimationId=ye(function(){t._redraw()})}},{key:"getImage",value:function(){return this._canvas}}]),t}();var Se=function(t){ct(i,ge);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t,n,r,a,o){var s;return v(this,i),(s=e.call(this,t,n))._xAxis=r,s._yAxis=a,s._paneId=o,s}return _(i,[{key:"_draw",value:function(){this._ctx.globalCompositeOperation="destination-over",this._drawContent()}},{key:"_drawContent",value:function(){this._drawTechs(),this._drawGrid()}},{key:"_drawGrid",value:function(){var t=this,e=this._chartStore.styleOptions().grid;if(e.show){var i=e.horizontal;this._ctx.save(),i.show&&(this._ctx.strokeStyle=i.color,this._ctx.lineWidth=i.size,this._ctx.setLineDash(i.style===I?i.dashValue:[]),this._yAxis.ticks().forEach(function(e){Vt(t._ctx,e.y,0,t._width)}));var n=e.vertical;n.show&&(this._ctx.strokeStyle=n.color,this._ctx.lineWidth=n.size,this._ctx.setLineDash(n.style===I?n.dashValue:[]),this._xAxis.ticks().forEach(function(e){Ht(t._ctx,e.x,0,t._height)})),this._ctx.restore()}}},{key:"_drawTechs",value:function(){var t=this;this._ctx.globalCompositeOperation="source-over";var e=this._chartStore.timeScaleStore().to(),i=this._chartStore.styleOptions().technicalIndicator;this._chartStore.technicalIndicatorStore().instances(this._paneId).forEach(function(n){var r=n.plots,a=[],o=t._chartStore.dataList(),s=n.result,c=n.styles||i;n.render&&(t._ctx.save(),n.render({ctx:t._ctx,dataSource:{from:t._chartStore.timeScaleStore().from(),to:e,kLineDataList:t._chartStore.dataList(),technicalIndicatorDataList:s},viewport:{width:t._width,height:t._height,dataSpace:t._chartStore.timeScaleStore().dataSpace(),barSpace:t._chartStore.timeScaleStore().barSpace()},styles:c,xAxis:t._xAxis,yAxis:t._yAxis}),t._ctx.restore());var h=c.line.colors||[],l=h.length,u=[],f=[],d=t._yAxis.isCandleYAxis();t._ctx.lineWidth=1,t._drawGraphics(function(i,v,p,_,m){var y=s[v]||{},x=0;n.shouldOhlc&&!d&&t._drawCandleBar(i,_,m,p,c.bar,"ohlc"),r.forEach(function(n){var r=y[n.key],d=t._yAxis.convertToPixel(r);switch(n.type){case mt:if(b(r)){var p=xt(o,s,v,n,c,{color:c.circle.noChangeColor,isStroke:!0});t._drawCircle({x:i,y:d,radius:_,color:p.color,isStroke:p.isStroke})}break;case"bar":if(b(r)){var m;m=b(n.baseValue)?n.baseValue:t._yAxis.min();var g=t._yAxis.convertToPixel(m),S=Math.abs(g-d),k={x:i-_,width:2*_,height:Math.max(1,S)};k.y=d>g?g:1>S?g-1:d;var w=xt(o,s,v,n,c,{color:c.bar.noChangeColor});k.color=w.color,k.isStroke=w.isStroke,t._drawBar(k)}break;case _t:var E=null;if(b(r)){E=xt(o,s,v,n,c,{color:h[x%l]});var P={x:i,y:d},C=u[x];f[x]||(f[x]=[]),f[x].push(P),C&&(C.color!==E.color||C.isDashed!==E.isDashed)&&(a.push({color:C.color,isDashed:C.isDashed,coordinates:f[x]}),f[x]=[P]),v===e-1&&a.push({color:E.color,isDashed:E.isDashed,coordinates:f[x]})}u[x]=E,x++}})},function(){t._drawLines(a,c)})}),this._ctx.globalCompositeOperation="destination-over"}},{key:"_drawGraphics",value:function(t,e){var i=this._chartStore.visibleDataList(),n=this._chartStore.timeScaleStore().barSpace(),r=this._chartStore.timeScaleStore().halfBarSpace();i.forEach(function(e,i){t(e.x,e.index,e.data,r,n,i)}),e&&e()}},{key:"_drawLines",value:function(t,e){var i=this;this._ctx.lineWidth=e.line.size,t.forEach(function(t){i._ctx.strokeStyle=t.color,i._ctx.setLineDash(t.isDashed?e.line.dashValue:[]),jt(i._ctx,t.coordinates)})}},{key:"_drawBar",value:function(t){t.isStroke?(this._ctx.strokeStyle=t.color,this._ctx.strokeRect(t.x+.5,t.y,t.width-1,t.height)):(this._ctx.fillStyle=t.color,this._ctx.fillRect(t.x,t.y,t.width,t.height))}},{key:"_drawCircle",value:function(t){this._ctx.strokeStyle=t.color,this._ctx.fillStyle=t.color,this._ctx.beginPath(),this._ctx.arc(t.x,t.y,t.radius,2*Math.PI,0,!0),t.isStroke?this._ctx.stroke():this._ctx.fill(),this._ctx.closePath()}},{key:"_drawCandleBar",value:function(t,e,i,n,r,a){var o=n.open,s=n.close,c=n.high,h=n.low;s>o?(this._ctx.strokeStyle=r.upColor,this._ctx.fillStyle=r.upColor):o>s?(this._ctx.strokeStyle=r.downColor,this._ctx.fillStyle=r.downColor):(this._ctx.strokeStyle=r.noChangeColor,this._ctx.fillStyle=r.noChangeColor);var l=this._yAxis.convertToPixel(o),u=this._yAxis.convertToPixel(s),f=[l,u,this._yAxis.convertToPixel(c),this._yAxis.convertToPixel(h)];f.sort(function(t,e){return t-e}),this._ctx.fillRect(t-.5,f[0],1,f[1]-f[0]),this._ctx.fillRect(t-.5,f[2],1,f[3]-f[2]);var d=Math.max(1,f[2]-f[1]);switch(a){case B:this._ctx.fillRect(t-e,f[1],i,d);break;case"candle_stroke":this._ctx.strokeRect(t-e+.5,f[1],i-1,d);break;case"candle_up_stroke":s>o?this._ctx.strokeRect(t-e+.5,f[1],i-1,d):this._ctx.fillRect(t-e,f[1],i,d);break;case"candle_down_stroke":s>o?this._ctx.fillRect(t-e,f[1],i,d):this._ctx.strokeRect(t-e+.5,f[1],i-1,d);break;default:this._ctx.fillRect(t-.5,f[0],1,f[3]-f[0]),this._ctx.fillRect(t-e,l,e,1),this._ctx.fillRect(t,u,e,1)}}}]),i}();function ke(t,e,i,n,r){t.fillStyle=e,t.fillText(r,i,n)}var we=function(t){ct(i,ge);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t,n,r,a,o){var s;return v(this,i),(s=e.call(this,t,n))._xAxis=r,s._yAxis=a,s._paneId=o,s}return _(i,[{key:"_draw",value:function(){this._ctx.textBaseline="alphabetic",this._drawTag(),this._drawShape(),this._drawAnnotation();var t=this._chartStore.crosshairStore().get();if(t.kLineData){var e=this._chartStore.styleOptions().crosshair;t.paneId===this._paneId&&this._drawCrosshairLine(e,"horizontal",t.y,0,this._width,Vt),t.paneId&&this._drawCrosshairLine(e,"vertical",t.realX,0,this._height,Ht),this._drawTooltip(t,this._chartStore.technicalIndicatorStore().instances(this._paneId))}}},{key:"_drawAnnotation",value:function(){var t=this,e=this._chartStore.annotationStore().get(this._paneId);e&&e.forEach(function(e){e.draw(t._ctx)})}},{key:"_drawTag",value:function(){var t=this,e=this._chartStore.tagStore().get(this._paneId);e&&e.forEach(function(e){e.drawMarkLine(t._ctx)})}},{key:"_drawShape",value:function(){var t=this;this._chartStore.shapeStore().instances(this._paneId).forEach(function(e){e.draw(t._ctx)});var e=this._chartStore.shapeStore().progressInstance();e.paneId===this._paneId&&e.instance.draw(this._ctx)}},{key:"_drawTooltip",value:function(t,e){var i=this._chartStore.styleOptions().technicalIndicator;this._drawBatchTechToolTip(t,e,i,0,this._shouldDrawTooltip(t,i.tooltip))}},{key:"_drawCrosshairLine",value:function(t,e,i,n,r,a){var o=t[e],s=o.line;t.show&&o.show&&s.show&&(this._ctx.save(),this._ctx.lineWidth=s.size,this._ctx.strokeStyle=s.color,s.style===I&&this._ctx.setLineDash(s.dashValue),a(this._ctx,i,n,r),this._ctx.restore())}},{key:"_drawBatchTechToolTip",value:function(t,e,i){var n=this,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;if(!(arguments.length>4?arguments[4]:void 0))return 0;var a=i.tooltip,o=r,s=o;return e.forEach(function(e){o+=a.text.marginTop+n._drawTechTooltip(t,e,i,o)+a.text.marginBottom}),o-s}},{key:"_drawTechTooltip",value:function(t,e,i){var n=this,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=i.tooltip,o=a.text,s=o.marginLeft,c=o.marginRight,h=o.size,l=o.color,u=0,f=o.marginTop+r,d=h,v=this._getTechTooltipData(t,e,i);if(this._ctx.textBaseline="top",this._ctx.font=Wt(h,o.weight,o.family),a.showName){var p=v.name,_=Yt(this._ctx,p);ke(this._ctx,l,u+=s,f,p),u+=_,a.showParams||(u+=c)}if(a.showParams){var m=v.calcParamText,y=Yt(this._ctx,m);a.showName||(u+=s),ke(this._ctx,l,u,f,m),u+=y+c}return v.values.forEach(function(t){u+=s;var e="".concat(t.title).concat(t.value),i=Yt(n._ctx,e);u+i>n._width&&(u=s,d+=h+1,f+=h+1),ke(n._ctx,t.color||o.color,u,f,e),u+=i+c}),d}},{key:"_shouldDrawTooltip",value:function(t,e){var i=e.showRule;return i===z||"follow_cross"===i&&!!t.paneId}},{key:"_getTechTooltipData",value:function(t,e,i){var n=this._chartStore.dataList(),r=e.result,a="",o=e.calcParams;if(o.length>0){var s=o.map(function(t){return k(t)?t.value:t});a="(".concat(s.join(","),")")}var c=[];if(S(e.createToolTipDataSource))c=e.createToolTipDataSource({dataSource:{from:this._chartStore.timeScaleStore().from(),to:this._chartStore.timeScaleStore().to(),kLineDataList:this._chartStore.dataList(),technicalIndicatorDataList:r},viewport:{width:this._width,height:this._height,dataSpace:this._chartStore.timeScaleStore().dataSpace(),barSpace:this._chartStore.timeScaleStore().barSpace()},crosshair:t,technicalIndicator:e,xAxis:this._xAxis,yAxis:this._yAxis,defaultStyles:i})||[];else{var h=e.styles||i,l=r[t.dataIndex],u=e.precision,f=e.shouldFormatBigNumber,d=h.line.colors||[],v=d.length,p=0;e.plots.forEach(function(e){var a={};switch(e.type){case mt:a={color:h.circle.noChangeColor};break;case"bar":a={color:h.bar.noChangeColor};break;case _t:a={color:d[p%v]||i.tooltip.text.color},p++}var o=xt(n,r,t.dataIndex,e,h,a),s={};if(b(e.title)){var _=(l||{})[e.key];b(_)&&(_=J(_,u),f&&(_=Q(_))),s.title=e.title,s.value=_||i.tooltip.defaultValue,s.color=o.color,c.push(s)}})}return{values:c,name:e.shortName,calcParamText:a}}}]),i}();var be=function(t){ct(i,me);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(){return v(this,i),e.apply(this,arguments)}return _(i,[{key:"_createMainView",value:function(t,e){return new Se(t,e.chartStore,e.xAxis,e.yAxis,e.paneId)}},{key:"_createOverlayView",value:function(t,e){return new we(t,e.chartStore,e.xAxis,e.yAxis,e.paneId)}}]),i}();function Ee(t,e,i,n,r,a,o,s,c){Ce(t,e,r,a,o,s,c),Pe(t,i,n,r,a,o,s,c)}function Pe(t,e,i,n,r,a,o,s){t.lineWidth=i,t.strokeStyle=e,Ae(t,n,r,a,o,s),t.stroke()}function Ce(t,e,i,n,r,a,o){t.fillStyle=e,Ae(t,i,n,r,a,o),t.fill()}function Ae(t,e,i,n,r,a){t.beginPath(),t.moveTo(e+a,i),t.arcTo(e+n,i,e+n,i+r,a),t.arcTo(e+n,i+r,e,i+r,a),t.arcTo(e,i+r,e,i,a),t.arcTo(e,i,e+n,i,a),t.closePath()}function Ie(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}function Te(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Ie(Object(i),!0).forEach(function(e){wt(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Ie(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}var Me=function(t){ct(i,ge);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t,n,r,a){var o;return v(this,i),(o=e.call(this,t,n))._yAxis=r,o._paneId=a,o}return _(i,[{key:"_draw",value:function(){var t=this._chartStore.styleOptions().yAxis;t.show&&(this._drawAxisLine(t),this._drawTickLines(t),this._drawTickLabels(t),this._drawTechLastValue(),this._drawLastPriceLabel())}},{key:"_drawAxisLine",value:function(t){var e,i=t.axisLine;i.show&&(this._ctx.strokeStyle=i.color,this._ctx.lineWidth=i.size,e=this._yAxis.isFromYAxisZero()?0:this._width-1,Ht(this._ctx,e,0,this._height))}},{key:"_drawTickLines",value:function(t){var e=this,i=t.tickLine;if(i.show){this._ctx.lineWidth=i.size,this._ctx.strokeStyle=i.color;var n,r,a=i.length;this._yAxis.isFromYAxisZero()?(n=0,t.axisLine.show&&(n+=t.axisLine.size),r=n+a):(n=this._width,t.axisLine.show&&(n-=t.axisLine.size),r=n-a),this._yAxis.ticks().forEach(function(t){Vt(e._ctx,t.y,n,r)})}}},{key:"_drawTickLabels",value:function(t){var e=this,i=t.tickText;if(i.show){var n,r=t.tickLine,a=r.show,o=r.length;this._yAxis.isFromYAxisZero()?(n=i.paddingLeft,t.axisLine.show&&(n+=t.axisLine.size),a&&(n+=o),this._ctx.textAlign="left"):(n=this._width-i.paddingRight,t.axisLine.show&&(n-=t.axisLine.size),a&&(n-=o),this._ctx.textAlign="right"),this._ctx.textBaseline="middle",this._ctx.font=Wt(i.size,i.weight,i.family),this._ctx.fillStyle=i.color,this._yAxis.ticks().forEach(function(t){e._ctx.fillText(t.v,n,t.y)}),this._ctx.textAlign="left"}}},{key:"_drawTechLastValue",value:function(){var t=this,e=this._chartStore.styleOptions().technicalIndicator,i=e.lastValueMark;if(i.show&&i.text.show){var n=this._chartStore.technicalIndicatorStore().instances(this._paneId),r=this._chartStore.dataList();n.forEach(function(n){var a=n.result||[],o=a.length,s=a[o-1]||{},c={prev:{kLineData:r[o-2],technicalIndicatorData:a[o-2]},current:{kLineData:r[o-1],technicalIndicatorData:s},next:{kLineData:null,technicalIndicatorData:null}},h=n.precision,l=n.styles||e,u=l.line.colors||[],f=u.length,d=0;n.plots.forEach(function(e){var r,a=s[e.key];switch(e.type){case mt:r=e.color&&e.color(c,l)||l.circle.noChangeColor;break;case"bar":r=e.color&&e.color(c,l)||l.bar.noChangeColor;break;case _t:r=u[d%f],d++}b(a)&&t._drawMarkLabel(a,h,n.shouldFormatBigNumber,Te(Te({},i.text),{},{backgroundColor:r}))})})}}},{key:"_drawLastPriceLabel",value:function(){if(this._yAxis.isCandleYAxis()){var t=this._chartStore.styleOptions().candle.priceMark,e=t.last;if(t.show&&e.show&&e.text.show){var i=this._chartStore.dataList(),n=i[i.length-1];if(n){var r,a=n.close,o=n.open;r=a>o?e.upColor:o>a?e.downColor:e.noChangeColor,this._drawMarkLabel(a,this._chartStore.pricePrecision(),!1,Te(Te({},e.text),{},{backgroundColor:r}))}}}}},{key:"_drawMarkLabel",value:function(t,e,i,n){var r,a=n.size,o=n.weight,s=n.family,c=n.color,h=n.backgroundColor,l=n.borderRadius,u=n.paddingLeft,f=n.paddingTop,d=n.paddingRight,v=n.paddingBottom,p=this._yAxis.convertToNicePixel(t);if(this._yAxis.yAxisType()===L){var _=((this._chartStore.visibleDataList()[0]||{}).data||{}).close;r="".concat(((t-_)/_*100).toFixed(2),"%")}else r=J(t,e),i&&(r=Q(r));this._ctx.font=Wt(a,o,s);var m,y=Yt(this._ctx,r)+u+d,x=f+a+v;m=this._yAxis.isFromYAxisZero()?0:this._width-y,Ce(this._ctx,h,m,p-f-a/2,y,x,l),this._ctx.textBaseline="middle",ke(this._ctx,c,m+u,p,r)}}]),i}();var De=function(t){ct(i,ge);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t,n,r,a){var o;return v(this,i),(o=e.call(this,t,n))._yAxis=r,o._paneId=a,o}return _(i,[{key:"_draw",value:function(){this._ctx.textBaseline="middle",this._drawTag(),this._drawCrossHairLabel()}},{key:"_drawTag",value:function(){var t=this,e=this._chartStore.tagStore().get(this._paneId);e&&e.forEach(function(e){e.drawText(t._ctx)})}},{key:"_drawCrossHairLabel",value:function(){var t=this._chartStore.crosshairStore().get();if(t.paneId===this._paneId&&0!==this._chartStore.dataList().length){var e=this._chartStore.styleOptions().crosshair,i=e.horizontal,n=i.text;if(e.show&&i.show&&n.show){var r,a,o=this._yAxis.convertFromPixel(t.y);if(this._yAxis.yAxisType()===L){var s=(this._chartStore.visibleDataList()[0]||{}).data||{};r="".concat(((o-s.close)/s.close*100).toFixed(2),"%")}else{var c=this._chartStore.technicalIndicatorStore().instances(this._paneId),h=0,l=!1;this._yAxis.isCandleYAxis()?h=this._chartStore.pricePrecision():c.forEach(function(t){h=Math.max(t.precision,h),l||(l=t.shouldFormatBigNumber)}),r=J(o,h),l&&(r=Q(r))}var u=n.borderSize,f=Xt(this._ctx,r,n),d=$t(n);a=this._yAxis.isFromYAxisZero()?0:this._width-f,Ee(this._ctx,n.backgroundColor,n.borderColor,u,a,t.y-u-n.paddingTop-n.size/2,f,d,n.borderRadius),ke(this._ctx,n.color,a+u+n.paddingLeft,t.y,r)}}}}]),i}();var Oe=function(t){ct(i,me);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(){return v(this,i),e.apply(this,arguments)}return _(i,[{key:"_createMainView",value:function(t,e){return new Me(t,e.chartStore,e.yAxis,e.paneId)}},{key:"_createOverlayView",value:function(t,e){return new De(t,e.chartStore,e.yAxis,e.paneId)}}]),i}(),Le=function(){function t(e){v(this,t),this._chartStore=e,this._width=0,this._height=0,this._cacheMinValue=0,this._cacheMaxValue=0,this._minValue=0,this._maxValue=0,this._range=0,this._realMinValue=0,this._realMaxValue=0,this._realRange=0,this._ticks=[],this._initMeasureCanvas()}return _(t,[{key:"_initMeasureCanvas",value:function(){var t=ve("canvas"),e=Nt(t);this._measureCtx=t.getContext("2d"),this._measureCtx.scale(e,e)}},{key:"min",value:function(){return this._minValue}},{key:"max",value:function(){return this._maxValue}},{key:"width",value:function(){return this._width}},{key:"height",value:function(){return this._height}},{key:"setWidth",value:function(t){this._width=t}},{key:"setHeight",value:function(t){this._height=t}},{key:"ticks",value:function(){return this._ticks}},{key:"computeAxis",value:function(t){var e=this._optimalMinMax(this._computeMinMax());return this._minValue=e.min,this._maxValue=e.max,this._range=e.range,this._realMinValue=e.realMin,this._realMaxValue=e.realMax,this._realRange=e.realRange,!(this._cacheMinValue===e.min&&this._cacheMaxValue===e.max&&!t||(this._cacheMinValue=e.min,this._cacheMaxValue=e.max,this._ticks=this._optimalTicks(this._computeTicks()),0))}},{key:"_computeMinMax",value:function(){}},{key:"_optimalMinMax",value:function(t){}},{key:"_computeTicks",value:function(){var t=[];if(this._range>=0){var e=this._computeInterval(this._realRange),i=e.interval,n=e.precision,r=et(Math.ceil(this._realMinValue/i)*i,n),a=et(Math.floor(this._realMaxValue/i)*i,n),o=0,s=r;if(0!==i)for(;a>=s;)t[o]={v:s.toFixed(n)},++o,s+=i}return t}},{key:"_optimalTicks",value:function(t){}},{key:"_computeInterval",value:function(t){var e,i,n,r,a=(i=Math.floor(it(e=t/8)),e=(1.5>(r=e/(n=nt(i)))?1:2.5>r?2:3.5>r?3:4.5>r?4:5.5>r?5:6.5>r?6:8)*n,-20>i?e:+e.toFixed(0>i?-i:0));return{interval:a,precision:function(t){var e=""+a,i=e.indexOf("e");if(i>0){var n=+e.slice(i+1);return 0>n?-n:0}var r=e.indexOf(".");return 0>r?0:e.length-1-r}()}}}]),t}();var Re=function(t){ct(i,Le);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t,n,r){var a;return v(this,i),(a=e.call(this,t))._isCandleYAxis=n,a._paneId=r,a}return _(i,[{key:"_computeMinMax",value:function(){var t,e=this,i=[Number.MAX_SAFE_INTEGER,Number.MIN_SAFE_INTEGER],n=[],r=!1,a=Number.MAX_SAFE_INTEGER,o=Number.MIN_SAFE_INTEGER,s=Number.MAX_SAFE_INTEGER;this._chartStore.technicalIndicatorStore().instances(this._paneId).forEach(function(i){if(r||(r=i.shouldOhlc),s=Math.min(s,i.precision),w(i.minValue)&&(a=Math.min(a,i.minValue)),w(i.maxValue)&&(o=Math.max(o,i.maxValue)),i.styles){t||(t={top:0,bottom:0});var c=i.styles.margin;w(c.top)&&(t.top=Math.max(1>c.top?c.top:c.top/e._height,t.top)),w(c.bottom)&&(t.bottom=Math.max(1>c.bottom?c.bottom:c.bottom/e._height,t.bottom))}n.push({plots:i.plots,result:i.result})});var c=4;if(this._isCandleYAxis){var h=this._chartStore.pricePrecision();c=s!==Number.MAX_SAFE_INTEGER?Math.min(s,h):h}else s!==Number.MAX_SAFE_INTEGER&&(c=s);var l=this._chartStore.visibleDataList(),u=this._chartStore.styleOptions().candle,f=u.type===F,d=u.area.value,v=this._isCandleYAxis&&!f||!this._isCandleYAxis&&r;return l.forEach(function(t){var r=t.index,a=t.data;v&&(i[0]=Math.min(i[0],a.low),i[1]=Math.max(i[1],a.high)),e._isCandleYAxis&&f&&(i[0]=Math.min(i[0],a[d]),i[1]=Math.max(i[1],a[d])),n.forEach(function(t){var e=t.result[r]||{};t.plots.forEach(function(t){var n=e[t.key];b(n)&&(i[0]=Math.min(i[0],n),i[1]=Math.max(i[1],n))})})}),i[0]!==Number.MAX_SAFE_INTEGER&&i[1]!==Number.MIN_SAFE_INTEGER?(i[0]=Math.min(a,i[0]),i[1]=Math.max(o,i[1])):(i[0]=0,i[1]=10),{min:i[0],max:i[1],precision:c,specifyMin:a,specifyMax:o,techGap:t}}},{key:"_optimalMinMax",value:function(t){var e,i,n=t.precision,r=t.specifyMin,a=t.specifyMax,o=t.techGap,s=t.min,c=t.max,h=this.yAxisType();switch(h){case L:var l=(this._chartStore.visibleDataList()[0]||{}).data||{};w(l.close)&&(s=(s-l.close)/l.close*100,c=(c-l.close)/l.close*100),e=.01;break;case R:s=it(s),c=it(c),e=.05*nt(-n);break;default:e=nt(-n)}if(s===c||e>Math.abs(s-c)){var u=r===s,f=a===c;s=u?s:f?s-8*e:s-4*e,c=f?c:u?c+8*e:c+4*e}var d,v=.2;w((i=this._isCandleYAxis?this._chartStore.styleOptions().candle.margin:o?{top:0,bottom:0}:this._chartStore.styleOptions().technicalIndicator.margin).top)&&(d=1>i.top?i.top:i.top/this._height,v=o?Math.max(o.top,d):d);var p,_=.1;w(i.bottom)&&(p=1>i.bottom?i.bottom:i.bottom/this._height,_=o?Math.max(o.bottom,p):p);var m,y,x,g=Math.abs(c-s);return g=Math.abs((c+=g*v)-(s-=g*_)),h===R?(m=nt(s),y=nt(c),x=Math.abs(y-m)):(m=s,y=c,x=g),{min:s,max:c,range:g,realMin:m,realMax:y,realRange:x}}},{key:"_optimalTicks",value:function(t){var e=this,i=[],n=this.yAxisType(),r=this._chartStore.technicalIndicatorStore().instances(this._paneId),a=0,o=!1;this._isCandleYAxis?a=this._chartStore.pricePrecision():r.forEach(function(t){a=Math.max(a,t.precision),o||(o=t.shouldFormatBigNumber)});var s,c=this._chartStore.styleOptions().xAxis.tickText.size;return t.forEach(function(t){var r,h=t.v,l=e._innerConvertToPixel(+h);switch(n){case L:r="".concat(J(h,2),"%");break;case R:l=e._innerConvertToPixel(it(h)),r=J(h,a);break;default:r=J(h,a),o&&(r=Q(r))}l>c&&e._height-c>l&&(s&&Math.abs(s-l)>2*c||!s)&&(i.push({v:r,y:l}),s=l)}),i}},{key:"_innerConvertToPixel",value:function(t){var e=(t-this._minValue)/this._range;return this.isReverse()?Math.round(e*this._height):Math.round((1-e)*this._height)}},{key:"isCandleYAxis",value:function(){return this._isCandleYAxis}},{key:"yAxisType",value:function(){return this._isCandleYAxis?this._chartStore.styleOptions().yAxis.type:O}},{key:"isReverse",value:function(){return this._isCandleYAxis&&this._chartStore.styleOptions().yAxis.reverse}},{key:"isFromYAxisZero",value:function(){var t=this._chartStore.styleOptions().yAxis;return t.position===M&&t.inside||t.position===D&&!t.inside}},{key:"getSelfWidth",value:function(){var t=this,e=this._chartStore.styleOptions(),i=e.yAxis,n=i.width;if(w(n))return n;var r=0;if(i.show&&(i.axisLine.show&&(r+=i.axisLine.size),i.tickLine.show&&(r+=i.tickLine.length),i.tickText.show)){var a=0;this._measureCtx.font=Wt(i.tickText.size,i.tickText.weight,i.tickText.family),this._ticks.forEach(function(e){a=Math.max(a,Yt(t._measureCtx,e.v))}),r+=i.tickText.paddingLeft+i.tickText.paddingRight+a}var o=e.crosshair,s=0;if(o.show&&o.horizontal.show&&o.horizontal.text.show){var c=0,h=!1;this._chartStore.technicalIndicatorStore().instances(this._paneId).forEach(function(t){c=Math.max(t.precision,c),h||(h=t.shouldFormatBigNumber)}),this._measureCtx.font=Wt(o.horizontal.text.size,o.horizontal.text.weight,o.horizontal.text.family);var l=2;if(this.yAxisType()!==L)if(this._isCandleYAxis){var u=this._chartStore.pricePrecision(),f=e.technicalIndicator.lastValueMark;l=f.show&&f.text.show?Math.max(c,u):u}else l=c;var d=J(this._maxValue,l);h&&(d=Q(d)),s+=o.horizontal.text.paddingLeft+o.horizontal.text.paddingRight+2*o.horizontal.text.borderSize+Yt(this._measureCtx,d)}return Math.max(r,s)}},{key:"convertFromPixel",value:function(t){var e=(this.isReverse()?t/this._height:1-t/this._height)*this._range+this._minValue;switch(this.yAxisType()){case L:var i=(this._chartStore.visibleDataList()[0]||{}).data||{};if(w(i.close))return i.close*e/100+i.close;break;case R:return nt(e);default:return e}}},{key:"convertToPixel",value:function(t){var e;switch(this.yAxisType()){case L:var i=(this._chartStore.visibleDataList()[0]||{}).data||{};w(i.close)&&(e=(t-i.close)/i.close*100);break;case R:e=it(t);break;default:e=t}return this._innerConvertToPixel(e)}},{key:"convertToNicePixel",value:function(t){var e=this.convertToPixel(t);return Math.round(Math.max(.05*this._height,Math.min(e,.98*this._height)))}}]),i}();var Be=function(t){ct(i,pe);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t){var n;return v(this,i),(n=e.call(this,t))._minHeight=30,n._initHeight(t),n}return _(i,[{key:"_initBefore",value:function(t){this._id=t.id,this._yAxis=this._createYAxis(t)}},{key:"_initHeight",value:function(t){var e=t.height,i=t.minHeight;if(b(i)&&this.setMinHeight(i),b(e)){var n=this.minHeight();this.setHeight(n>e?n:e)}}},{key:"_createYAxis",value:function(t){return new Re(t.chartStore,!1,t.id)}},{key:"_createMainWidget",value:function(t,e){return new be({container:t,chartStore:e.chartStore,xAxis:e.xAxis,yAxis:this._yAxis,paneId:e.id})}},{key:"_createYAxisWidget",value:function(t,e){return new Oe({container:t,chartStore:e.chartStore,yAxis:this._yAxis,paneId:e.id})}},{key:"minHeight",value:function(){return this._minHeight}},{key:"setMinHeight",value:function(t){this._minHeight=t}},{key:"setHeight",value:function(t){de(lt(i.prototype),"setHeight",this).call(this,t),this._yAxis.setHeight(t)}},{key:"setWidth",value:function(t,e){de(lt(i.prototype),"setWidth",this).call(this,t,e),this._yAxis.setWidth(e)}},{key:"id",value:function(){return this._id}},{key:"yAxis",value:function(){return this._yAxis}}]),i}();var Fe=function(t){ct(i,Se);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(){return v(this,i),e.apply(this,arguments)}return _(i,[{key:"_drawContent",value:function(){var t=this._chartStore.styleOptions().candle;this._drawLastPriceLine(t.priceMark),t.type===F?this._drawArea(t):(this._drawHighLowPrice(t.priceMark),this._drawCandle(t)),this._drawTechs(),this._drawGrid()}},{key:"_drawArea",value:function(t){var e=this,i=[],n=[],r=Number.MAX_SAFE_INTEGER,a=t.area;this._drawGraphics(function(t,o,s,c,h,l){var u=s[a.value];if(w(u)){var f=e._yAxis.convertToPixel(u);if(0===l){var d=t-c;n.push({x:d,y:e._height}),n.push({x:d,y:f}),i.push({x:d,y:f})}i.push({x:t,y:f}),n.push({x:t,y:f}),r=Math.min(r,f)}},function(){var t=n.length;if(t>0){var o=n[t-1],s=e._chartStore.timeScaleStore().halfBarSpace(),c=o.x+s;i.push({x:c,y:o.y}),n.push({x:c,y:o.y}),n.push({x:c,y:e._height})}if(i.length>0&&(e._ctx.lineWidth=a.lineSize,e._ctx.strokeStyle=a.lineColor,jt(e._ctx,i)),n.length>0){var h=a.backgroundColor;if(g(h)){var l=e._ctx.createLinearGradient(0,e._height,0,r);try{h.forEach(function(t){l.addColorStop(t.offset,t.color)})}catch(t){}e._ctx.fillStyle=l}else e._ctx.fillStyle=h;zt(e._ctx,n)}})}},{key:"_drawCandle",value:function(t){var e=this;this._drawGraphics(function(i,n,r,a,o){e._drawCandleBar(i,a,o,r,t.bar,t.type)})}},{key:"_drawHighLowPrice",value:function(t){if(t.show&&(t.high.show||t.low.show)){var e={price:Number.MIN_SAFE_INTEGER,pos:-1},i={price:Number.MAX_SAFE_INTEGER,pos:-1};this._chartStore.visibleDataList().forEach(function(t){var n=t.index,r=t.data,a=K(r,"high",Number.MIN_SAFE_INTEGER);a>e.price&&(e.price=a,e.pos=n);var o=K(r,"low",Number.MAX_SAFE_INTEGER);i.price>o&&(i.price=o,i.pos=n)});var n=this._yAxis.convertToPixel(e.price);e.y=n;var r=this._yAxis.convertToPixel(i.price);i.y=r;var a=[],o=[];r>n?(a=[-2,-5],o=[2,5]):(a=[2,5],o=[-2,-5]);var s=this._chartStore.pricePrecision();this._ctx.textAlign="left",this._ctx.lineWidth=1,this._ctx.textBaseline="middle",this._drawRealHighLowPrice(t.high,a,s,e),this._drawRealHighLowPrice(t.low,o,s,i)}}},{key:"_drawRealHighLowPrice",value:function(t,e,i,n){if(t.show){var r,a,o=n.price,s=n.y,c=this._xAxis.convertToPixel(n.pos),h=s+e[0];this._ctx.strokeStyle=t.color,this._ctx.fillStyle=t.color,jt(this._ctx,[{x:c-2,y:h+e[0]},{x:c,y:h},{x:c+2,y:h+e[0]}]),c>this._width/2?(a=(r=c-5)-t.textMargin,this._ctx.textAlign="right"):(r=c+5,this._ctx.textAlign="left",a=r+t.textMargin);var l=h+e[1];jt(this._ctx,[{x:c,y:h},{x:c,y:l},{x:r,y:l}]),this._ctx.font=Wt(t.textSize,t.textWeight,t.textFamily);var u=J(o,i);this._ctx.fillText(u,a,l)}}},{key:"_drawLastPriceLine",value:function(t){var e=t.last;if(t.show&&e.show&&e.line.show){var i=this._chartStore.dataList(),n=i[i.length-1];if(n){var r,a=n.close,o=n.open,s=this._yAxis.convertToNicePixel(a);r=a>o?e.upColor:o>a?e.downColor:e.noChangeColor,this._ctx.save(),this._ctx.strokeStyle=r,this._ctx.lineWidth=e.line.size,e.line.style===I&&this._ctx.setLineDash(e.line.dashValue),Vt(this._ctx,s,0,this._width),this._ctx.restore()}}}}]),i}();var ze=function(t){ct(i,we);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(){return v(this,i),e.apply(this,arguments)}return _(i,[{key:"_drawTooltip",value:function(t,e){var i=this._chartStore.styleOptions(),n=i.candle,r=n.tooltip,a=i.technicalIndicator,o=a.tooltip,s=this._shouldDrawTooltip(t,r),c=this._shouldDrawTooltip(t,o);if(r.showType===V&&o.showType===V)this._drawCandleTooltipWithRect(t,e,n,s,a,0,c);else if(r.showType===H){var h=this._drawCandleTooltipWithStandard(t.kLineData,n,s);o.showType===H?this._drawBatchTechToolTip(t,e,a,s?h+r.text.marginTop:0,c):this._drawCandleTooltipWithRect(t,e,n,!1,a,h,c)}else{var l=this._drawBatchTechToolTip(t,e,a,0,c);this._drawCandleTooltipWithRect(t,e,n,s,a,l,!1)}}},{key:"_drawCandleTooltipWithStandard",value:function(t,e,i){var n=this;if(!i)return 0;var r=this._getCandleTooltipData(t,e),a=e.tooltip,o=a.text.marginLeft,s=a.text.marginRight,c=a.text.size,h=a.text.color,l=a.labels;this._ctx.textBaseline="top",this._ctx.font=Wt(c,a.text.weight,a.text.family);var u=o,f=a.text.marginTop,d=c;return l.forEach(function(t,e){var i,l,v=Yt(n._ctx,t),p=r[e]||a.defaultValue;k(p)?(i=p.value||a.defaultValue,l=p.color||h):(l=h,i=p);var _=Yt(n._ctx,i);u+v+_>n._width&&(u=o,d+=c+1,f+=c+1),ke(n._ctx,h,u,f,t),ke(n._ctx,l,u+v,f,i),u+=v+_+o+s}),d}},{key:"_drawCandleTooltipWithRect",value:function(t,e,i,n,r,a,o){var s=this;if(n||o){var c=i.tooltip,h=c.labels,l=this._getCandleTooltipData(t.kLineData,i),u=c.text.marginLeft,f=c.text.marginRight,d=c.text.marginTop,v=c.text.marginBottom,p=c.text.size,_=c.text.color,m=c.rect,y=m.borderSize,x=m.paddingLeft,g=m.paddingRight,S=m.paddingTop,w=m.paddingBottom,E=m.offsetLeft,P=m.offsetRight,C=0,A=0,I=0;this._ctx.save(),this._ctx.textBaseline="top",n&&(this._ctx.font=Wt(p,c.text.weight,c.text.family),h.forEach(function(t,e){var i,n=l[e];i=k(n)?n.value||c.defaultValue:n;var r="".concat(t).concat(i),a=Yt(s._ctx,r)+u+f;C=Math.max(C,a)}),I+=(v+d+p)*h.length);var T=r.tooltip,O=T.text.marginLeft,L=T.text.marginRight,R=T.text.marginTop,B=T.text.marginBottom,F=T.text.size,z=[];if(e.forEach(function(e){z.push(s._getTechTooltipData(t,e,r))}),o&&(this._ctx.font=Wt(F,T.text.weight,T.text.family),z.forEach(function(t){t.values.forEach(function(t){var e=t.title,i=t.value;if(b(e)){var n="".concat(e).concat(i),r=Yt(s._ctx,n)+O+L;C=Math.max(C,r),I+=R+B+F}})})),0!==(A+=C)&&0!==I){A+=2*y+x+g,I+=2*y+S+w;var V,H=this._chartStore.styleOptions();this._width/2>t.realX?(V=this._width-P-A,H.yAxis.inside&&H.yAxis.position===D&&(V-=this._yAxis.width())):(V=E,H.yAxis.inside&&H.yAxis.position===M&&(V+=this._yAxis.width()));var j=a+m.offsetTop,N=m.borderRadius;Ce(this._ctx,m.backgroundColor,V,j,A,I,N),Pe(this._ctx,m.borderColor,y,V,j,A,I,N);var Y=V+y+x+u,W=j+y+S;if(n&&(this._ctx.font=Wt(p,c.text.weight,c.text.family),h.forEach(function(t,e){W+=d,s._ctx.textAlign="left",ke(s._ctx,_,Y,W,t);var i,n,r=l[e];k(r)?(n=r.color||_,i=r.value||c.defaultValue):(n=_,i=r||c.defaultValue),s._ctx.textAlign="right",ke(s._ctx,n,V+A-y-f-g,W,i),W+=p+v})),o){var X=V+y+x+O;this._ctx.font=Wt(F,T.text.weight,T.text.family),z.forEach(function(t){t.values.forEach(function(t){W+=R,s._ctx.textAlign="left",s._ctx.fillStyle=t.color||T.text.color,s._ctx.fillText(t.title,X,W),s._ctx.textAlign="right",s._ctx.fillText(t.value,V+A-y-L-g,W),W+=F+B})})}this._ctx.restore()}}}},{key:"_getCandleTooltipData",value:function(t,e){var i=this,n=e.tooltip.values,r=[];if(n)S(n)?r=n(t,e)||[]:g(n)&&(r=n);else{var a=this._chartStore.pricePrecision(),o=this._chartStore.volumePrecision();(r=[K(t,"timestamp"),K(t,"open"),K(t,"close"),K(t,"high"),K(t,"low"),K(t,"volume")]).forEach(function(t,e){switch(e){case 0:r[e]=q(i._chartStore.timeScaleStore().dateTimeFormat(),t,"YYYY-MM-DD hh:mm");break;case r.length-1:r[e]=Q(J(t,o));break;default:r[e]=J(t,a)}})}return r}}]),i}();var Ve=function(t){ct(i,be);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(){return v(this,i),e.apply(this,arguments)}return _(i,[{key:"_createMainView",value:function(t,e){return new Fe(t,e.chartStore,e.xAxis,e.yAxis,e.paneId)}},{key:"_createOverlayView",value:function(t,e){return new ze(t,e.chartStore,e.xAxis,e.yAxis,e.paneId)}}]),i}();var He=function(t){ct(i,Be);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(){return v(this,i),e.apply(this,arguments)}return _(i,[{key:"_createYAxis",value:function(t){return new Re(t.chartStore,!0,t.id)}},{key:"_createMainWidget",value:function(t,e){return new Ve({container:t,chartStore:e.chartStore,xAxis:e.xAxis,yAxis:this._yAxis,paneId:e.id})}}]),i}();var je=function(t){ct(i,ge);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t,n,r){var a;return v(this,i),(a=e.call(this,t,n))._xAxis=r,a}return _(i,[{key:"_draw",value:function(){var t=this._chartStore.styleOptions().xAxis;t.show&&(this._drawAxisLine(t),this._drawTickLines(t),this._drawTickLabels(t))}},{key:"_drawAxisLine",value:function(t){var e=t.axisLine;e.show&&(this._ctx.strokeStyle=e.color,this._ctx.lineWidth=e.size,Vt(this._ctx,0,0,this._width))}},{key:"_drawTickLines",value:function(t){var e=this,i=t.tickLine;if(i.show){this._ctx.lineWidth=i.size,this._ctx.strokeStyle=i.color;var n=t.axisLine.show?t.axisLine.size:0,r=n+i.length;this._xAxis.ticks().forEach(function(t){Ht(e._ctx,t.x,n,r)})}}},{key:"_drawTickLabels",value:function(t){var e=t.tickText;if(e.show){var i=t.tickLine;this._ctx.textBaseline="top",this._ctx.font=Wt(e.size,e.weight,e.family),this._ctx.textAlign="center",this._ctx.fillStyle=e.color;var n=e.paddingTop;t.axisLine.show&&(n+=t.axisLine.size),i.show&&(n+=i.length);for(var r=this._xAxis.ticks(),a=r.length,o=0;a>o;o++)this._ctx.fillText(r[o].v,r[o].x,n)}}}]),i}();var Ne=function(t){ct(i,ge);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t,n,r){var a;return v(this,i),(a=e.call(this,t,n))._xAxis=r,a}return _(i,[{key:"_draw",value:function(){this._drawCrosshairLabel()}},{key:"_drawCrosshairLabel",value:function(){var t=this._chartStore.crosshairStore().get();if(t.paneId){var e=this._chartStore.styleOptions().crosshair,i=e.vertical,n=i.text;if(e.show&&i.show&&n.show&&t.dataIndex===t.realDataIndex){var r=t.kLineData.timestamp,a=q(this._chartStore.timeScaleStore().dateTimeFormat(),r,"YYYY-MM-DD hh:mm"),o=n.paddingLeft,s=n.paddingRight,c=n.paddingTop,h=n.borderSize,l=Xt(this._ctx,a,n),u=$t(n),f=l-2*h-o-s,d=t.realX-f/2;o+h>d?d=o+h:d>this._width-f-h-s&&(d=this._width-f-h-s),Ee(this._ctx,n.backgroundColor,n.borderColor,h,d-h-o,0,l,u,n.borderRadius),this._ctx.textBaseline="top",ke(this._ctx,n.color,d,h+c,a)}}}}]),i}();var Ye=function(t){ct(i,me);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(){return v(this,i),e.apply(this,arguments)}return _(i,[{key:"_createMainView",value:function(t,e){return new je(t,e.chartStore,e.xAxis)}},{key:"_createOverlayView",value:function(t,e){return new Ne(t,e.chartStore,e.xAxis)}}]),i}();var We=function(t){ct(i,Le);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(){return v(this,i),e.apply(this,arguments)}return _(i,[{key:"_computeMinMax",value:function(){return{min:this._chartStore.timeScaleStore().from(),max:this._chartStore.timeScaleStore().to()-1}}},{key:"_optimalMinMax",value:function(t){var e=t.min,i=t.max,n=i-e+1;return{min:e,max:i,range:n,realMin:e,realMax:i,realRange:n}}},{key:"_optimalTicks",value:function(t){var e=[],i=t.length,n=this._chartStore.dataList();if(i>0){var r=this._chartStore.timeScaleStore().dateTimeFormat(),a=this._chartStore.styleOptions().xAxis.tickText;this._measureCtx.font=Wt(a.size,a.weight,a.family);var o=Yt(this._measureCtx,"00-00 00:00"),s=this.convertToPixel(parseInt(t[0].v,10)),c=1;if(i>1){var h=this.convertToPixel(parseInt(t[1].v,10)),l=Math.abs(h-s);o>l&&(c=Math.ceil(o/l))}for(var u=0;i>u;u+=c){var f=parseInt(t[u].v,10),d=n[f].timestamp,v=q(r,d,"hh:mm");0!==u&&(v=this._optimalTickLabel(r,d,n[parseInt(t[u-c].v,10)].timestamp)||v);var p=this.convertToPixel(f);e.push({v:v,x:p,oV:d})}if(1===e.length)e[0].v=q(r,e[0].oV,"YYYY-MM-DD hh:mm");else{var _=e[0].oV,m=e[1].oV;if(e[2]){var y=e[2].v;/^[0-9]{2}-[0-9]{2}$/.test(y)?e[0].v=q(r,_,"MM-DD"):/^[0-9]{4}-[0-9]{2}$/.test(y)?e[0].v=q(r,_,"YYYY-MM"):/^[0-9]{4}$/.test(y)&&(e[0].v=q(r,_,"YYYY"))}else e[0].v=this._optimalTickLabel(r,_,m)||e[0].v}}return e}},{key:"_optimalTickLabel",value:function(t,e,i){var n=q(t,e,"YYYY"),r=q(t,e,"YYYY-MM"),a=q(t,e,"MM-DD");return n!==q(t,i,"YYYY")?n:r!==q(t,i,"YYYY-MM")?r:a!==q(t,i,"MM-DD")?a:null}},{key:"getSelfHeight",value:function(){var t=this._chartStore.styleOptions(),e=t.xAxis,i=e.height;if(w(i))return i;var n=t.crosshair,r=0;e.show&&(e.axisLine.show&&(r+=e.axisLine.size),e.tickLine.show&&(r+=e.tickLine.length),e.tickText.show&&(r+=e.tickText.paddingTop+e.tickText.paddingBottom+e.tickText.size));var a=0;return n.show&&n.vertical.show&&n.vertical.text.show&&(a+=n.vertical.text.paddingTop+n.vertical.text.paddingBottom+2*n.vertical.text.borderSize+n.vertical.text.size),Math.max(r,a)}},{key:"convertFromPixel",value:function(t){return this._chartStore.timeScaleStore().coordinateToDataIndex(t)}},{key:"convertToPixel",value:function(t){return this._chartStore.timeScaleStore().dataIndexToCoordinate(t)}}]),i}();var Xe=function(t){ct(i,pe);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(){return v(this,i),e.apply(this,arguments)}return _(i,[{key:"_initBefore",value:function(){this._xAxis=new We(this._chartStore)}},{key:"_createMainWidget",value:function(t,e){return new Ye({container:t,chartStore:e.chartStore,xAxis:this._xAxis})}},{key:"xAxis",value:function(){return this._xAxis}},{key:"setWidth",value:function(t,e){de(lt(i.prototype),"setWidth",this).call(this,t,e),this._xAxis.setWidth(t)}},{key:"setHeight",value:function(t){de(lt(i.prototype),"setHeight",this).call(this,t),this._xAxis.setHeight(t)}}]),i}(),$e=new(function(){function t(){v(this,t),this._baseId=1}return _(t,[{key:"next",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=(new Date).getTime();return e===this._prevIdTimestamp?++this._baseId:this._baseId=1,this._prevIdTimestamp=e,"".concat(t).concat(e,"_").concat(this._baseId)}}]),t}());function Ge(t){return $e.next(t)}var Ue="mouse",Ze="touch";function Ke(t){return t.type===Ze}function qe(t){return t.type===Ue}function Je(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}function Qe(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Je(Object(i),!0).forEach(function(e){wt(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Je(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function ti(t){return t.getBoundingClientRect()||{left:0,top:0}}function ei(t){return!!t.touches}function ii(t){t.cancelable&&t.preventDefault()}function ni(t,e){var i=t.clientX-e.clientX,n=t.clientY-e.clientY;return Math.sqrt(i*i+n*n)}var ri=function(){function t(e,i,n){v(this,t),this._target=e,this._handler=i,this._options=n,this._clickCount=0,this._clickTimeoutId=null,this._longTapTimeoutId=null,this._longTapActive=!1,this._mouseMoveStartPosition=null,this._moveExceededManhattanDistance=!1,this._cancelClick=!1,this._unsubscribeOutsideEvents=null,this._unsubscribeMousemove=null,this._unsubscribeRoot=null,this._startPinchMiddleCoordinate=null,this._startPinchDistance=0,this._pinchPrevented=!1,this._preventDragProcess=!1,this._mousePressed=!1,this._init()}return _(t,[{key:"setOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._options=Qe(Qe({},this.options),t)}},{key:"destroy",value:function(){null!==this._unsubscribeOutsideEvents&&(this._unsubscribeOutsideEvents(),this._unsubscribeOutsideEvents=null),null!==this._unsubscribeMousemove&&(this._unsubscribeMousemove(),this._unsubscribeMousemove=null),null!==this._unsubscribeRoot&&(this._unsubscribeRoot(),this._unsubscribeRoot=null),this._clearLongTapTimeout(),this._resetClickTimeout()}},{key:"_mouseEnterHandler",value:function(t){var e=this;this._unsubscribeMousemove&&this._unsubscribeMousemove();var i=this._mouseMoveHandler.bind(this),n=this._mouseWheelHandler.bind(this);this._unsubscribeMousemove=function(){e._target.removeEventListener("mousemove",i),e._target.removeEventListener("wheel",n)},this._target.addEventListener("mousemove",i),this._target.addEventListener("wheel",n,{passive:!1}),ei(t)&&this._mouseMoveHandler(t);var r=this._makeCompatEvent(t);this._processEvent(r,this._handler.mouseEnterEvent)}},{key:"_resetClickTimeout",value:function(){null!==this._clickTimeoutId&&clearTimeout(this._clickTimeoutId),this._clickCount=0,this._clickTimeoutId=null}},{key:"_mouseMoveHandler",value:function(t){if(!this._mousePressed||ei(t)){var e=this._makeCompatEvent(t);this._processEvent(e,this._handler.mouseMoveEvent)}}},{key:"_mouseWheelHandler",value:function(t){var e=this._makeCompatEvent(t);t.localX=e.localX,t.localY=e.localY,this._processEvent(t,this._handler.mouseWheelEvent)}},{key:"_mouseMoveWithDownHandler",value:function(t){if(!("button"in t&&0!==t.button||null!==this._startPinchMiddleCoordinate)){var e=ei(t);if(!this._preventDragProcess||!e){this._pinchPrevented=!0;var i=this._makeCompatEvent(t),n=this._mouseMoveStartPosition,r=Math.abs(n.x-i.pageX),a=Math.abs(n.y-i.pageY),o=r+a>5;if(o||!e){if(o&&!this._moveExceededManhattanDistance&&e){var s=.5*r;a>=s&&!this._options.treatVertTouchDragAsPageScroll||s>a&&!this._options.treatHorzTouchDragAsPageScroll||(this._preventDragProcess=!0)}o&&(this._moveExceededManhattanDistance=!0,this._cancelClick=!0,e&&this._clearLongTapTimeout()),this._preventDragProcess||(this._processEvent(i,this._handler.pressedMouseMoveEvent),e&&ii(t))}}}}},{key:"_mouseUpHandler",value:function(t){if(!("button"in t)||0===t.button){var e=this._makeCompatEvent(t);this._clearLongTapTimeout(),this._mouseMoveStartPosition=null,this._mousePressed=!1,this._unsubscribeRoot&&(this._unsubscribeRoot(),this._unsubscribeRoot=null),ei(t)&&this._mouseLeaveHandler(t),this._processEvent(e,this._handler.mouseUpEvent),++this._clickCount,this._clickTimeoutId&&this._clickCount>1?(this._processEvent(e,this._handler.mouseDoubleClickEvent),this._resetClickTimeout()):this._cancelClick||this._processEvent(e,this._handler.mouseClickEvent),ei(t)&&(ii(t),this._mouseLeaveHandler(t),0===t.touches.length&&(this._longTapActive=!1))}}},{key:"_clearLongTapTimeout",value:function(){null!==this._longTapTimeoutId&&(clearTimeout(this._longTapTimeoutId),this._longTapTimeoutId=null)}},{key:"_mouseDownHandler",value:function(t){if(!("button"in t)||0===t.button||2===t.button){var e=this._makeCompatEvent(t);if("button"in t&&2===t.button)this._processEvent(e,this._handler.mouseRightDownEvent);else{this._cancelClick=!1,this._moveExceededManhattanDistance=!1,this._preventDragProcess=!1,ei(t)&&this._mouseEnterHandler(t),this._mouseMoveStartPosition={x:e.pageX,y:e.pageY},this._unsubscribeRoot&&(this._unsubscribeRoot(),this._unsubscribeRoot=null);var i=this._mouseMoveWithDownHandler.bind(this),n=this._mouseUpHandler.bind(this),r=this._target.ownerDocument.documentElement;this._unsubscribeRoot=function(){r.removeEventListener("touchmove",i),r.removeEventListener("touchend",n),r.removeEventListener("mousemove",i),r.removeEventListener("mouseup",n)},r.addEventListener("touchmove",i,{passive:!1}),r.addEventListener("touchend",n,{passive:!1}),this._clearLongTapTimeout(),ei(t)&&1===t.touches.length?this._longTapTimeoutId=setTimeout(this._longTapHandler.bind(this,t),600):(r.addEventListener("mousemove",i),r.addEventListener("mouseup",n)),this._mousePressed=!0,this._processEvent(e,this._handler.mouseDownEvent),this._clickTimeoutId||(this._clickCount=0,this._clickTimeoutId=setTimeout(this._resetClickTimeout.bind(this),500))}}}},{key:"_init",value:function(){var t=this;this._target.addEventListener("mouseenter",this._mouseEnterHandler.bind(this)),this._target.addEventListener("touchcancel",this._clearLongTapTimeout.bind(this));var e,i=this._target.ownerDocument,n=function(e){t._handler.mouseDownOutsideEvent&&(e.target&&t._target.contains(e.target)||t._handler.mouseDownOutsideEvent())};this._unsubscribeOutsideEvents=function(){i.removeEventListener("mousedown",n),i.removeEventListener("touchstart",n)},i.addEventListener("mousedown",n),i.addEventListener("touchstart",n,{passive:!0}),this._target.addEventListener("mouseleave",this._mouseLeaveHandler.bind(this)),this._target.addEventListener("touchstart",this._mouseDownHandler.bind(this),{passive:!0}),e="ontouchstart"in window||!!(window.DocumentTouch&&document instanceof window.DocumentTouch),"onorientationchange"in window&&(navigator.maxTouchPoints||navigator.msMaxTouchPoints||e)||this._target.addEventListener("mousedown",this._mouseDownHandler.bind(this)),this._initPinch(),this._target.addEventListener("touchmove",function(){},{passive:!1})}},{key:"_initPinch",value:function(){var t=this;void 0===this._handler.pinchStartEvent&&void 0===this._handler.pinchEvent&&void 0===this._handler.pinchEndEvent||(this._target.addEventListener("touchstart",function(e){return t._checkPinchState(e.touches)},{passive:!0}),this._target.addEventListener("touchmove",function(e){if(2===e.touches.length&&null!==t._startPinchMiddleCoordinate&&void 0!==t._handler.pinchEvent){var i=ni(e.touches[0],e.touches[1]);t._handler.pinchEvent(t._startPinchMiddleCoordinate,i/t._startPinchDistance),ii(e)}},{passive:!1}),this._target.addEventListener("touchend",function(e){t._checkPinchState(e.touches)}))}},{key:"_checkPinchState",value:function(t){1===t.length&&(this._pinchPrevented=!1),2!==t.length||this._pinchPrevented||this._longTapActive?this._stopPinch():this._startPinch(t)}},{key:"_startPinch",value:function(t){var e=ti(this._target);this._startPinchMiddleCoordinate={x:(t[0].clientX-e.left+(t[1].clientX-e.left))/2,y:(t[0].clientY-e.top+(t[1].clientY-e.top))/2},this._startPinchDistance=ni(t[0],t[1]),void 0!==this._handler.pinchStartEvent&&this._handler.pinchStartEvent(),this._clearLongTapTimeout()}},{key:"_stopPinch",value:function(){null!==this._startPinchMiddleCoordinate&&(this._startPinchMiddleCoordinate=null,void 0!==this._handler.pinchEndEvent&&this._handler.pinchEndEvent())}},{key:"_mouseLeaveHandler",value:function(t){this._unsubscribeMousemove&&this._unsubscribeMousemove();var e=this._makeCompatEvent(t);this._processEvent(e,this._handler.mouseLeaveEvent)}},{key:"_longTapHandler",value:function(t){var e=this._makeCompatEvent(t);this._processEvent(e,this._handler.longTapEvent),this._cancelClick=!0,this._longTapActive=!0}},{key:"_processEvent",value:function(t,e){e&&e.call(this._handler,t)}},{key:"_makeCompatEvent",value:function(t){var e;e="touches"in t&&t.touches.length?t.touches[0]:"changedTouches"in t&&t.changedTouches.length?t.changedTouches[0]:t;var i=ti(this._target);return{clientX:e.clientX,clientY:e.clientY,pageX:e.pageX,pageY:e.pageY,screenX:e.screenX,screenY:e.screenY,localX:e.clientX-i.left,localY:e.clientY-i.top,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey,metaKey:t.metaKey,type:t.type.startsWith("mouse")?Ue:Ze,target:e.target,view:t.view}}}]),t}(),ai=function(){function t(e,i,n,r,a,o){v(this,t),this._chartStore=i,this._topPaneId=n,this._bottomPaneId=r,this._dragEnabled=a,this._width=0,this._offsetLeft=0,this._dragEventHandler=o,this._dragFlag=!1,this._initElement(e),this._initEvent(a)}return _(t,[{key:"_initElement",value:function(t){this._container=t,this._wrapper=ve("div",{margin:"0",padding:"0",position:"relative",boxSizing:"border-box"}),this._element=ve("div",{width:"100%",height:"7px",margin:"0",padding:"0",position:"absolute",top:"-3px",zIndex:"20",boxSizing:"border-box"}),this._wrapper.appendChild(this._element);var e=t.lastChild;e?t.insertBefore(this._wrapper,e):t.appendChild(this._wrapper)}},{key:"_initEvent",value:function(t){t&&(this._element.style.cursor="ns-resize",this._dragEvent=new ri(this._element,{mouseDownEvent:this._mouseDownEvent.bind(this),mouseUpEvent:this._mouseUpEvent.bind(this),pressedMouseMoveEvent:this._pressedMouseMoveEvent.bind(this),mouseEnterEvent:this._mouseEnterEvent.bind(this),mouseLeaveEvent:this._mouseLeaveEvent.bind(this)},{treatVertTouchDragAsPageScroll:!1,treatHorzTouchDragAsPageScroll:!0}))}},{key:"_mouseDownEvent",value:function(t){this._dragFlag=!0,this._startY=t.pageY,this._dragEventHandler.startDrag(this._topPaneId,this._bottomPaneId)}},{key:"_mouseUpEvent",value:function(){this._dragFlag=!1,this._chartStore.setDragPaneFlag(!1)}},{key:"_pressedMouseMoveEvent",value:function(t){this._dragEventHandler.drag(t.pageY-this._startY,this._topPaneId,this._bottomPaneId),this._chartStore.setDragPaneFlag(!0),this._chartStore.crosshairStore().set()}},{key:"_mouseEnterEvent",value:function(){var t=this._chartStore.styleOptions().separator;this._element.style.background=t.activeBackgroundColor,this._chartStore.crosshairStore().set()}},{key:"_mouseLeaveEvent",value:function(){this._dragFlag||(this._element.style.background=null,this._chartStore.setDragPaneFlag(!1))}},{key:"height",value:function(){return this._wrapper.offsetHeight}},{key:"setSize",value:function(t,e){this._offsetLeft=t,this._width=e,this.invalidate()}},{key:"setDragEnabled",value:function(t){t!==this._dragEnabled&&(this._dragEnabled=t,t?!this._dragEvent&&this._initEvent(t):(this._element.style.cursor="default",this._dragEvent&&this._dragEvent.destroy(),this._dragEvent=null))}},{key:"topPaneId",value:function(){return this._topPaneId}},{key:"bottomPaneId",value:function(){return this._bottomPaneId}},{key:"updatePaneId",value:function(t,e){b(t)&&(this._topPaneId=t),b(e)&&(this._bottomPaneId=e)}},{key:"invalidate",value:function(){var t=this._chartStore.styleOptions().separator;this._element.style.top="".concat(-Math.floor((7-t.size)/2),"px"),this._wrapper.style.backgroundColor=t.color,this._wrapper.style.height="".concat(t.size,"px"),this._wrapper.style.marginLeft="".concat(t.fill?0:this._offsetLeft,"px"),this._wrapper.style.width=t.fill?"100%":"".concat(this._width,"px")}},{key:"getImage",value:function(){var t=this._chartStore.styleOptions().separator,e=this._wrapper.offsetWidth,i=t.size,n=ve("canvas",{width:"".concat(e,"px"),height:"".concat(i,"px"),boxSizing:"border-box"}),r=n.getContext("2d"),a=Nt(n);return n.width=e*a,n.height=i*a,r.scale(a,a),r.fillStyle=t.color,r.fillRect(this._offsetLeft,0,e,i),n}},{key:"destroy",value:function(){this._dragEvent&&this._dragEvent.destroy(),this._container.removeChild(this._wrapper)}}]),t}(),oi=_(function t(e){v(this,t),this._chartStore=e});var si=function(t){ct(i,oi);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t){var n;return v(this,i),(n=e.call(this,t))._flingStartTime=null,n._flingScrollTimerId=null,n._startScrollCoordinate=null,n._touchCoordinate=null,n._touchCancelCrosshair=!1,n._touchZoomed=!1,n._pinchScale=1,n}return _(i,[{key:"pinchStartEvent",value:function(){this._pinchScale=1,this._touchZoomed=!0}},{key:"pinchEvent",value:function(t,e){var i=5*(e-this._pinchScale);this._pinchScale=e,this._chartStore.timeScaleStore().zoom(i,t)}},{key:"mouseUpEvent",value:function(){this._startScrollCoordinate=null}},{key:"mouseLeaveEvent",value:function(t){var e=this;if(Ke(t)){if(this._startScrollCoordinate){var i=(new Date).getTime()-this._flingStartTime,n=(t.localX-this._startScrollCoordinate.x)/(i>0?i:1)*20;200>i&&Math.abs(n)>0&&function t(){e._flingScrollTimerId=ye(function(){e._chartStore.timeScaleStore().startScroll(),e._chartStore.timeScaleStore().scroll(n),1>Math.abs(n*=.975)?e._flingScrollTimerId&&(xe(e._flingScrollTimerId),e._flingScrollTimerId=null):t()})}()}}else this._startScrollCoordinate=null,qe(t)&&this._chartStore.crosshairStore().set()}},{key:"mouseMoveEvent",value:function(t){qe(t)&&this._chartStore.crosshairStore().set({x:t.localX,y:t.paneY,paneId:t.paneId})}},{key:"mouseWheelEvent",value:function(t){if(Math.abs(t.deltaX)>Math.abs(t.deltaY)){if(t.cancelable&&t.preventDefault(),0===Math.abs(t.deltaX))return;this._chartStore.timeScaleStore().startScroll(),this._chartStore.timeScaleStore().scroll(-t.deltaX)}else{var e=-t.deltaY/100;if(0===e)return;switch(t.cancelable&&t.preventDefault(),t.deltaMode){case t.DOM_DELTA_PAGE:e*=120;break;case t.DOM_DELTA_LINE:e*=32}if(0!==e){var i=Math.sign(e)*Math.min(1,Math.abs(e));this._chartStore.timeScaleStore().zoom(i,{x:t.localX,y:t.localY})}}}},{key:"mouseClickEvent",value:function(t){Ke(t)&&(this._touchCoordinate||this._touchCancelCrosshair||this._touchZoomed||(this._touchCoordinate={x:t.localX,y:t.localY},this._chartStore.crosshairStore().set({x:t.localX,y:t.paneY,paneId:t.paneId})))}},{key:"mouseDownEvent",value:function(t){if(this._flingScrollTimerId&&(xe(this._flingScrollTimerId),this._flingScrollTimerId=null),this._flingStartTime=(new Date).getTime(),this._startScrollCoordinate={x:t.localX,y:t.localY},this._chartStore.timeScaleStore().startScroll(),Ke(t))if(this._touchZoomed=!1,this._touchCoordinate){var e=t.localX-this._touchCoordinate.x,i=t.localY-this._touchCoordinate.y;10>Math.sqrt(e*e+i*i)?(this._touchCoordinate={x:t.localX,y:t.localY},this._chartStore.crosshairStore().set({x:t.localX,y:t.paneY,paneId:t.paneId})):(this._touchCancelCrosshair=!0,this._touchCoordinate=null,this._chartStore.crosshairStore().set())}else this._touchCancelCrosshair=!1}},{key:"pressedMouseMoveEvent",value:function(t){var e={x:t.localX,y:t.paneY,paneId:t.paneId};if(Ke(t)){if(this._touchCoordinate)return this._touchCoordinate={x:t.localX,y:t.localY},void this._chartStore.crosshairStore().set(e);e=null}if(this._startScrollCoordinate){var i=t.localX-this._startScrollCoordinate.x;this._chartStore.timeScaleStore().scroll(i,e)}}},{key:"longTapEvent",value:function(t){Ke(t)&&(this._touchCoordinate={x:t.localX,y:t.localY},this._chartStore.crosshairStore().set({x:t.localX,y:t.paneY,paneId:t.paneId}))}}]),i}();function ci(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}function hi(t){for(var e=1;arguments.length>e;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ci(Object(i),!0).forEach(function(e){wt(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ci(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function li(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return ui(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?ui(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function ui(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}var fi=function(t){ct(i,oi);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t,n){var r;return v(this,i),(r=e.call(this,t))._yAxis=n,r}return _(i,[{key:"_performOverlayMouseHover",value:function(t,e,i,n){var r;if(t){var a,o=li(t);try{for(o.s();!(a=o.n()).done&&!(r=a.value.checkEventCoordinateOn(i)););}catch(t){o.e(t)}finally{o.f()}r&&e.id===r.id||(e.id&&e.instance&&qe(n)&&e.instance.onMouseLeave({id:e.id,points:e.instance.points(),event:n}),r&&r.id!==e.id&&r.instance&&qe(n)&&r.instance.onMouseEnter({id:r.id,points:r.instance.points(),event:n}))}return r}},{key:"mouseUpEvent",value:function(){this._chartStore.shapeStore().updatePressedInstance()}},{key:"mouseMoveEvent",value:function(t){if(qe(t)){if(this._waitingForMouseMove)return!1;this._waitingForMouseMove=!0;var e,i,n,r={x:t.localX,y:t.paneY},a=this._chartStore.shapeStore().progressInstance(),o=a.instance,s=a.paneId;if(o&&o.isDrawing())t.paneId&&(o.isStart()&&this._chartStore.shapeStore().updateProgressInstance(this._yAxis(t.paneId),t.paneId),s===t.paneId&&o.mouseMoveForDrawing(r,t),e={id:o.id(),element:Ut,elementIndex:o.points().length-1}),i={id:"",element:Zt,elementIndex:-1};else{var c=this._chartStore.annotationStore().get(t.paneId),h=this._chartStore.shapeStore().instances(t.paneId),l=this._chartStore.shapeStore().eventOperate().hover,u=this._chartStore.annotationStore().eventOperate();e=this._performOverlayMouseHover(h,l,r,t),n=this._performOverlayMouseHover(c,u,r,t)}this._chartStore.shapeStore().setEventOperate({hover:e||{id:"",element:Zt,elementIndex:-1},click:i}),this._chartStore.annotationStore().setEventOperate(n||{id:""}),this._waitingForMouseMove=!1}}},{key:"mouseDownEvent",value:function(t){var e,i={x:t.localX,y:t.paneY},n=this._chartStore.shapeStore().progressInstance(),r=n.instance,a={id:"",element:Zt,elementIndex:-1},o=n.paneId;if(r&&r.isDrawing())Ke(t)&&(r.isStart()&&(this._chartStore.shapeStore().updateProgressInstance(this._yAxis(t.paneId),t.paneId),o=t.paneId),o===t.paneId&&r.mouseMoveForDrawing(i,t)),o===t.paneId&&(r.mouseLeftButtonDownForDrawing(),e={id:r.id(),element:Ut,elementIndex:r.points().length-1},a={id:r.id(),element:Ut,elementIndex:r.points().length-1});else{var s,c=li(this._chartStore.shapeStore().instances(t.paneId));try{for(c.s();!(s=c.n()).done;){var h=s.value;if(e=h.checkEventCoordinateOn(i)){this._chartStore.shapeStore().updatePressedInstance(h,t.paneId,e.element),e.element===Ut?a=hi({},e):h.startPressedOtherMove(i),h.onClick({id:e.id,points:h.points(),event:t});break}}}catch(t){c.e(t)}finally{c.f()}var l=this._chartStore.annotationStore().get(t.paneId);if(l){var u,f=li(l);try{for(f.s();!(u=f.n()).done;){var d=u.value,v=d.checkEventCoordinateOn(i);if(v){d.onClick({id:v.id,points:d.points(),event:t});break}}}catch(t){f.e(t)}finally{f.f()}}}this._chartStore.shapeStore().setEventOperate({hover:a,click:e||{id:"",element:Zt,elementIndex:-1}})&&this._chartStore.invalidate(1)}},{key:"mouseRightDownEvent",value:function(t){var e,i=this._chartStore.shapeStore().progressInstance().instance;(e=i||this._chartStore.shapeStore().instances(t.paneId).find(function(e){return e.checkEventCoordinateOn({x:t.localX,y:t.paneY})}))&&!e.onRightClick({id:e.id(),points:e.points(),event:t})&&this._chartStore.shapeStore().removeInstance(e.id());var n=this._chartStore.annotationStore().get(t.paneId);if(n){var r=n.find(function(e){return e.checkEventCoordinateOn({x:t.localX,y:t.paneY})});r&&r.onRightClick({id:r.id(),points:r.points(),event:t})}}},{key:"pressedMouseMoveEvent",value:function(t){var e=this._chartStore.shapeStore().pressedInstance(),i=e.instance;if(i&&e.paneId===t.paneId){var n={x:t.localX,y:t.paneY};e.element===Ut?i.mousePressedPointMove(n,t):i.mousePressedOtherMove(n,t),this._chartStore.crosshairStore().set({x:t.localX,y:t.paneY,paneId:t.paneId})}}}]),i}();var di=function(t){ct(i,oi);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(){return v(this,i),e.apply(this,arguments)}return _(i,[{key:"keyBoardDownEvent",value:function(t){if(t.shiftKey)switch(t.code){case"Equal":this._chartStore.timeScaleStore().zoom(.5);break;case"Minus":this._chartStore.timeScaleStore().zoom(-.5);break;case"ArrowLeft":this._chartStore.timeScaleStore().startScroll(),this._chartStore.timeScaleStore().scroll(-3*this._chartStore.timeScaleStore().dataSpace());break;case"ArrowRight":this._chartStore.timeScaleStore().startScroll(),this._chartStore.timeScaleStore().scroll(3*this._chartStore.timeScaleStore().dataSpace())}}}]),i}(),vi=function(){function t(e,i,n){v(this,t),this._target=e,this._chartStore=i,this._chartContentLeftRight={},this._chartContentTopBottom={},this._paneContentSize={},this._event=new ri(this._target,{pinchStartEvent:this._pinchStartEvent.bind(this),pinchEvent:this._pinchEvent.bind(this),mouseUpEvent:this._mouseUpEvent.bind(this),mouseClickEvent:this._mouseClickEvent.bind(this),mouseDownEvent:this._mouseDownEvent.bind(this),mouseRightDownEvent:this._mouseRightDownEvent.bind(this),mouseLeaveEvent:this._mouseLeaveEvent.bind(this),mouseMoveEvent:this._mouseMoveEvent.bind(this),mouseWheelEvent:this._mouseWheelEvent.bind(this),pressedMouseMoveEvent:this._pressedMouseMoveEvent.bind(this),longTapEvent:this._longTapEvent.bind(this)},{treatVertTouchDragAsPageScroll:!0,treatHorzTouchDragAsPageScroll:!1}),this._boundKeyBoardDownEvent=this._keyBoardDownEvent.bind(this),this._target.addEventListener("keydown",this._boundKeyBoardDownEvent),this._boundContextMenuEvent=function(t){t.preventDefault()},this._target.addEventListener("contextmenu",this._boundContextMenuEvent,!1),this._zoomScrollEventHandler=new si(i),this._overlayEventHandler=new fi(i,n),this._keyBoardEventHandler=new di(i)}return _(t,[{key:"_keyBoardDownEvent",value:function(t){this._keyBoardEventHandler.keyBoardDownEvent(t)}},{key:"_pinchStartEvent",value:function(){this._zoomScrollEventHandler.pinchStartEvent()}},{key:"_pinchEvent",value:function(t,e){this._zoomScrollEventHandler.pinchEvent(t,e)}},{key:"_mouseUpEvent",value:function(t){this._checkEventInChartContent(t)&&(this._target.style.cursor="crosshair"),this._zoomScrollEventHandler.mouseUpEvent(t),this._shouldPerformOverlayEvent()&&this._overlayEventHandler.mouseUpEvent(t)}},{key:"_mouseLeaveEvent",value:function(t){this._zoomScrollEventHandler.mouseLeaveEvent(t)}},{key:"_mouseMoveEvent",value:function(t){if(t.target instanceof HTMLCanvasElement)if(this._checkEventInChartContent(t)){this._target.style.cursor="crosshair";var e=this._compatChartEvent(t,!0);this._shouldPerformOverlayEvent()&&this._overlayEventHandler.mouseMoveEvent(e),this._chartStore.dragPaneFlag()||this._zoomScrollEventHandler.mouseMoveEvent(e)}else this._target.style.cursor="default",this._zoomScrollEventHandler.mouseLeaveEvent(t);else this._target.style.cursor="default",this._chartStore.crosshairStore().set()}},{key:"_mouseWheelEvent",value:function(t){this._checkZoomScroll()&&this._checkEventInChartContent(t)&&this._zoomScrollEventHandler.mouseWheelEvent(this._compatChartEvent(t))}},{key:"_mouseClickEvent",value:function(t){this._checkZoomScroll()&&this._checkEventInChartContent(t)&&(this._zoomScrollEventHandler.mouseClickEvent(this._compatChartEvent(t,!0)),this._modifyEventOptions(t))}},{key:"_mouseDownEvent",value:function(t){if(this._checkEventInChartContent(t)){this._target.style.cursor="pointer";var e=this._compatChartEvent(t,!0);this._shouldPerformOverlayEvent()&&this._overlayEventHandler.mouseDownEvent(e),this._checkZoomScroll()&&(this._zoomScrollEventHandler.mouseDownEvent(e),this._modifyEventOptions(t))}}},{key:"_mouseRightDownEvent",value:function(t){this._shouldPerformOverlayEvent()&&this._checkEventInChartContent(t)&&this._overlayEventHandler.mouseRightDownEvent(this._compatChartEvent(t,!0))}},{key:"_pressedMouseMoveEvent",value:function(t){if(this._checkEventInChartContent(t)){var e=this._compatChartEvent(t,!0);this._checkZoomScroll()?(this._zoomScrollEventHandler.pressedMouseMoveEvent(e),this._modifyEventOptions(t)):this._overlayEventHandler.pressedMouseMoveEvent(e)}}},{key:"_longTapEvent",value:function(t){this._checkZoomScroll()&&this._checkEventInChartContent(t)&&(this._zoomScrollEventHandler.longTapEvent(this._compatChartEvent(t,!0)),this._modifyEventOptions(t))}},{key:"_checkZoomScroll",value:function(){return!this._chartStore.dragPaneFlag()&&!this._chartStore.shapeStore().isPressed()&&!this._chartStore.shapeStore().isDrawing()}},{key:"_shouldPerformOverlayEvent",value:function(){return!this._chartStore.shapeStore().isEmpty()||!this._chartStore.annotationStore().isEmpty()}},{key:"_modifyEventOptions",value:function(t){Ke(t)&&this._chartStore.crosshairStore().get().paneId?this._event.setOptions({treatVertTouchDragAsPageScroll:!1}):this._event.setOptions({treatVertTouchDragAsPageScroll:!0})}},{key:"_compatChartEvent",value:function(t,e){if(e)for(var i in this._paneContentSize)if(Object.prototype.hasOwnProperty.call(this._paneContentSize,i)){var n=this._paneContentSize[i];if(t.localY>n.contentTop&&n.contentBottom>t.localY){t.paneY=t.localY-n.contentTop,t.paneId=i;break}}return t.localX-=this._chartContentLeftRight.contentLeft,t}},{key:"_checkEventInChartContent",value:function(t){return t.localX>this._chartContentLeftRight.contentLeft&&this._chartContentLeftRight.contentRight>t.localX&&t.localY>this._chartContentTopBottom.contentTop&&this._chartContentTopBottom.contentBottom>t.localY}},{key:"setChartContentLeftRight",value:function(t){this._chartContentLeftRight=t}},{key:"setChartContentTopBottom",value:function(t){this._chartContentTopBottom=t}},{key:"setPaneContentSize",value:function(t){this._paneContentSize=t}},{key:"destroy",value:function(){this._event.destroy(),this._target.removeEventListener("keydown",this._boundKeyBoardDownEvent),this._target.removeEventListener("contextmenu",this._boundContextMenuEvent)}}]),t}();var pi=function(t){ct(i,Et);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t){var n,r=t.id,a=t.point,o=void 0===a?{}:a,s=t.chartStore,c=t.xAxis,h=t.yAxis,l=t.styles;return v(this,i),(n=e.call(this,{id:r,chartStore:s,points:o,xAxis:c,yAxis:h}))._symbolCoordinate={},n.setStyles(l,s.styleOptions().annotation),n}return _(i,[{key:"_drawSymbol",value:function(t,e,i){var n=this._chartStore.timeScaleStore().barSpace(),r=i.symbol,a=r.size,o=r.activeSize,s=e?w(o)?o:n:w(a)?a:n,c=e?r.activeColor:r.color;switch(r.type){case j:Pt(t,c,this._symbolCoordinate,s/2);break;case N:!function(t,e,i,n,r,a){t.fillStyle=e,t.fillRect(i,n,r,a)}(t,c,this._symbolCoordinate.x-s/2,this._symbolCoordinate.y-s/2,s,s);break;case W:!function(t,e,i,n,r){t.fillStyle=e,t.beginPath(),t.moveTo(i.x-n/2,i.y),t.lineTo(i.x,i.y-r/2),t.lineTo(i.x+n/2,i.y),t.lineTo(i.x,i.y+r/2),t.closePath(),t.fill()}(t,c,this._symbolCoordinate,s,s);break;case Y:!function(t,e,i,n,r){t.fillStyle=e,t.beginPath(),t.moveTo(i.x-n/2,i.y+r/2),t.lineTo(i.x,i.y-r/2),t.lineTo(i.x+n/2,i.y+r/2),t.closePath(),t.fill()}(t,c,this._symbolCoordinate,s,s);break;case X:t.save(),this.drawCustomSymbol({ctx:t,point:this._points,coordinate:this._symbolCoordinate,viewport:{width:this._xAxis.width(),height:this._yAxis.height(),barSpace:n},styles:r,isActive:e}),t.restore()}}},{key:"draw",value:function(t){var e=this._styles||this._chartStore.styleOptions().annotation,i=e.offset||[0,0],n=0;switch(e.position){case $:n=this._yAxis.convertToPixel(this._points.value);break;case G:n=0;break;case U:n=this._yAxis.height()}this._symbolCoordinate.y=n+i[0];var r=this._id===this._chartStore.annotationStore().eventOperate().id;this._drawSymbol(t,r,e),this.drawExtend&&(t.save(),this.drawExtend({ctx:t,point:this._points,coordinate:this._symbolCoordinate,viewport:{width:this._xAxis.width(),height:this._yAxis.height()},styles:e,isActive:r}),t.restore())}},{key:"checkEventCoordinateOn",value:function(t){var e,i,n,r,a,o=this._chartStore.timeScaleStore().barSpace(),s=(this._styles||this._chartStore.styleOptions().annotation).symbol,c=w(s.size)?s.size:o;switch(s.type){case j:e=Lt(this._symbolCoordinate,c/2,t);break;case N:e=function(t,e,i){return!(t.x>i.x||i.x>e.x||t.y>i.y||i.y>e.y)}({x:this._symbolCoordinate.x-c/2,y:this._symbolCoordinate.y-c/2},{x:this._symbolCoordinate.x+c/2,y:this._symbolCoordinate.y+c/2},t);break;case W:e=(n=c)*(r=c)/2+2>Math.abs((i=this._symbolCoordinate).x-(a=t).x)*r+Math.abs(i.y-a.y)*n;break;case Y:e=function(t,e){var i=Ct(t[0],t[1],t[2]),n=Ct(t[0],t[1],e)+Ct(t[0],t[2],e)+Ct(t[1],t[2],e);return 2>Math.abs(i-n)}([{x:this._symbolCoordinate.x-c/2,y:this._symbolCoordinate.y+c/2},{x:this._symbolCoordinate.x,y:this._symbolCoordinate.y-c/2},{x:this._symbolCoordinate.x+c/2,y:this._symbolCoordinate.y+c/2}],t);break;case X:e=this.checkEventCoordinateOnCustomSymbol({eventCoordinate:t,coordinate:this._symbolCoordinate,size:c})}if(e)return{id:this._id,instance:this}}},{key:"createSymbolCoordinate",value:function(t){var e=this._styles||this._chartStore.styleOptions().annotation;this._symbolCoordinate={x:t+(e.offset||[0,0])[1]}}},{key:"checkEventCoordinateOnCustomSymbol",value:function(t){}},{key:"drawCustomSymbol",value:function(t){}}]),i}();var _i=function(t){ct(i,Et);var e=function(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var i,n=lt(t);if(e){var r=lt(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return ht(this,i)}}(i);function i(t){var n,r=t.id,a=t.point,o=void 0===a?{}:a,s=t.text,c=t.mark,h=t.chartStore,l=t.xAxis,u=t.yAxis,f=t.styles;return v(this,i),(n=e.call(this,{id:r,chartStore:h,points:o,xAxis:l,yAxis:u}))._text=s,n._mark=c,n.setStyles(f,h.styleOptions().tag),n}return _(i,[{key:"update",value:function(t){var e=t.point,i=t.text,n=t.mark,r=t.styles,a=!1;return k(e)&&(this._points=e,a=!0),b(i)&&(this._text=i,a=!0),b(n)&&(this._mark=n,a=!0),this.setStyles(r,this._chartStore.styleOptions().tag)&&(a=!0),a}},{key:"drawMarkLine",value:function(t){var e=this._chartStore.styleOptions(),i=e.yAxis,n=this._styles||e.tag,r=this._getY(n);t.save(),this._drawLine(t,r,n,i),this._drawMark(t,r,n,i),t.restore()}},{key:"drawText",value:function(t){if(b(this._text)){var e=this._chartStore.styleOptions(),i=this._styles||e.tag,n=i.text;t.save();var r,a=Xt(t,this._text,n),o=$t(n);r=this._yAxis.isFromYAxisZero()?0:this._yAxis.width()-a;var s=this._getY(i);Ee(t,n.backgroundColor,n.borderColor,n.borderSize,r,s-o/2,a,o,n.borderRadius),ke(t,n.color,r+n.paddingLeft,s,this._text),t.restore()}}},{key:"_drawLine",value:function(t,e,i,n){var r=i.line;if(r.show){t.save();var a=Xt(t,this._text,i.text),o=Xt(t,this._mark,i.mark);t.strokeStyle=r.color,t.lineWidth=r.size,r.style===I&&t.setLineDash(r.dashValue);var s=i.mark.offset,c=[],h=b(this._text),l=b(this._mark);n.inside?n.position===M?h&&l?s>0?(c.push([a,a+s]),c.push([a+s+o,this._xAxis.width()])):c.push(Math.min(a,o)>Math.abs(s)?[a+s+o,this._xAxis.width()]:[Math.max(a,o),this._xAxis.width()]):h?c.push([a,this._xAxis.width()]):l?s>0?(c.push([0,s]),c.push([s+o,this._xAxis.width()])):c.push(o>Math.abs(s)?[s+o,this._xAxis.width()]:[0,this._xAxis.width()]):c.push([0,this._xAxis.width()]):h&&l?0>s?(c.push([0,this._xAxis.width()-a+s-o]),c.push([this._xAxis.width()-a+s,this._xAxis.width()-a])):c.push(Math.min(a,o)>s?[0,this._xAxis.width()-a-o+s]:[0,this._xAxis.width()-Math.max(a,o)]):h?c.push([0,this._xAxis.width()-a]):l?0>s?(c.push([0,this._xAxis.width()+s-o]),c.push([this._xAxis.width()+s,this._xAxis.width()])):c.push(o>s?[0,this._xAxis.width()-o+s]:[0,this._xAxis.width()]):c.push([0,this._xAxis.width()]):n.position===M?l?s>0?(c.push([0,s]),c.push([s+o,this._xAxis.width()])):c.push(o>Math.abs(s)?[o+s,this._xAxis.width()]:[0,this._xAxis.width()]):c.push([0,this._xAxis.width()]):l?0>s?(c.push([0,this._xAxis.width()-o+s]),c.push([this._xAxis.width()+s,this._xAxis.width()])):c.push(o>s?[0,this._xAxis.width()-o+s]:[0,this._xAxis.width()]):c.push([0,this._xAxis.width()]),c.forEach(function(i){Vt(t,e,i[0],i[1])}),t.restore()}}},{key:"_drawMark",value:function(t,e,i,n){if(b(this._mark)){var r,a=i.mark,o=Xt(t,this._mark,a),s=$t(a);if(n.inside){var c=0;b(this._text)&&(c=Xt(t,this._text,i.text)),r=n.position===M?c:this._xAxis.width()-c-o}else r=n.position===M?0:this._xAxis.width()-o;Ee(t,a.backgroundColor,a.borderColor,a.borderSize,r+=a.offset,e-s/2,o,s,a.borderRadius),t.textBaseline="middle",t.font=Wt(a.size,a.weight,a.family),ke(t,a.color,r+a.paddingLeft,e,this._mark)}}},{key:"_getY",value:function(t){var e=t.offset;switch(t.position){case G:return e;case U:return this._yAxis.height()+e;default:return this._yAxis.convertToNicePixel(this._points.value)+e}}}]),i}();function mi(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return yi(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?yi(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function yi(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}var xi="candle_pane",gi=function(){function t(e,i){var n=this;v(this,t),this._initChartContainer(e),this._separatorDragStartTopPaneHeight=0,this._separatorDragStartBottomPaneHeight=0,this._chartStore=new fe(i,{invalidate:this._invalidatePane.bind(this),crosshair:this._crosshairObserver.bind(this)}),this._xAxisPane=new Xe({id:"x_axis_pane",container:this._chartContainer,chartStore:this._chartStore}),this._panes=new Map([[xi,new He({container:this._chartContainer,chartStore:this._chartStore,xAxis:this._xAxisPane.xAxis(),id:xi})]]),this._separators=new Map,this._chartWidth={},this._chartHeight={},this._chartEvent=new vi(this._chartContainer,this._chartStore,function(t){return n._panes.get(t).yAxis()}),this.adjustPaneViewport(!0,!0,!0)}return _(t,[{key:"_initChartContainer",value:function(t){this._container=t,this._chartContainer=ve("div",{userSelect:"none",webkitUserSelect:"none",msUserSelect:"none",MozUserSelect:"none",webkitTapHighlightColor:"transparent",position:"relative",outline:"none",borderStyle:"none",width:"100%",cursor:"crosshair",boxSizing:"border-box"}),this._chartContainer.tabIndex=1,t.appendChild(this._chartContainer)}},{key:"_crosshairObserver",value:function(t){var e=this,i=t.paneId,n=t.dataIndex,r=t.kLineData,a=t.x,o=t.y;if(this._chartStore.actionStore().has(rt.CROSSHAIR)||this._chartStore.actionStore().has(rt.TOOLTIP)){var s={};this._panes.forEach(function(t,i){var a={},o=[];e.chartStore().technicalIndicatorStore().instances(i).forEach(function(t){var e=t.result[n];a[t.name]=e,o.push({name:t.name,data:e})}),s[i]=a,e._chartStore.actionStore().execute(rt.TOOLTIP,{paneId:i,dataIndex:n,kLineData:r,technicalIndicatorData:o})}),i&&this._chartStore.actionStore().execute(rt.CROSSHAIR,{paneId:i,coordinate:{x:a,y:o},dataIndex:n,kLineData:r,technicalIndicatorData:s})}}},{key:"_separatorStartDrag",value:function(t,e){this._separatorDragStartTopPaneHeight=this._panes.get(t).height(),this._separatorDragStartBottomPaneHeight=this._panes.get(e).height()}},{key:"_separatorDrag",value:function(t,e,i){var n=this._panes.get(e),r=this._panes.get(i),a=n.minHeight(),o=r.minHeight(),s=this._separatorDragStartTopPaneHeight+t,c=this._separatorDragStartBottomPaneHeight-t;s>this._separatorDragStartTopPaneHeight+this._separatorDragStartBottomPaneHeight&&(s=this._separatorDragStartTopPaneHeight+this._separatorDragStartBottomPaneHeight,c=0),0>s&&(s=0,c=this._separatorDragStartTopPaneHeight+this._separatorDragStartBottomPaneHeight),a>s&&(c-=o-s,s=a),o>c&&(s-=a-c,c=o),n.setHeight(s),r.setHeight(c),this._chartStore.actionStore().execute(rt.PANE_DRAG,{topPaneId:e,bottomPaneId:i,topPaneHeight:s,bottomPaneHeight:c}),this.adjustPaneViewport(!0,!0,!0,!0,!0)}},{key:"_invalidatePane",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;if(1===t)this._xAxisPane.invalidate(t),this._panes.forEach(function(e){e.invalidate(t)});else{var e=!1;this._panes.forEach(function(t){var i=t.yAxis().computeAxis();i&&(e=i)}),this.adjustPaneViewport(!1,e,!0)}}},{key:"_measurePaneHeight",value:function(){var t=this,e=this._chartStore.styleOptions(),i=this._container.offsetHeight,n=e.separator.size,r=n*this._separators.size,a=this._xAxisPane.xAxis().getSelfHeight(),o=i-a-r;0>o&&(o=0);var s=0;this._panes.forEach(function(t){if(t.id()!==xi){var e=t.height(),i=t.minHeight();i>e&&(e=i),s+e>o?0>(e=o-(s=o))&&(e=0):s+=e,t.setHeight(e)}});var c=o-s,h={};h.candle_pane={contentTop:0,contentBottom:c};var l=c,u=c;this._panes.get(xi).setHeight(c),this._chartHeight.candle_pane=c,this._panes.forEach(function(e){if(e.id()!==xi){var i=e.height();u+=i+n,h[e.id()]={contentTop:l,contentBottom:u},t._chartHeight[e.id()]=i,l=u}}),this._xAxisPane.setHeight(a),this._chartHeight.xAxis=a,this._chartHeight.total=i,this._chartEvent.setPaneContentSize(h),this._chartEvent.setChartContentTopBottom({contentTop:0,contentBottom:u})}},{key:"_measurePaneWidth",value:function(){var t,e,i,n=this,r=this._chartStore.styleOptions().yAxis,a=r.position===M,o=!r.inside,s=this._container.offsetWidth,c=Number.MIN_SAFE_INTEGER;this._panes.forEach(function(t){c=Math.max(c,t.yAxis().getSelfWidth())}),c>s&&(c=s),o?(t=s-c,a?(e=0,i=c):(e=s-c,i=0)):(t=s,i=0,e=a?0:s-c);var h=t;h<this._chartStore.timeScaleStore().dataSpace()&&(h=this._chartStore.timeScaleStore().dataSpace()),this._chartStore.timeScaleStore().setTotalDataSpace(h),this._panes.forEach(function(r,a){r.setWidth(t,c),r.setOffsetLeft(i,e);var o=n._separators.get(a);o&&o.setSize(i,t)}),this._chartWidth={content:t,yAxis:c,total:s},this._xAxisPane.setWidth(t,c),this._xAxisPane.setOffsetLeft(i,e),this._chartEvent.setChartContentLeftRight({contentLeft:i,contentRight:i+t})}},{key:"getContainer",value:function(){return this._container}},{key:"adjustPaneViewport",value:function(t,e,i,n,r){t&&this._measurePaneHeight();var a=!1;n&&this._panes.forEach(function(t){var e=t.yAxis().computeAxis(r);a||(a=e)}),(!n&&e||n&&a)&&this._measurePaneWidth(),i&&(this._xAxisPane.xAxis().computeAxis(!0),this._xAxisPane.layout(),this._panes.forEach(function(t){t.layout()}))}},{key:"hasPane",value:function(t){return this._panes.has(t)}},{key:"getPane",value:function(t){return this._panes.get(t)}},{key:"chartStore",value:function(){return this._chartStore}},{key:"removeTechnicalIndicator",value:function(t,e){var i=this;if(this._chartStore.technicalIndicatorStore().removeInstance(t,e)){var n=!1;if(t!==xi&&!this._chartStore.technicalIndicatorStore().hasInstance(t)){n=!0,this._panes.get(t).destroy();var r=this._separators.get(t).topPaneId();this._separators.get(t).destroy(),this._panes.delete(t),this._separators.delete(t),this._separators.forEach(function(t){var e=t.topPaneId();i._separators.has(e)||t.updatePaneId(r)})}this.adjustPaneViewport(n,!0,!0,!0,!0)}}},{key:"createTechnicalIndicator",value:function(t,e){var i=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this._panes.has(n.id)){var r=this._chartStore.technicalIndicatorStore().addInstance(n.id,t,e);return r&&r.finally(function(t){i.setPaneOptions(n,i._panes.get(n.id).yAxis().computeAxis(!0))}),n.id}var a=n.id||Ge("tech_pane_"),o=!E(n.dragEnabled)||n.dragEnabled;this._separators.set(a,new ai(this._chartContainer,this._chartStore,Array.from(this._panes.keys()).pop(),a,o,{startDrag:this._separatorStartDrag.bind(this),drag:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,i=0;return function(){var n=Date.now(),r=arguments;n-i>e&&(t.apply(this,r),i=n)}}(this._separatorDrag.bind(this),50)}));var s=new Be({container:this._chartContainer,chartStore:this._chartStore,xAxis:this._xAxisPane.xAxis(),id:a,height:n.height||100,minHeight:n.minHeight});this._panes.set(a,s);var c=this._chartStore.technicalIndicatorStore().addInstance(a,t,e);return c&&c.finally(function(t){i.adjustPaneViewport(!0,!0,!0,!0,!0)}),a}},{key:"createShape",value:function(t,e,i){var n=e.points,r=e.styles,a=e.lock,o=e.mode,s=e.data,c=e.onDrawStart,h=e.onDrawing,l=e.onDrawEnd,u=e.onClick,f=e.onRightClick,d=e.onPressedMove,v=e.onMouseEnter,p=e.onMouseLeave,_=e.onRemove,m=e.id||Ge("shape_");if(!this._chartStore.shapeStore().hasInstance(m)){var y=null;this.hasPane(i)?y=this._panes.get(i).yAxis():n&&n.length>0&&(i=xi,y=this._panes.get(xi).yAxis());var x=new t({id:m,chartStore:this._chartStore,xAxis:this._xAxisPane.xAxis(),yAxis:y,points:n,styles:r,lock:a,mode:o,data:s});return S(c)&&c({id:m}),bt(x,[{key:"onDrawing",fn:h},{key:"onDrawEnd",fn:l},{key:"onClick",fn:u},{key:"onRightClick",fn:f},{key:"onPressedMove",fn:d},{key:"onMouseEnter",fn:v},{key:"onMouseLeave",fn:p},{key:"onRemove",fn:_}]),this._chartStore.shapeStore().addInstance(x,i),m}return null}},{key:"createAnnotation",value:function(t,e){var i=this,n=[];t.forEach(function(t){var r=t.point,a=t.styles,o=t.checkEventCoordinateOnCustomSymbol,s=t.drawCustomSymbol,c=t.drawExtend,h=t.onClick,l=t.onRightClick,u=t.onMouseEnter,f=t.onMouseLeave;if(r&&r.timestamp){var d=new pi({id:Ge("an_"),chartStore:i._chartStore,point:r,xAxis:i._xAxisPane.xAxis(),yAxis:i._panes.get(e).yAxis(),styles:a});bt(d,[{key:"drawExtend",fn:c},{key:"drawCustomSymbol",fn:s},{key:"checkEventCoordinateOnCustomSymbol",fn:o},{key:"onClick",fn:h},{key:"onRightClick",fn:l},{key:"onMouseEnter",fn:u},{key:"onMouseLeave",fn:f}]),n.push(d)}}),n.length>0&&this._chartStore.annotationStore().add(n,e)}},{key:"createTag",value:function(t,e){var i=this,n=[],r=!1,a=!1;t.forEach(function(t){var o=t.id,s=t.point,c=t.text,h=t.mark,l=t.styles;if(b(o))if(i._chartStore.tagStore().has(o,e)){var u=i._chartStore.tagStore().update(o,e,{point:s,text:c,mark:h,styles:l});r||(r=u)}else a=!0,n.push(new _i({id:o,point:s,text:c,mark:h,styles:l,chartStore:i._chartStore,xAxis:i._xAxisPane.xAxis(),yAxis:i._panes.get(e).yAxis()}))}),a?this._chartStore.tagStore().add(n,e):r&&this._invalidatePane(1)}},{key:"removeAllHtml",value:function(){this._panes.forEach(function(t){t.removeHtml()}),this._xAxisPane.removeHtml()}},{key:"setPaneOptions",value:function(t,e){var i=e,n=!1;if(t.id!==xi){var r=this._panes.get(t.id);if(r){if(w(t.minHeight)&&t.minHeight>0&&r.setMinHeight(t.minHeight),w(t.height)&&t.height>0){var a=r.minHeight(),o=r.minHeight>t.height?a:t.height;r.height()!==o&&(i=!0,r.setHeight(t.height),n=!0)}E(t.dragEnabled)&&this._separators.get(t.id).setDragEnabled(t.dragEnabled)}}i&&this.adjustPaneViewport(n,!0,!0,!0,!0)}},{key:"setTimezone",value:function(t){this._chartStore.timeScaleStore().setTimezone(t),this._xAxisPane.xAxis().computeAxis(!0),this._xAxisPane.invalidate(3)}},{key:"convertToPixel",value:function(t,e){var i,n=this,r=e.paneId,a=void 0===r?xi:r,o=e.absoluteYAxis,s=[].concat(t),c=[],h=this._chartStore.styleOptions().separator.size,l=0,u=mi(this._panes.values());try{var f=function(){var t=i.value;if(t.id()===a)return c=s.map(function(e){var i=e.timestamp,r=e.value,a={},s=e.dataIndex;if(b(i)&&(s=n._chartStore.timeScaleStore().timestampToDataIndex(i)),b(s)&&(a.x=n._xAxisPane.xAxis().convertToPixel(s)),b(r)){var c=t.yAxis().convertToPixel(r);a.y=o?l+c:c}return a}),"break";l+=t.height()+h};for(u.s();!(i=u.n()).done&&"break"!==f(););}catch(t){u.e(t)}finally{u.f()}return g(t)?c:c[0]||{}}},{key:"convertFromPixel",value:function(t,e){var i,n=this,r=e.paneId,a=void 0===r?xi:r,o=e.absoluteYAxis,s=[].concat(t),c=[],h=this._chartStore.styleOptions().separator.size,l=0,u=mi(this._panes.values());try{var f=function(){var t=i.value;if(t.id()===a)return c=s.map(function(e){var i=e.x,r=e.y,a={};if(b(i)&&(a.dataIndex=n._xAxisPane.xAxis().convertFromPixel(i),a.timestamp=n._chartStore.timeScaleStore().dataIndexToTimestamp(a.dataIndex)),b(r)){var s=o?r-l:r;a.value=t.yAxis().convertFromPixel(s)}return a}),"break";l+=t.height()+h};for(u.s();!(i=u.n()).done&&"break"!==f(););}catch(t){u.e(t)}finally{u.f()}return g(t)?c:c[0]||{}}},{key:"chartWidth",value:function(){return this._chartWidth}},{key:"chartHeight",value:function(){return this._chartHeight}},{key:"getConvertPictureUrl",value:function(t,e,i){var n=this,r=this._chartContainer.offsetWidth,a=this._chartContainer.offsetHeight,o=ve("canvas",{width:"".concat(r,"px"),height:"".concat(a,"px"),boxSizing:"border-box"}),s=o.getContext("2d"),c=Nt(o);o.width=r*c,o.height=a*c,s.scale(c,c),s.fillStyle=i,s.fillRect(0,0,r,a);var h=0;return this._panes.forEach(function(e,i){if(i!==xi){var a=n._separators.get(i);s.drawImage(a.getImage(),0,h,r,a.height()),h+=a.height()}s.drawImage(e.getImage(t),0,h,r,e.height()),h+=e.height()}),s.drawImage(this._xAxisPane.getImage(t),0,h,r,this._xAxisPane.height()),o.toDataURL("image/".concat(e))}},{key:"destroy",value:function(){this._panes.forEach(function(t){t.destroy()}),this._separators.forEach(function(t){t.destroy()}),this._panes.clear(),this._separators.clear(),this._xAxisPane.destroy(),this._container.removeChild(this._chartContainer),this._chartEvent.destroy()}}]),t}(),Si=function(){function t(e,i){v(this,t),this._chartPane=new gi(e,i)}return _(t,[{key:"getDom",value:function(t){if(t){if(!k(t))return null;var e=t.position,i=this._chartPane.getPane(t.paneId);return i&&i.container(e)||null}return this._chartPane.getContainer()}},{key:"getWidth",value:function(){return this._chartPane.chartWidth()}},{key:"getHeight",value:function(){return this._chartPane.chartHeight()}},{key:"setStyleOptions",value:function(t){k(t)&&(this._chartPane.chartStore().applyStyleOptions(t),this._chartPane.adjustPaneViewport(!0,!0,!0,!0,!0))}},{key:"getStyleOptions",value:function(){return x(this._chartPane.chartStore().styleOptions())}},{key:"setPriceVolumePrecision",value:function(t,e){w(t)&&t>=0&&w(e)&&e>=0&&this._chartPane.chartStore().setPriceVolumePrecision(t,e)}},{key:"setTimezone",value:function(t){this._chartPane.setTimezone(t)}},{key:"getTimezone",value:function(){return this._chartPane.chartStore().timeScaleStore().timezone()}},{key:"resize",value:function(){this._chartPane.adjustPaneViewport(!0,!0,!0,!0,!0)}},{key:"setOffsetRightSpace",value:function(t){w(t)&&this._chartPane.chartStore().timeScaleStore().setOffsetRightSpace(t,!0)}},{key:"setLeftMinVisibleBarCount",value:function(t){w(t)&&t>0&&this._chartPane.chartStore().timeScaleStore().setLeftMinVisibleBarCount(Math.ceil(t))}},{key:"setRightMinVisibleBarCount",value:function(t){w(t)&&t>0&&this._chartPane.chartStore().timeScaleStore().setRightMinVisibleBarCount(Math.ceil(t))}},{key:"setDataSpace",value:function(t){w(t)&&this._chartPane.chartStore().timeScaleStore().setDataSpace(t)}},{key:"getDataSpace",value:function(){return this._chartPane.chartStore().timeScaleStore().dataSpace()}},{key:"getBarSpace",value:function(){return this._chartPane.chartStore().timeScaleStore().barSpace()}},{key:"clearData",value:function(){this._chartPane.chartStore().clearDataList()}},{key:"getDataList",value:function(){return this._chartPane.chartStore().dataList()}},{key:"applyNewData",value:function(t,e){var i=this;if(g(t)){var n=this._chartPane.chartStore();n.clearDataList(),n.addData(t,0,e),n.technicalIndicatorStore().calcInstance().finally(function(t){i._chartPane.adjustPaneViewport(!1,!0,!0,!0)})}}},{key:"applyMoreData",value:function(t,e){var i=this;if(g(t)){var n=this._chartPane.chartStore();n.addData(t,0,e),n.technicalIndicatorStore().calcInstance().finally(function(t){i._chartPane.adjustPaneViewport(!1,!0,!0,!0)})}}},{key:"updateData",value:function(t){var e=this;if(k(t)&&!g(t)){var i=this._chartPane.chartStore(),n=i.dataList(),r=n.length,a=K(t,"timestamp",0),o=K(n[r-1],"timestamp",0);if(a>=o){var s=r;a===o&&(s=r-1),i.addData(t,s),i.technicalIndicatorStore().calcInstance().finally(function(t){e._chartPane.adjustPaneViewport(!1,!0,!0,!0)})}}}},{key:"loadMore",value:function(t){S(t)&&this._chartPane.chartStore().timeScaleStore().setLoadMoreCallback(t)}},{key:"createTechnicalIndicator",value:function(t,e,i){if(!b(t))return null;var n=k(t)&&!g(t)?t:{name:t};return this._chartPane.chartStore().technicalIndicatorStore().hasTemplate(n.name)?this._chartPane.createTechnicalIndicator(n,e,i):null}},{key:"addTechnicalIndicatorTemplate",value:function(t){if(k(t)){var e=[].concat(t);this._chartPane.chartStore().technicalIndicatorStore().addTemplate(e)}}},{key:"overrideTechnicalIndicator",value:function(t,e){var i=this;if(k(t)&&!g(t)){var n=this._chartPane.chartStore().technicalIndicatorStore().override(t,e);n&&n.then(function(t){i._chartPane.adjustPaneViewport(!1,!0,!0,!0)})}}},{key:"getTechnicalIndicatorTemplate",value:function(t){return this._chartPane.chartStore().technicalIndicatorStore().getTemplateInfo(t)}},{key:"getTechnicalIndicatorByPaneId",value:function(t,e){return this._chartPane.chartStore().technicalIndicatorStore().getInstanceInfo(t,e)}},{key:"removeTechnicalIndicator",value:function(t,e){this._chartPane.removeTechnicalIndicator(t,e)}},{key:"addShapeTemplate",value:function(t){if(k(t)){var e=[].concat(t);this._chartPane.chartStore().shapeStore().addTemplate(e)}}},{key:"createShape",value:function(t,e){if(!b(t))return null;var i=k(t)&&!g(t)?t:{name:t},n=this._chartPane.chartStore().shapeStore().getTemplate(i.name);return n?this._chartPane.createShape(n,i,e):null}},{key:"getShape",value:function(t){return this._chartPane.chartStore().shapeStore().getInstanceInfo(t)}},{key:"setShapeOptions",value:function(t){k(t)&&!g(t)&&this._chartPane.chartStore().shapeStore().setInstanceOptions(t)}},{key:"removeShape",value:function(t){this._chartPane.chartStore().shapeStore().removeInstance(t)}},{key:"createAnnotation",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:xi;if(k(t)&&this._chartPane.hasPane(e)){var i=[].concat(t);this._chartPane.createAnnotation(i,e)}}},{key:"removeAnnotation",value:function(t,e){this._chartPane.chartStore().annotationStore().remove(t,e)}},{key:"createTag",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:xi;if(k(t)&&this._chartPane.hasPane(e)){var i=[].concat(t);this._chartPane.createTag(i,e)}}},{key:"removeTag",value:function(t,e){this._chartPane.chartStore().tagStore().remove(t,e)}},{key:"createHtml",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:xi;if(!k(t))return null;if(!(P(t.content)||t.content instanceof HTMLElement))return null;var i=this._chartPane.getPane(e);return i?i.createHtml(t):null}},{key:"removeHtml",value:function(t,e){if(t){var i=this._chartPane.getPane(t);i&&i.removeHtml(e)}else this._chartPane.removeAllHtml()}},{key:"setPaneOptions",value:function(t){k(t)&&this._chartPane.setPaneOptions(t,!1)}},{key:"setZoomEnabled",value:function(t){this._chartPane.chartStore().timeScaleStore().setZoomEnabled(t)}},{key:"isZoomEnabled",value:function(){return this._chartPane.chartStore().timeScaleStore().zoomEnabled()}},{key:"setScrollEnabled",value:function(t){this._chartPane.chartStore().timeScaleStore().setScrollEnabled(t)}},{key:"isScrollEnabled",value:function(){return this._chartPane.chartStore().timeScaleStore().scrollEnabled()}},{key:"scrollByDistance",value:function(t,e){var i=this;if(w(t))if(w(e)&&e>0){this._chartPane.chartStore().timeScaleStore().startScroll();var n=(new Date).getTime();!function r(){var a=((new Date).getTime()-n)/e,o=a>=1,s=o?t:t*a;i._chartPane.chartStore().timeScaleStore().scroll(s),o||ye(r)}()}else this._chartPane.chartStore().timeScaleStore().startScroll(),this._chartPane.chartStore().timeScaleStore().scroll(t)}},{key:"scrollToRealTime",value:function(t){var e=this._chartPane.chartStore().timeScaleStore().dataSpace(),i=this._chartPane.chartStore().timeScaleStore().offsetRightBarCount()-this._chartPane.chartStore().timeScaleStore().offsetRightSpace()/e;this.scrollByDistance(i*e,t)}},{key:"scrollToDataIndex",value:function(t,e){if(w(t)){var i=(this._chartPane.chartStore().timeScaleStore().offsetRightBarCount()+(this.getDataList().length-1-t))*this._chartPane.chartStore().timeScaleStore().dataSpace();this.scrollByDistance(i,e)}}},{key:"scrollToTimestamp",value:function(t,e){if(w(t)){var i=tt(this.getDataList(),"timestamp",t);this.scrollToDataIndex(i,e)}}},{key:"zoomAtCoordinate",value:function(t,e,i){var n=this;if(w(t))if(w(i)&&i>0){var r=this._chartPane.chartStore().timeScaleStore().dataSpace(),a=r*t-r,o=(new Date).getTime();!function t(){var s=((new Date).getTime()-o)/i,c=s>=1,h=c?a:a*s;n._chartPane.chartStore().timeScaleStore().zoom(h/r,e),c||ye(t)}()}else this._chartPane.chartStore().timeScaleStore().zoom(t,e)}},{key:"zoomAtDataIndex",value:function(t,e,i){if(w(t)&&w(e)){var n=this._chartPane.chartStore().timeScaleStore().dataIndexToCoordinate(e);this.zoomAtCoordinate(t,{x:n},i)}}},{key:"zoomAtTimestamp",value:function(t,e,i){if(w(t)&&w(e)){var n=tt(this.getDataList(),"timestamp",e);this.zoomAtDataIndex(t,n,i)}}},{key:"convertToPixel",value:function(t,e){return this._chartPane.convertToPixel(t,e)}},{key:"convertFromPixel",value:function(t,e){return this._chartPane.convertFromPixel(t,e)}},{key:"subscribeAction",value:function(t,e){at(t)&&S(e)&&this._chartPane.chartStore().actionStore().subscribe(t,e)}},{key:"unsubscribeAction",value:function(t,e){at(t)&&this._chartPane.chartStore().actionStore().unsubscribe(t,e)}},{key:"getConvertPictureUrl",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"jpeg",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"#FFFFFF";if("png"===e||"jpeg"===e||"bmp"===e)return this._chartPane.getConvertPictureUrl(t,e,i)}},{key:"destroy",value:function(){this._chartPane.destroy()}}]),t}(),ki={},wi=1,bi={version:function(){return"8.6.3"},init:function(t){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t)return null;if(!(e=P(t)?document.getElementById(t):t))return null;var n=ki[e.chartId||""];if(n)return n;var r="".concat("k_line_chart_").concat(wi++);return(n=new Si(e,i)).id=r,e.chartId=r,ki[r]=n,n},dispose:function(t){if(t){var e;if(P(t)){var i=document.getElementById(t);e=i&&i.chartId}else e=t instanceof Si?t.id:t&&t.chartId;e&&(ki[e].destroy(),delete ki[e])}},utils:{clone:x,merge:y,isString:P,isNumber:w,isValid:b,isObject:k,isArray:g,isFunction:S,isBoolean:E,formatValue:K,formatPrecision:J,formatBigNumber:Q},extension:d};return bi.extension.addTechnicalIndicatorTemplate([{name:"AVP",shortName:"AVP",series:"price",precision:2,plots:[{key:"avp",title:"AVP: ",type:"line"}],calcTechnicalIndicator:function(t){var e=0,i=0;return t.map(function(t){var n={};return e+=t.turnover||0,0!==(i+=t.volume||0)&&(n.avp=e/i),n})}},t,e,i,{name:"EMV",shortName:"EMV",calcParams:[14,9],plots:[{key:"emv",title:"EMV: ",type:"line"},{key:"maEmv",title:"MAEMV: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=[],o=[];return t.forEach(function(e,s){var c={},h=t[s-1]||e,l=e.high,u=e.low,f=e.turnover||0,d=0;0!==f&&(d=((l+u)/2-(h.high+h.low)/2)*(l-u)/f),a.push(d),n+=d,i[0]-1>s||(c.emv=n,n-=a[s-(i[0]-1)],r+=c.emv,i[0]+i[1]-2>s||(c.maEmv=r/i[1],r-=o[s-(i[1]-1)].emv)),o.push(c)}),o}},{name:"EMA",shortName:"EMA",series:"price",calcParams:[6,12,20],precision:2,shouldCheckParamCount:!1,shouldOhlc:!0,plots:[{key:"ema6",title:"EMA6: ",type:"line"},{key:"ema12",title:"EMA12: ",type:"line"},{key:"ema20",title:"EMA20: ",type:"line"}],regeneratePlots:function(t){return t.map(function(t){return{key:"ema".concat(t),title:"EMA".concat(t,": "),type:"line"}})},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots,r=0,a=[];return t.map(function(t,e){var o={},s=t.close;return r+=s,i.forEach(function(t,i){t-1>e||(a[i]=e>t-1?(2*s+(t-1)*a[i])/(t+1):r/t,o[n[i].key]=a[i])}),o})}},{name:"MA",shortName:"MA",series:"price",calcParams:[5,10,30,60],precision:2,shouldCheckParamCount:!1,shouldOhlc:!0,plots:[{key:"ma5",title:"MA5: ",type:"line"},{key:"ma10",title:"MA10: ",type:"line"},{key:"ma30",title:"MA30: ",type:"line"},{key:"ma60",title:"MA60: ",type:"line"}],regeneratePlots:function(t){return t.map(function(t){return{key:"ma".concat(t),title:"MA".concat(t,": "),type:"line"}})},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots,r=[];return t.map(function(e,a){var o={},s=e.close;return i.forEach(function(e,i){r[i]=(r[i]||0)+s,e-1>a||(o[n[i].key]=r[i]/e,r[i]-=t[a-(e-1)].close)}),o})}},n,{name:"SMA",shortName:"SMA",series:"price",calcParams:[12,2],precision:2,plots:[{key:"sma",title:"SMA: ",type:"line"}],shouldCheckParamCount:!0,shouldOhlc:!0,calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0;return t.map(function(t,e){var a={},o=t.close;return n+=o,i[0]-1>e||(a.sma=r=e>i[0]-1?(o*i[1]+r*(i[0]-i[1]+1))/(i[0]+1):n/i[0]),a})}},{name:"TRIX",shortName:"TRIX",calcParams:[12,9],plots:[{key:"trix",title:"TRIX: ",type:"line"},{key:"maTrix",title:"MATRIX: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i,n,r,a=e.params,o=0,s=0,c=0,h=0,l=[];return t.forEach(function(t,e){var u={},f=t.close;if(o+=f,e>=a[0]-1&&(s+=i=e>a[0]-1?(2*f+(a[0]-1)*i)/(a[0]+1):o/a[0],e>=2*a[0]-2&&(c+=n=e>2*a[0]-2?(2*i+(a[0]-1)*n)/(a[0]+1):s/a[0],e>=3*a[0]-3))){var d,v=0;e>3*a[0]-3?v=((d=(2*n+(a[0]-1)*r)/(a[0]+1))-r)/r*100:d=c/a[0],r=d,u.trix=v,h+=v,3*a[0]+a[1]-4>e||(u.maTrix=h/a[1],h-=l[e-(a[1]-1)].trix)}l.push(u)}),l}},{name:"BRAR",shortName:"BRAR",calcParams:[26],plots:[{key:"br",title:"BR: ",type:"line"},{key:"ar",title:"AR: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=0,o=0;return t.map(function(e,s){var c={},h=e.high,l=e.low,u=e.open,f=(t[s-1]||e).close;if(a+=h-u,o+=u-l,n+=h-f,r+=f-l,s>=i[0]-1){c.ar=0!==o?a/o*100:0,c.br=0!==r?n/r*100:0;var d=t[s-(i[0]-1)],v=d.high,p=d.low,_=d.open,m=(t[s-i[0]]||t[s-(i[0]-1)]).close;n-=v-m,r-=m-p,a-=v-_,o-=_-p}return c})}},r,{name:"MTM",shortName:"MTM",calcParams:[12,6],plots:[{key:"mtm",title:"MTM: ",type:"line"},{key:"maMtm",title:"MAMTM: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=[];return t.forEach(function(e,a){var o={};a<i[0]||(o.mtm=e.close-t[a-i[0]].close,n+=o.mtm,i[0]+i[1]-1>a||(o.maMtm=n/i[1],n-=r[a-(i[1]-1)].mtm)),r.push(o)}),r}},{name:"PSY",shortName:"PSY",calcParams:[12,6],plots:[{key:"psy",title:"PSY: ",type:"line"},{key:"maPsy",title:"MAPSY: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=[],o=[];return t.forEach(function(e,s){var c={},h=e.close-(t[s-1]||e).close>0?1:0;a.push(h),n+=h,i[0]-1>s||(c.psy=n/i[0]*100,r+=c.psy,i[0]+i[1]-2>s||(c.maPsy=r/i[1],r-=o[s-(i[1]-1)].psy),n-=a[s-(i[0]-1)]),o.push(c)}),o}},{name:"ROC",shortName:"ROC",calcParams:[12,6],shouldCheckParamCount:!0,plots:[{key:"roc",title:"ROC: ",type:"line"},{key:"maRoc",title:"MAROC: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=[],r=0;return t.forEach(function(e,a){var o={};if(a>=i[0]-1){var s=(t[a-i[0]]||t[a-(i[0]-1)]).close;o.roc=0!==s?(e.close-s)/s*100:0,r+=o.roc,i[0]-1+i[1]-1>a||(o.maRoc=r/i[1],r-=n[a-(i[1]-1)].roc)}n.push(o)}),n}},{name:"VR",shortName:"VR",calcParams:[26,6],plots:[{key:"vr",title:"VR: ",type:"line"},{key:"maVr",title:"MAVR: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=0,o=0,s=[];return t.forEach(function(e,c){var h={},l=e.close,u=(t[c-1]||e).close,f=e.volume;if(l>u?n+=f:u>l?r+=f:a+=f,c>=i[0]-1){var d=a/2;h.vr=r+d===0?0:(n+d)/(r+d)*100,o+=h.vr,i[0]+i[1]-2>c||(h.maVr=o/i[1],o-=s[c-(i[1]-1)].vr);var v=t[c-(i[0]-1)],p=t[c-i[0]]||v,_=v.close,m=v.volume;_>p.close?n-=m:p.close>_?r-=m:a-=m}s.push(h)}),s}},a,{name:"BIAS",shortName:"BIAS",calcParams:[6,12,24],shouldCheckParamCount:!1,plots:[{key:"bias6",title:"BIAS6: ",type:"line"},{key:"bias12",title:"BIAS12: ",type:"line"},{key:"bias24",title:"BIAS24: ",type:"line"}],regeneratePlots:function(t){return t.map(function(t){return{key:"bias".concat(t),title:"BIAS".concat(t,": "),type:"line"}})},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots,r=[];return t.map(function(e,a){var o={},s=e.close;return i.forEach(function(e,c){if(r[c]=(r[c]||0)+s,a>=e-1){var h=r[c]/i[c];o[n[c].key]=(s-h)/h*100,r[c]-=t[a-(e-1)].close}}),o})}},o,s,h,l,u,f,{name:"OBV",shortName:"OBV",calcParams:[30],plots:[{key:"obv",title:"OBV: ",type:"line"},{key:"maObv",title:"MAOBV: ",type:"line"}],calcTechnicalIndicator:function(t,e){var i=e.params,n=0,r=0,a=[];return t.forEach(function(e,o){var s=t[o-1]||e;s.close>e.close?r-=e.volume:e.close>s.close&&(r+=e.volume);var c={obv:r};n+=r,i[0]-1>o||(c.maObv=n/i[0],n-=a[o-(i[0]-1)].obv),a.push(c)}),a}},{name:"PVT",shortName:"PVT",plots:[{key:"pvt",title:"PVT: ",type:"line"}],calcTechnicalIndicator:function(t){var e=0;return t.map(function(i,n){var r={},a=(t[n-1]||i).close,o=0;return 0!==a&&(o=(i.close-a)/a*i.volume),r.pvt=e+=o,r})}},{name:"VOL",shortName:"VOL",series:"volume",calcParams:[5,10,20],shouldCheckParamCount:!1,shouldFormatBigNumber:!0,precision:0,minValue:0,plots:[{key:"ma5",title:"MA5: ",type:"line"},{key:"ma10",title:"MA10: ",type:"line"},{key:"ma20",title:"MA20: ",type:"line"},{key:"volume",title:"VOLUME: ",type:"bar",baseValue:0,color:function(t,e){var i=t.current.kLineData||{};return i.close>i.open?e.bar.upColor:i.open>i.close?e.bar.downColor:e.bar.noChangeColor}}],regeneratePlots:function(t){var e=t.map(function(t){return{key:"ma".concat(t),title:"MA".concat(t,": "),type:"line"}});return e.push({key:"volume",title:"VOLUME: ",type:"bar",baseValue:0,color:function(t,e){var i=t.current.kLineData||{};return i.close>i.open?e.bar.upColor:i.open>i.close?e.bar.downColor:e.bar.noChangeColor}}),e},calcTechnicalIndicator:function(t,e){var i=e.params,n=e.plots,r=[];return t.map(function(e,a){var o=e.volume||0,s={volume:o};return i.forEach(function(e,i){r[i]=(r[i]||0)+o,e-1>a||(s[n[i].key]=r[i]/e,r[i]-=t[a-(e-1)].volume)}),s})}}]),bi.extension.addShapeTemplate([{name:"horizontalRayLine",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Dt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i={x:0,y:e[0].y};return e[1]&&e[1].x>e[0].x&&(i.x=t.viewport.width),[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[e[0],i]]}]},performEventPressedMove:function(t){var e=t.points,i=t.pressPoint;e[0].value=i.value,e[1].value=i.value},performEventMoveForDrawing:function(t){2===t.step&&(t.points[0].value=t.movePoint.value)}},{name:"horizontalSegment",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Ot(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i=[];return 2===e.length&&(i=[e]),[{type:"line",isDraw:!0,isCheck:!0,dataSource:i}]},performEventPressedMove:function(t){var e=t.points,i=t.pressPoint;e[0].value=i.value,e[1].value=i.value},performEventMoveForDrawing:function(t){2===t.step&&(t.points[0].value=t.movePoint.value)}},{name:"horizontalStraightLine",totalStep:2,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Mt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[{x:0,y:e[0].y},{x:t.viewport.width,y:e[0].y}]]}]}},{name:"verticalRayLine",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Dt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i={x:e[0].x,y:0};return e[1]&&e[1].y>e[0].y&&(i.y=t.viewport.height),[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[e[0],i]]}]},performEventPressedMove:function(t){var e=t.points,i=t.pressPoint;e[0].timestamp=i.timestamp,e[0].dataIndex=i.dataIndex,e[1].timestamp=i.timestamp,e[1].dataIndex=i.dataIndex},performEventMoveForDrawing:function(t){var e=t.points,i=t.movePoint;2===t.step&&(e[0].timestamp=i.timestamp,e[0].dataIndex=i.dataIndex)}},{name:"verticalSegment",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Ot(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i=[];return 2===e.length&&(i=[e]),[{type:"line",isDraw:!0,isCheck:!0,dataSource:i}]},performEventPressedMove:function(t){var e=t.points,i=t.pressPoint;e[0].timestamp=i.timestamp,e[0].dataIndex=i.dataIndex,e[1].timestamp=i.timestamp,e[1].dataIndex=i.dataIndex},performEventMoveForDrawing:function(t){var e=t.points,i=t.movePoint;2===t.step&&(e[0].timestamp=i.timestamp,e[0].dataIndex=i.dataIndex)}},{name:"verticalStraightLine",totalStep:2,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Mt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[{x:e[0].x,y:0},{x:e[0].x,y:t.viewport.height}]]}]}},{name:"rayLine",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Dt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e,i,n,r=t.coordinates,a=t.viewport;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:[(e=r[0],i=r[1],n={x:a.width,y:a.height},e&&i?[e,e.x===i.x&&e.y!==i.y?i.y>e.y?{x:e.x,y:n.y}:{x:e.x,y:0}:e.x>i.x?{x:0,y:It(e,i,{x:0,y:e.y})}:{x:n.x,y:It(e,i,{x:n.x,y:e.y})}]:[])]}]}},{name:"segment",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Ot(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i=[];return 2===e.length&&(i=[e]),[{type:"line",isDraw:!0,isCheck:!0,dataSource:i}]}},{name:"straightLine",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Mt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates,i=t.viewport;return 2>e.length||e[0].x===e[1].x?[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[{x:e[0].x,y:0},{x:e[0].x,y:i.height}]]}]:[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[{x:0,y:It(e[0],e[1],{x:0,y:e[0].y})},{x:i.width,y:It(e[0],e[1],{x:i.width,y:e[0].y})}]]}]}},{name:"parallelStraightLine",totalStep:4,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Mt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.viewport;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:Rt(t.coordinates,{x:e.width,y:e.height})}]}},{name:"priceChannelLine",totalStep:4,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Mt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.viewport;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:Rt(t.coordinates,{x:e.width,y:e.height},1)}]}},{name:"priceLine",totalStep:2,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Dt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.coordinates;return[{type:"line",isDraw:!0,isCheck:!0,dataSource:[[e[0],{x:t.viewport.width,y:e[0].y}]]},{type:"text",isDraw:!0,isCheck:!1,dataSource:[{x:e[0].x,y:e[0].y,text:t.points[0].value.toFixed(t.precision.price)}]}]}},{name:"fibonacciLine",totalStep:3,checkEventCoordinateOnShape:function(t){var e=t.dataSource;return Mt(e[0],e[1],t.eventCoordinate)},createShapeDataSource:function(t){var e=t.points,i=t.coordinates,n=t.precision;if(i.length>0){var r=[],a=[],o=t.viewport.width;if(i.length>1){var s=i[0].y-i[1].y,c=e[0].value-e[1].value;[1,.786,.618,.5,.382,.236,0].forEach(function(t){var h=i[1].y+s*t,l=(e[1].value+c*t).toFixed(n.price);r.push([{x:0,y:h},{x:o,y:h}]),a.push({x:0,y:h,text:"".concat(l," (").concat((100*t).toFixed(1),"%)")})})}return[{type:"line",isDraw:!0,isCheck:!0,dataSource:r},{type:"text",isDraw:!0,isCheck:!1,dataSource:a}]}return[]}}]),bi},t.exports=n()},"7w8M":function(t,e,i){t.exports=i.p+"static/img/recharge.64ff8ce.png"},AB9B:function(t,e,i){t.exports=i.p+"static/img/4.eb3ee29.png"},GjJs:function(t,e,i){t.exports=i.p+"static/img/1.2790038.png"},KE1O:function(t,e,i){"use strict";t.exports=i("1zby")},VkkZ:function(t,e,i){t.exports=i.p+"static/img/new_shares.3653a14.png"},WjZl:function(t,e,i){t.exports=i.p+"static/img/news_title.cb1b453.png"},X6d5:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=i("Xxa5"),r=i.n(n),a=i("exGp"),o=i.n(a),s=i("c2Ch"),c=i("KE1O"),h={components:{},props:{},data:function(){return{isShow:!1,tabactive:1,newsContent1:[],noticebar:"",hushentiao:"",isshow:!0,hotStockList:[]}},mounted:function(){this.getUserInfo(),this.getNewsList(1),this.stockgetZdfNumber(),this.getListMarket()},destroyed:function(){Object(c.dispose)("chart-type-k-line"),this.isshow=!1},methods:{useFormatMoney:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"CNY",n={minimumFractionDigits:2,maximumFractionDigits:6};e&&(n.style="currency",n.currency=i);var r=Number(t||0);if(isNaN(r))throw new Error("Invalid input: price must be a number---\x3e"+t);return r.toLocaleString(void 0,n)},getListMarket:function(){var t=this;s.L({pageNum:1,pageSize:15}).then(function(e){for(var i=0;i<3;i++)t.hotStockList.push(e.data[i])})},getkline:function(){arguments.length>0&&void 0!==arguments[0]?arguments[0]:Date.now();var t=this;arguments.length>1&&void 0!==arguments[1]&&arguments[1],arguments.length>2&&void 0!==arguments[2]&&arguments[2];return o()(r.a.mark(function e(){var i,n,a,o,c,h,l;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i={code:"000001",time:5,ma:5,size:100},e.next=3,s.N(i);case 3:for(n=e.sent,a=n.data.values.reverse(),o=[],c=0;c<a.length;c++)h={open:a[c][0],low:a[c][2],high:a[c][3],close:a[c][1],volume:a[c][4],timestamp:a[c][5]},o.unshift(h);l=t,setTimeout(function(){t.kLineChart.applyNewData(o)},500),t.isshow&&setTimeout(function(){l.getkline()},3e3);case 10:case"end":return e.stop()}},e,t)}))()},getUserInfo:function(){var t=this;return o()(r.a.mark(function e(){var i;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,s._7();case 2:0===(i=e.sent).status?(t.$store.commit("dialogVisible",!1),t.$store.state.userInfo=i.data,t.userInfo=i.data,t.rate=i.data.enableAmt/i.data.userAmt*100,1!==i.data.isActive&&2!==i.data.isActive||(t.showBtn=!1)):t.$store.commit("dialogVisible",!0);case 4:case"end":return e.stop()}},e,t)}))()},getsearch:function(){this.$router.push({path:"/Searchlist"})},stockgetZdfNumber:function(){var t=this;return o()(r.a.mark(function e(){var i;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,s._35();case 2:i=e.sent,t.hushentiao=i.data;case 4:case"end":return e.stop()}},e,t)}))()},getNewsList:function(t){var e=this;return o()(r.a.mark(function i(){var n;return r.a.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return e.tabactive=t,e.newsContent1=[],i.next=4,s._25(t);case 4:n=i.sent,e.newsContent1=n.data.list,e.noticebar=n.data.list[0].title;case 7:case"end":return i.stop()}},i,e)}))()},getnewsdetail:function(t){this.$router.push({path:"/newPage",query:{listid:t.id}})},getHeaderlink:function(t){0==t?this.$router.push({path:"/MyList"}):1==t?this.$router.push({path:"/trading-list"}):2==t?this.$router.push({path:"/about?e=6"}):3==t?this.$router.push({path:"/about?e=6"}):4===t?this.$router.push({path:"/service"}):5==t&&this.$router.push({path:"/openaccount"})},getHeaderlink1:function(t){switch(t){case 1:this.$router.push({path:"/Subscription?idx=1"});break;case 2:this.$router.push({path:"/Subscription?idx=3"});break;case 3:this.$router.push({path:"/Subscription?idx=4"});break;case 4:this.$router.push({path:"/Subscription?idx=5"});break;case 5:this.$router.push({path:"/Subscription?idx=2"});break;case 6:this.$router.push({path:"/DragonTiger"});break;case 7:this.$router.push({path:"/stopRecovery"});break;case 8:this.$router.push({path:"/topTen"});break;case 9:this.$router.push({path:"/daylimit"});break;case 10:this.$router.push("/about?e=5")}},getInfoSite:function(){var t=this;return o()(r.a.mark(function e(){var i;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,s.J();case 2:0===(i=e.sent).status?t.onlineService=i.data.onlineService:t.$store.commit("elAlertShow",{elAlertShow:!0,elAlertText:i.msg});case 4:case"end":return e.stop()}},e,t)}))()}}},l={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"container"},[n("div",{staticClass:"top"},[n("div",{staticClass:"header"},[n("img",{staticStyle:{height:"0.8139rem"},attrs:{src:i("w2f/")}}),t._v(" "),n("img",{staticStyle:{height:"0.8139rem","margin-left":"0.3488rem"},attrs:{src:i("b/4H")},on:{click:function(e){return t.$router.push("/speedtest")}}})]),t._v(" "),n("div",{staticClass:"search",on:{click:function(e){return t.getsearch()}}},[n("div",{staticClass:"search_icon"},[n("svg",{staticClass:"icon",attrs:{t:"1742902499156",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"4638",width:"64",height:"64"}},[n("path",{attrs:{d:"M653.54 707.45C498.1 833.17 270.18 809.07 144.46 653.63 18.84 498.32 42.96 270.17 198.28 144.55 353.72 18.84 581.64 42.93 707.36 198.37c107.37 132.76 107.37 322.5 0 455.26l241.48 241.35c14.87 14.86 14.88 38.96 0.03 53.84-14.87 14.89-38.99 14.9-53.87 0.02L653.54 707.45zM425.8 139.62c-157.93 0-285.96 128.03-285.96 285.96 0 157.93 128.03 285.96 285.96 285.96 157.84 0 285.84-127.89 285.96-285.74-0.08-157.92-128.02-285.97-285.96-286.18z","p-id":"4639"}})])]),t._v(" "),n("span",[t._v("请输入股票代码/简拼")])]),t._v(" "),n("div",{staticClass:"banner"},[n("van-swipe",{attrs:{autoplay:3e3,"indicator-color":"white"}},[n("van-swipe-item",[n("img",{attrs:{src:i("GjJs")}})]),t._v(" "),n("van-swipe-item",[n("img",{attrs:{src:i("+yag")}})]),t._v(" "),n("van-swipe-item",[n("img",{attrs:{src:i("ahWu")}})]),t._v(" "),n("van-swipe-item",[n("img",{attrs:{src:i("AB9B")}})]),t._v(" "),n("van-swipe-item",[n("img",{attrs:{src:i("d4Xz")}})]),t._v(" "),n("van-swipe-item",[n("img",{attrs:{src:i("srJx")}})])],1)],1),t._v(" "),n("div",{staticClass:"menu"},[n("div",{staticClass:"home-item",on:{click:function(e){return t.$router.push({path:"/newshares"})}}},[t._m(0),t._v(" "),t._m(1)]),t._v(" "),n("div",{staticClass:"home-item",on:{click:function(e){return t.$router.push({path:"/peishouhistory?type=1"})}}},[t._m(2),t._v(" "),t._m(3)]),t._v(" "),n("div",{staticClass:"home-item",on:{click:function(e){return t.$router.push({path:"/biglist"})}}},[t._m(4),t._v(" "),t._m(5)]),t._v(" "),n("div",{staticClass:"home-item",on:{click:function(e){return t.$router.push({path:"/recharge"})}}},[t._m(6),t._v(" "),t._m(7)]),t._v(" "),n("div",{staticClass:"home-item",on:{click:function(e){return t.$router.push({path:"/service"})}}},[t._m(8),t._v(" "),t._m(9)])])]),t._v(" "),n("div",{staticClass:"data"},[t._m(10),t._v(" "),n("div",{staticClass:"data_layout"},[n("div",{staticClass:"data_item"},[n("div",{staticClass:"data_item_title"},[t._v("总资产")]),t._v(" "),n("div",{staticClass:"data_item_money"},[t._v(t._s(t.useFormatMoney(t.$store.state.userInfo.userAmt||0)))])]),t._v(" "),n("div",{staticClass:"line"}),t._v(" "),n("div",{staticClass:"data_item"},[n("div",{staticClass:"data_item_title"},[t._v("可用资金")]),t._v(" "),n("div",{staticClass:"data_item_money"},[t._v(t._s(t.useFormatMoney(t.$store.state.userInfo.enableAmt||0)))])])])]),t._v(" "),n("div",{staticClass:"stock"},[t._m(11),t._v(" "),n("div",{staticClass:"stock_swiper"},t._l(t.hotStockList,function(e){return n("div",{key:e.id,staticClass:"index"},[n("div",{staticClass:"index_title"},[t._v(t._s(e.indexName))]),t._v(" "),n("div",{class:"index_num "+(e.floatRate>0?"red":"green")},[t._v(t._s(e.currentPoint))]),t._v(" "),n("div",{class:"index_text "+(e.floatRate>0?"red":"green")},[n("span",[t._v(t._s(Number(e.floatPoint).toFixed(2)))]),t._v(" "),n("span",[t._v(t._s(e.floatRate)+"%")])])])}),0)]),t._v(" "),n("div",{staticClass:"news_layout"},[t._m(12),t._v(" "),n("div",{staticClass:"news_menu"},[n("div",{class:"news_menu_item "+(1==t.tabactive?"active":""),on:{click:function(e){return t.getNewsList(1)}}},[t._v("财经要闻")]),t._v(" "),n("div",{class:"news_menu_item "+(2==t.tabactive?"active":""),on:{click:function(e){return t.getNewsList(2)}}},[t._v("经济数据")]),t._v(" "),n("div",{class:"news_menu_item "+(3==t.tabactive?"active":""),on:{click:function(e){return t.getNewsList(3)}}},[t._v("7*24全球")]),t._v(" "),n("div",{class:"news_menu_item "+(4==t.tabactive?"active":""),on:{click:function(e){return t.getNewsList(4)}}},[t._v("国际经济")])]),t._v(" "),n("div",{staticClass:"news"},t._l(t.newsContent1,function(e){return n("div",{key:e.id,staticClass:"news_item",on:{click:function(i){return t.getnewsdetail(e)}}},[n("div",{staticClass:"news_title"},[t._v(t._s(e.title))])])}),0)])])},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"item-img"},[e("img",{attrs:{src:i("VkkZ"),alt:""}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"item-name"},[e("span",[this._v("新股申购")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"item-img"},[e("img",{attrs:{src:i("u+si"),alt:""}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"item-name"},[e("span",[this._v("我的新股")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"item-img"},[e("img",{attrs:{src:i("gM++"),alt:""}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"item-name"},[e("span",[this._v("天启护盘")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"item-img"},[e("img",{attrs:{src:i("7w8M"),alt:""}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"item-name"},[e("span",[this._v("银证转账")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"item-img"},[e("img",{attrs:{src:i("mT7N"),alt:""}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"item-name"},[e("span",[this._v("客服")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"data_title"},[e("img",{attrs:{src:i("bMaa")}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"stock_title"},[e("img",{attrs:{src:i("bv2P")}})])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"news_title"},[e("img",{attrs:{src:i("WjZl")}})])}]};var u=i("VU/8")(h,l,!1,function(t){i("+0UJ")},"data-v-44b9eac0",null);e.default=u.exports},ahWu:function(t,e,i){t.exports=i.p+"static/img/3.b6627bf.png"},"b/4H":function(t,e,i){t.exports=i.p+"static/img/58.42ccc4d.png"},bMaa:function(t,e,i){t.exports=i.p+"static/img/data_title.93b62df.png"},bv2P:function(t,e,i){t.exports=i.p+"static/img/stock_title.ef59b3c.png"},d4Xz:function(t,e,i){t.exports=i.p+"static/img/5.c6a8b23.png"},"gM++":function(t,e,i){t.exports=i.p+"static/img/dazong.aa33ae3.png"},mT7N:function(t,e,i){t.exports=i.p+"static/img/kefu.126e3de.png"},srJx:function(t,e,i){t.exports=i.p+"static/img/6.85cef1e.png"},"u+si":function(t,e,i){t.exports=i.p+"static/img/my_new.c81d6da.png"},"w2f/":function(t,e,i){t.exports=i.p+"static/img/logo.605356b.jpg"}});